# ECONN v2.3.3

2021.1.1: 现已支持多任务训练

2020.9.18: 现已支持pytorch1.6 cuda10.2, 支持分布式训练, 资源利用率显著提高！

## ECONN详解文档

[documents](documents/documents.pdf)

## Introduction

完全重构的训练框架，参考了CenterNet、MMDetection、Detectron2等框架，与CenterNet训练框架相比更加模块化，
与MMDetection相比更灵活。

![demo image](documents/demo2.png)
### features
- **模块化设计**
- **支持多种任务：物体检测、实例分割、人体姿态检测**
- **友好的接口**
- **灵活的配置文件**
- **支持分布式训练**
- **支持多任务训练**
- **支持冻结参数训练，实现多头检测**

![arch image](documents/arch.png)
   
## Models
### Architecture：
- **CenterNet** : [Objects as Points[arXiv 1904.07850]]( http://arxiv.org/abs/1904.07850 )
- **FCOS** : [FCOS: Fully Convolutional One-Stage Object Detection[arXiv 1904.01355]]( https://arxiv.org/abs/1904.01355)
- **GFL** : [Generalized Focal Loss: Learning Qualified and Distributed Bounding Boxes for Dense Object Detection[arXiv 2006.04388]]( https://arxiv.org/abs/2006.04388)
- **GFocalV2** : [Generalized Focal Loss V2: Learning Reliable Localization Quality Estimation for Dense Object Detection](https://arxiv.org/pdf/2011.12885.pdf)
- **CenterNet Based FCOS** 
- **CenterNet Based CondInst** 
- **PolarMask(support soon)** 
- **YoloV4-Tiny**
- **Varifocal Loss** (自己实现的简化版的VFL，去除了不能用来部署的Star Conv分支)
- **OneNet** (基于CenterNet实现)
- **YOLACT**
- **BoxInst** (基于OneNet+YOLACT实现)
- **YOLOF** : [You Only Look One-level Feature[arXiv 2103.09460]]( http://arxiv.org/abs/2103.09460 )
- **Multi_head** (基于GFL检测头实现)

### Backbones:
- **ResNet**
- **MobileNetV2**
- **ShuffleNetV2**
- **GhostNet**
- **VoVNet**
- **MobileNeXt(non-official)**
- **VarGNetV2**
- **EfficientNet**
### Necks
- **FPN**
- **PAN**
- **Dilated Encoder(YOLOF)**
- **DeConv upsample for CenterNet**
- **DeConv upsample with shortcut for CenterNet**

## Install
```bash
conda install pytorch=1.6 torchvision cudatoolkit=10.2 -c pytorch
pip install -r requirements.txt
python setup.py develop
```

## Usage 使用说明
### 1.单机单卡训练
直接运行train.py 并指明配置文件即可开始训练
```bash
python train.py YOUR_CONFIG_PATH
```

### 2. 单机多卡训练【注意！！！！】
自更新至1.0.0版本起，多卡训练方式修改为了分布式单机多卡，如要使用旧版的单进程单机多卡，请切换至git的v0.1.10
版本。


对于分布式单机多卡，每一张GPU由1个进程负责。因此在config中，learning rate的设置不应该按照总batch size的
大小进行线性法则的增长，而是应该按照总batch size除以进程数得到单个进程的batch size，按这个batch size修改
learning rate。

例：单卡batch_size=4, lr=0.001; 4GPU batch_size=16, lr=0.001; 4GPU batch_size=32, lr=0.002

如何运行分布式训练：
```bash
cd YOUR_ECONN_PATH
export PYTHONPATH=$PYTHONPATH:`pwd`
python -m torch.distributed.launch --nproc_per_node=YOUR_GPU_NUM --master_port 29501 tools/train.py YOUR_CONFIG_PATH
```
在服务器上跑请将以上代码写成shell脚本执行,参考run_distributed_training.sh

### 3. 多任务训练【注意！！！！】

目前ECONN已支持在单GPU上进行跨数据集多任务联合训练（暂未支持多卡），训练配置文件编写规则参考configs/Multi_
Task里的例子，使用tools/train_multi_task.py训练。

```bash
python train_multi_task.py YOUR_CONFIG_PATH
```

## Update Logs

| 日期         | 功能                                                               | 版本           |
|------------|------------------------------------------------------------------|--------------|
| 2020.3.26  | 完整跑通Resnet-CenterNet的Train（单机单卡）                                 | v0.0.1       |
| 2020.3.30  | 完整跑通Resnet-CenterNet单机多卡分布式训练(效果未验证)                             | v0.0.2       |
| 2020.3.31  | 支持加载模型，支持恢复训练                                                    | v0.0.3       |
| 2020.3.31  | 支持多种优化器设置：SGD(momentum)、Adam、AMSGrad                             | v0.0.3       |
| 2020.4.2   | 完成了evaluate，                                                     | v0.0.4       |
| 2020.4.3   | 加入了tensorboard logger，同时按step记录与按epoch记录，                        | v0.0.5       |
| 2020.4.7   | 支持warmup                                                         | v0.0.6       |
| 2020.4.7   | 加入mobilenetv2、深度可分离反卷积、深度可分离centernet head                       | v0.0.7       |
| 2020.4.7   | 支持基于imgaug的颜色数据增强                                                | v0.0.8       |
| 2020.4.8   | 支持单机多卡时list类型数据分配到各个gpu, 使用logging模块输出                           | v0.0.9       |
| 2020.4.9   | 同时兼容在dataloader中进行encode/在计算loss时encode。                         | v0.0.10      |
|            | 注：在dataloader中进行多线程encode时，model的encoder必须指向一个静态方法               |              |
| 2020.4.9   | 加入evaluator builder, 目前只支持coco bbox eval                         | v0.0.11      |
| 2020.4.13  | 完成inference                                                      | v0.0.12      |
| 2020.4.13  | 支持色彩偏移、噪声数据增强，加入requirement文件                                    | v0.0.13      |
| 2020.4.14  | 移植CtFCOS（未验证），加入DIOULoss                                         | v0.0.14      |
| 2020.4.15  | 支持FPN                                                            | v0.0.15      |
| 2020.4.16  | 移植MMDetection版FCOS（未验证），加入IOULoss、GIOU Loss                      | v0.0.16      |
| 2020.4.16  | 支持Group Normalization                                            | v0.0.17      |
| 2020.4.16  | 修正了FCOS inference时bbox转换大小的bug                                   | v0.0.18      |
| 2020.4.17  | 加入了pth转onnx的脚本 publish_model.py                                  | v0.0.19      |
| 2020.4.17  | 支持计算混淆矩阵                                                         | v0.0.20      |
| 2020.4.17  | 支持pascal eval                                                    | v0.0.21      |
| 2020.4.20  | 修复了非1：1输入时生成仿射变换矩阵错误的bug                                         | v0.0.22      |
| 2020.4.21  | 支持pascal AP、Precision&Recall，修改tensorboard显示顺序                   | v0.0.23      |
| 2020.4.21  | 支持ShuffleNetV2                                                   | v0.0.24      |
| 2020.4.22  | 支持GhostNet                                                       | v0.0.25      |
| 2020.4.22  | CenterNet支持归一化宽高                                                 | v0.0.26      |
| 2020.4.23  | 加入计算模型flops和参数量的脚本                                               | v0.0.27      |
| 2020.4.28  | backbone可配置激活函数                                                  | v0.0.28      |
| 2020.4.29  | 加入轻量化的FCOS，配置参考FCOS-Lite_example.yml                             | v0.0.29      |
| 2020.5.7   | 实现centernet+condinst的实例分割模型                                      | v0.0.30      |
| 2020.5.12  | 实现实例分割inference的显示                                               | v0.0.31      |
| 2020.5.14  | 实现实例分割的coco evaluate                                             | v0.0.32      |
| 2020.5.18  | 优化实例分割可视化速度，增加label显示                                            | v0.0.33      |
| 2020.6.1   | 加入VoVNet                                                         | v0.0.34      |
| 2020.6.1   | centernet系列task head和neck都可以配置激活函数                               | v0.0.35      |
| 2020.6.28  | 修复了Pascal eval的多个历史遗留bug                                         | v0.0.36      |
| 2020.6.29  | 加入InstanceVisualizer 同时支持物体检测、实力分割、语义分割三种显示方式（画bbox比opencv版慢）    | v0.0.37      |
| 2020.6.29  | 加入eco_ground                                                     | v0.0.38      |
| 2020.7.08  | 加入MobileNeXt(非官方实现)                                              | v0.0.39      |
| 2020.7.14  | 支持人体姿态关键点检测任务，升级至0.1版本                                           | v0.1.0       |
| 2020.7.14  | 修改了人体姿态检测训练时跳过无标注数据的方式                                           | v0.1.1       |
| 2020.8.6   | 增加了FCOS新版论文中的涨点方法                                                | v0.1.2       |
| 2020.8.7   | 又又又又一次修复了非1：1输入时生成仿射变换矩阵错误的bug，再出错就要合并albu分支了                    | v0.1.3       |
| 2020.8.10  | 根据mobilenext作者在github上的留言，修改了sand glass模块的通道数                    | v0.1.4       |
| 2020.8.12  | 支持室内家具实例分割数据集                                                    | v0.1.5       |
| 2020.8.13  | 修正了实例分割显示时中空物体mask无法镂空的问题，但是绘图速度更慢                               | v0.1.6       |
| 2020.8.14  | 增加了带short cut反卷积neck的通道数选项                                       | v0.1.7       |
| 2020.8.14  | 支持config中neck的name为None，表示不设置neck                                | v0.1.8       |
| 2020.8.14  | 增加了深度可分离的ct-condinst head                                        | v0.1.9       |
| 2020.8.18  | 实现了用于centernet的Quality Focal Loss                                | v0.1.10      |
| 2020.8.24  | 加入了anchor head，移植了原版GFL                                          | v0.2.0       |
| 2020.8.26  | 增加了轻量级GFLhead                                                    | v0.2.1       |
| 2020.8.28  | 最新支持到pytorch 1.6，cuda 10.1                                       | v0.3.0       |
| 2020.8.28  | 加入不共享权重的GFLhead                                                  | v0.3.1       |
| 2020.9.1   | 更新至torch1.6语法，int整除必须使用//                                        | v0.3.2       |
| 2020.9.18  | 支持pytorch1.6单机多卡分布式训练                                            | v0.3.3       |
| 2020.9.18  | ShuffleNetV2更新至torchvision官方版本，可以支持加载imagenet预训练权重               | v0.3.4       |
| 2020.9.21  | GhostNet更新至华为官方版本                                                | v0.3.5       |
| 2020.9.22  | 完善Readme，正式更新至1.0版本，增加训练脚本示例                                     | v1.0.0       |
| 2020.9.22  | 增加反卷积替换上采样的FPN                                                   | v1.0.1       |
| 2020.9.23  | 修正了分布式训练时只有第一张卡会LR decay的Bug                                     | v1.0.2       |
| 2020.9.24  | 实验性支持yolo v4的mosaic数据增强                                          | v1.0.3       |
| 2020.9.27  | 增加了yolo v5中的Focus模块                                              | v1.0.4       |
| 2020.10.14 | 增加了ICLR 2021投稿的Apollo优化器                                         | v1.0.5       |
| 2020.10.15 | 添加了地平线提供的VarGNetV2                                               | v1.0.6       |
| 2020.10.23 | 添加了AdaBelief优化器                                                  | v1.0.7       |
| 2020.11.2  | 增加了EfficientNet                                                  | v1.0.8       |
| 2020.11.26 | 现已实验性支持yolo，并支持darknet权重读取！（yolo训练暂未实现）                          | v1.1.0 Beta  |
| 2020.11.27 | 已加入Generalized Focal Loss V2）                                    | v1.1.1       |
| 2020.12.03 | 加入简化版的Varifocal loss                                             | v1.2.0       |
| 2020.12.10 | 在CenterNet基础上加入One to One的正样本匹配规则（OneNet）                        | v1.3.0       |
| 2021.1.1   | 支持多任务训练（第一版），参考configs/Multi_Task里的例子，使用tools/train_multi_task.py训练 | v2.0.0       |
| 2021.1.18  | 多任务loss加权，手动设置权重功能待更新                                            | v2.0.1       |
| 2021.1.29  | 第三版多任务训练，同一batch拼不同数据                                            | v2.1.0       |
| 2021.2.18  | onenet head 支持配置激活函数，是否输出归一化坐标下的box待定                            | v2.1.0       |
| 2021.3.1   | tools目录下加入SWA模型平均                                                | v2.1.1       |
| 2021.3.2   | 加入EIOU Loss, FCOS、GFL、VFL head目前都可以选择多种IOU Loss                  | v2.1.2       |
| 2021.3.3   | 加入One2OneHead支持选择不同的IOU loss和IOU cost                            | v2.1.3       |
| 2021.3.12  | 加入数据集类别平衡功能，使用方法参考config/class_balance_example.yml               | v2.1.4       |
| 2021.3.24  | 支持YOLACT实例分割模型                                                   | v2.2.0       |
| 2021.4.1   | 支持BoxInst无mask标注实例分割训练(未验证)                                      | v2.3.0       |
| 2021.4.7   | 复现YOLOF(未完成)                                                     | v2.3.1 alpha |
| 2021.4.15  | 增加接口设置第一层卷积通道数                                                   | v2.3.2       |
| 2021.4.15  | 增加了详细文档                                                          | v2.3.3       |
| 2021.9.8   | add RepVGG backbone                                              | v2.3.3       |
| 2021.9.8   | update tools/eval.py for repvgg                                  | v2.3.3       |
 | 2022.7.5   | 增加多头检测 （基于GFL）                                                   | v0.0.1       |