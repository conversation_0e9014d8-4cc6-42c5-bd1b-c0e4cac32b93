

from tools.inference import Detector, MultiTaskDetector
from econn.utils.config import cfg, update_config
import cv2
import numpy as np
import os
import torch

# demo = r"D:\Videos\YDXJ0175_0226.mp4"
# demo = r"D:\Dataset\coco\val2017"
# demo = r'D:\Dataset\eco_indoor_2020_old\val2019'
demo = r"webcam"
camera_id = 0
config_path = r"/home/<USER>/nn/exp/GFL_0503_gpu2/train_config.yml"
model_path = r"/home/<USER>/nn/exp/GFL_0503_gpu2/model_best_score/model_best_score.pth"

update_config(cfg, config_path)
class_names = cfg.class_names
class_names = ['Trash can', 'Charging dock', 'Cleaning cloth', 'Rug', 'Shoes',
              'Wire', 'Sliding rail', 'Wheels']
if cfg.model.architecture == 'MultiTask':
    detector = MultiTaskDetector(cfg, model_path, device='cuda:0')
else:
    detector = Detector(cfg, model_path, 'cuda:0')


torch.backends.cudnn.enabled = True
torch.backends.cudnn.benchmark = True

image_ext = ['.jpg', '.jpeg', '.webp', '.bmp', '.png']
video_ext = ['mp4', 'mov', 'avi', 'mkv']

if demo == 'webcam' or demo[demo.rfind('.') + 1:].lower() in video_ext:
    camera = cv2.VideoCapture(2, cv2.CAP_V4L2)
    # 指定分辨率和编码格式
    camera.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))
    camera.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
    camera.set(cv2.CAP_PROP_FRAME_HEIGHT, 960)
    if not camera.isOpened():
        print("无法打开摄像头")
        exit()
    print('Press "Esc", "q" or "Q" to exit.')
    while True:
        ret_val, img = camera.read()
        if not ret_val:
            print("无法读取帧")
            break

        cv2.imshow('摄像头画面', img)

        if cv2.waitKey(1) & 0xFF == ord('q'):  # 按 'q' 键退出
            break

        # img = cv2.remap(img, mapx, mapy, cv2.INTER_LINEAR)
        dets, meta = detector.inference(img)
        detector.show(dets, meta, class_names, 0.3, wait=1)
        ch = cv2.waitKey(1)
        if ch == 27 or ch == ord('q') or ch == ord('Q'):
            break




# import cv2

# cap = cv2.VideoCapture(0, cv2.CAP_V4L2)
# # 指定分辨率和编码格式
# cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))
# cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
# cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)

# if not cap.isOpened():
#     print("摄像头打开失败")
#     exit()

# while True:
#     ret, frame = cap.read()
#     if not ret:
#         print("无法读取帧")
#         break

#     cv2.imshow("Camera", frame)
#     if cv2.waitKey(1) & 0xFF == ord('q'):
#         break

# cap.release()
# cv2.destroyAllWindows()


