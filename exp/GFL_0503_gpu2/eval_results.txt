COCO Eval:
 Average Precision  (AP) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = 0.452
 Average Precision  (AP) @[ IoU=0.50      | area=   all | maxDets=100 ] = 0.690
 Average Precision  (AP) @[ IoU=0.75      | area=   all | maxDets=100 ] = 0.486
 Average Precision  (AP) @[ IoU=0.50:0.95 | area= small | maxDets=100 ] = 0.091
 Average Precision  (AP) @[ IoU=0.50:0.95 | area=medium | maxDets=100 ] = 0.410
 Average Precision  (AP) @[ IoU=0.50:0.95 | area= large | maxDets=100 ] = 0.570
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=  1 ] = 0.489
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets= 10 ] = 0.620
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = 0.625
 Average Recall     (AR) @[ IoU=0.50:0.95 | area= small | maxDets=100 ] = 0.251
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=medium | maxDets=100 ] = 0.591
 Average Recall     (AR) @[ IoU=0.50:0.95 | area= large | maxDets=100 ] = 0.752
PascalBoxes_PerformanceByCategory/AP@0.5IOU/bin=0.832
PascalBoxes_PerformanceByCategory/AP@0.5IOU/charge=0.920
PascalBoxes_PerformanceByCategory/AP@0.5IOU/cloth=0.542
PascalBoxes_PerformanceByCategory/AP@0.5IOU/rail=0.552
PascalBoxes_PerformanceByCategory/AP@0.5IOU/rug=0.792
PascalBoxes_PerformanceByCategory/AP@0.5IOU/shoe=0.816
PascalBoxes_PerformanceByCategory/AP@0.5IOU/wheel=0.611
PascalBoxes_PerformanceByCategory/AP@0.5IOU/wire=0.567
PascalBoxes_PerformanceByCategory/Precision@0.4IOU/bin=0.787
PascalBoxes_PerformanceByCategory/Precision@0.4IOU/charge=0.895
PascalBoxes_PerformanceByCategory/Precision@0.4IOU/cloth=0.702
PascalBoxes_PerformanceByCategory/Precision@0.4IOU/rail=0.770
PascalBoxes_PerformanceByCategory/Precision@0.4IOU/rug=0.789
PascalBoxes_PerformanceByCategory/Precision@0.4IOU/shoe=0.814
PascalBoxes_PerformanceByCategory/Precision@0.4IOU/wheel=0.718
PascalBoxes_PerformanceByCategory/Precision@0.4IOU/wire=0.704
PascalBoxes_PerformanceByCategory/Recall@0.4IOU/bin=0.768
PascalBoxes_PerformanceByCategory/Recall@0.4IOU/charge=0.801
PascalBoxes_PerformanceByCategory/Recall@0.4IOU/cloth=0.421
PascalBoxes_PerformanceByCategory/Recall@0.4IOU/rail=0.400
PascalBoxes_PerformanceByCategory/Recall@0.4IOU/rug=0.702
PascalBoxes_PerformanceByCategory/Recall@0.4IOU/shoe=0.750
PascalBoxes_PerformanceByCategory/Recall@0.4IOU/wheel=0.585
PascalBoxes_PerformanceByCategory/Recall@0.4IOU/wire=0.485
PascalBoxes_Precision/mAP@0.5IOU=0.704
