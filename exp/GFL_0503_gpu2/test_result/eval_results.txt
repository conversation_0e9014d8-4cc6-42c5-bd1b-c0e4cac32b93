COCO Eval:
 Average Precision  (AP) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = 0.404
 Average Precision  (AP) @[ IoU=0.50      | area=   all | maxDets=100 ] = 0.648
 Average Precision  (AP) @[ IoU=0.75      | area=   all | maxDets=100 ] = 0.416
 Average Precision  (AP) @[ IoU=0.50:0.95 | area= small | maxDets=100 ] = 0.122
 Average Precision  (AP) @[ IoU=0.50:0.95 | area=medium | maxDets=100 ] = 0.494
 Average Precision  (AP) @[ IoU=0.50:0.95 | area= large | maxDets=100 ] = 0.561
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=  1 ] = 0.414
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets= 10 ] = 0.635
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = 0.642
 Average Recall     (AR) @[ IoU=0.50:0.95 | area= small | maxDets=100 ] = 0.229
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=medium | maxDets=100 ] = 0.639
 Average Recall     (AR) @[ IoU=0.50:0.95 | area= large | maxDets=100 ] = 0.786
PascalBoxes_PerformanceByCategory/AP@0.5IOU/bin=0.600
PascalBoxes_PerformanceByCategory/AP@0.5IOU/charge=0.868
PascalBoxes_PerformanceByCategory/AP@0.5IOU/cloth=0.882
PascalBoxes_PerformanceByCategory/AP@0.5IOU/rail=0.617
PascalBoxes_PerformanceByCategory/AP@0.5IOU/rug=0.380
PascalBoxes_PerformanceByCategory/AP@0.5IOU/shoe=0.884
PascalBoxes_PerformanceByCategory/AP@0.5IOU/wheel=0.637
PascalBoxes_PerformanceByCategory/AP@0.5IOU/wire=0.314
PascalBoxes_PerformanceByCategory/Precision@0.4IOU/bin=0.731
PascalBoxes_PerformanceByCategory/Precision@0.4IOU/charge=0.951
PascalBoxes_PerformanceByCategory/Precision@0.4IOU/cloth=0.978
PascalBoxes_PerformanceByCategory/Precision@0.4IOU/rail=0.500
PascalBoxes_PerformanceByCategory/Precision@0.4IOU/rug=0.133
PascalBoxes_PerformanceByCategory/Precision@0.4IOU/shoe=0.924
PascalBoxes_PerformanceByCategory/Precision@0.4IOU/wheel=0.757
PascalBoxes_PerformanceByCategory/Precision@0.4IOU/wire=0.477
PascalBoxes_PerformanceByCategory/Recall@0.4IOU/bin=0.466
PascalBoxes_PerformanceByCategory/Recall@0.4IOU/charge=0.431
PascalBoxes_PerformanceByCategory/Recall@0.4IOU/cloth=0.441
PascalBoxes_PerformanceByCategory/Recall@0.4IOU/rail=0.500
PascalBoxes_PerformanceByCategory/Recall@0.4IOU/rug=0.500
PascalBoxes_PerformanceByCategory/Recall@0.4IOU/shoe=0.824
PascalBoxes_PerformanceByCategory/Recall@0.4IOU/wheel=0.587
PascalBoxes_PerformanceByCategory/Recall@0.4IOU/wire=0.181
PascalBoxes_Precision/mAP@0.5IOU=0.648
