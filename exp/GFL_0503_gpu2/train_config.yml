#Config File example
save_dir: /home/<USER>/code_paper/econn-master/exp/ground_data_driven/GFL_0503_gpu2/test_result

class_names: ['bin', 'charge', 'cloth', 'rug', 'shoe',
              'wire', 'rail', 'wheel']
model:
  architecture: GFL
  backbone:
    name: MobileNeXt
    width_mult: 1.0
    identity_tensor_multiplier: 1.0
    out_stages: (2,4,7)
    last_channel: 640
    activation: ReLU
  neck:
    name: FPN
    in_channels: (192, 384, 640)
    out_channels: 64
    start_level: 0
    add_extra_convs: False
    extra_convs_on_inputs: False  # use P5
    num_outs: 3
    relu_before_extra_convs: True
  task_head:
    name: LiteGFLHeadV2
    num_classes: 8
    input_channel: 64
    feat_channels: 64
    stacked_convs: 3
    octave_base_scale: 8
    scales_per_octave: 1
    anchor_ratios: [1.0]
    anchor_strides: [8, 16, 32]
    target_means: [.0, .0, .0, .0]
    target_stds: [0.1, 0.1, 0.2, 0.2]
    reg_max: 16
    norm_cfg:
      type: BN
    loss:
      loss_qfl:
        name: QualityFocalLoss
        use_sigmoid: True
        beta: 2.0
        loss_weight: 1.0
      loss_dfl:
        name: DistributionFocalLoss
        loss_weight: 0.25
      loss_bbox:
        name: GIoULoss
        loss_weight: 2.0
train:
  num_epochs: 150
  batchsize: 128
  num_workers: 32
  gpu_ids: [0,1]
  freezing_parameters:
  optimizer:
    name: SGD
    lr: 0.04
    momentum: 0.9
    weight_decay: 0.0001
  lr_schedule:
    name: multi_step
    milestones: [80,110,120]
  warmup:
    name: linear
    steps: 500
    ratio: 0.1
  val_intervals: 5
  val_type: coco_bbox
#   resume:
  load_model: /home/<USER>/code_project/nn/exp/GFL_0503_gpu2/model_best_score/model_best_score.pth
#val:

data:
  train:
    dataset_name: eco_ground
    ann_path: /secret/zhangdezheng/ground_datasets/ground_dataset_20220414/annotations/train_0414.json
    img_path: /secret/zhangdezheng/ground_datasets/ground_dataset_20220414/ground_train_0414
    input_size: [512,512] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: False
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False
    augmentation:
      random_flip:
        flip_ratio: 0.5
      ioa_random_crop:
        random_stretch: True
      color:
        brightness: [0.8, 1.2]
        saturation: [0.8, 1.4]
        color_shift: [0.8, 1.2]

  val:
    dataset_name: eco_ground
    ann_path: /media/zhangdezheng/new_volume/eco_ground_copy/N10测试场地/test_dataset/ground_T10test_8classes.json
    img_path: /media/zhangdezheng/new_volume/eco_ground_copy/N10测试场地/test_dataset/images
    input_size: [512,512] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: False
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False

log:
  interval: 10




#debug_config:
#  debug: 2
#  eval_oracle_hm: True
#  eval_oracle_dis: True
