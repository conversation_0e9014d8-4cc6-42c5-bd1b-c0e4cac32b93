save_dir: workspace/class_balance_test
class_names: ['<PERSON>u<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>',
              '<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
              '<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
              '<PERSON><PERSON><PERSON>', 'uchair']
model:
  architecture: FCOS
  backbone:
    name: MobileNetV2
    width_mult: 1.0
    out_stages: (4,6)
    last_channel: 512
    activation: ReLU
  neck:
    name: DeconvFPN
    in_channels: (96,512)
    out_channels: 128
    start_level: 0
    add_extra_convs: True
    extra_convs_on_inputs: False
    num_outs: 3
    relu_before_extra_convs: True
#    activation: ReLU
  task_head:
    name: LiteOne2OneHead
    num_classes: 11
    input_channel: 128
    feat_channels: 64
    stacked_convs: 2
    strides: [16, 32]
    loss:
      loss_cls:
        name: FocalLoss
        gamma: 2.0
        alpha: 0.25
        loss_weight: 2.0
      loss_bbox:
        name: L1Loss
        loss_weight: 5.0
      loss_iou:
        name: GIoULoss
        loss_weight: 2.0
train:
  num_epochs: 300
  batchsize: 230
  num_workers: 24
  gpu_ids: [0]
  optimizer:
    name: AdaBelief
    lr: 1.5e-4
    betas: [0.9, 0.999]
    eps: 1.0e-8
    weight_decay: 0.0001
  lr_schedule:
    name: multi_step
    milestones: [250,280]
  warmup:
    name: linear
    steps: 1000
    ratio: 0.01
  val_intervals: 5
  val_type: coco_bbox
#  resume:
#  load_model:

data:
  train:
    dataset_name: eco_indoor
    warp_mode:
      name: class_balance
      oversample_thr: 0.1
      filter_empty_gt: False
    ann_path: /default_group/eco_indoor2020/annotations/ecov6.0_instances_train2020.json
    img_path: /default_group/eco_indoor2020/train2020
    input_size: [320,320] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: False
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False
    augmentation:
      random_flip:
        flip_ratio: 0.5
      ioa_random_crop:
        random_stretch: False
      color:
        brightness: [0.8, 1.2]
        saturation: [0.8, 1.2]
        color_shift: [0.8, 1.2]

  val:
    dataset_name: eco_indoor
    ann_path: /default_group/eco_indoor2020/annotations/ecov6.0_instances_val2020.json
    img_path: /default_group/eco_indoor2020/val2020
    input_size: [320,320] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: False
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False

log:
  interval: 10