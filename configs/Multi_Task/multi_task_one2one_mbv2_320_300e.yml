save_dir: workspace/multi_task/indoor_ground_one2one
class_names:
  task1_indoor: ['Pu<PERSON>ong<PERSON><PERSON>', '<PERSON>ha<PERSON><PERSON>',
                 '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
                 '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
                 '<PERSON><PERSON><PERSON>', 'uchair']
  task2_ground: ['bin', 'charge', 'cloth', 'rug', 'shoe',
                 'wire', 'rail']
model:
  architecture: MultiTask
  backbone:
    name: MobileNetV2
    input_channel: 32
    width_mult: 1.0
    out_stages: (2,4,6)
    last_channel: 512
    activation: ReLU
  neck:
    name: DeconvFPN
    in_channels: (32,96,512)
    out_channels: 128
    start_level: 0
    add_extra_convs: True
    extra_convs_on_inputs: False
    num_outs: 3
    relu_before_extra_convs: True
  task_head:
    task1_indoor:
      name: LiteOne2OneHead
      num_classes: 11
      input_channel: 128
      feat_channels: 64
      stacked_convs: 2
      strides: [8, 16, 32]
      class_weight: 2.0
      giou_weight: 2.0
      l1_weight: 5.0
      loss:
        loss_cls:
          name: Focal<PERSON>oss
        loss_bbox:
          name: L1Loss
        loss_giou:
          name: GIOULoss
    task2_ground:
      name: LiteOne2OneHead
      num_classes: 7
      input_channel: 128
      feat_channels: 64
      stacked_convs: 2
      strides: [8, 16, 32]
      class_weight: 2.0
      giou_weight: 2.0
      l1_weight: 5.0
      loss:
        loss_cls:
          name: FocalLoss
        loss_bbox:
          name: L1Loss
        loss_giou:
          name: GIOULoss
train:
  num_epochs: 150
  batchsize: 200
  num_workers: 12
  gpu_ids: [0]
  optimizer:
    name: AdaBelief
    lr: 5.0e-5
    betas: [0.9, 0.999]
    eps: 1.0e-8
    weight_decay: 0.0001
  lr_schedule:
    name: multi_step
    milestones: [120,140]
  warmup:
    name: linear
    steps: 500
    ratio: 0.1
  val_intervals: 5
  val_type:
    task1_indoor: coco_bbox
    task2_ground: coco_bbox
#  resume:
#  load_model:
input_setting: &input_setting
  input_size: [320,320] #[w,h]
  img_norm:
    mean: [0.408, 0.447, 0.470]
    std: [0.289, 0.274, 0.278]
  resize_keep_ratio: False
data:
  task1_indoor:
    train:
      dataset_name: eco_indoor
      ann_path: /eco_indoor/annotations/ecov5.4_instances_train2019.json
      img_path: /eco_indoor/train2019
      <<: *input_setting
      with_crowd: False
      with_mask: False
      with_keypoints: False
      skip_img_without_anno: False
      augmentation:
        random_flip:
          flip_ratio: 0.5
        ioa_random_crop:
          random_stretch: False
        color:
          brightness: [0.8, 1.2]
          saturation: [0.8, 1.4]
          color_shift: [0.8, 1.2]

    val:
      dataset_name: eco_indoor
      ann_path: /eco_indoor/annotations/ecov5.4_instances_val2019.json
      img_path: /eco_indoor/val2019
      <<: *input_setting
      with_crowd: False
      with_mask: False
      with_keypoints: False
      skip_img_without_anno: False
  task2_ground:
    train:
      dataset_name: eco_ground
      ann_path: /eco_ground/annotations/instances_train2019.json
      img_path: /eco_ground/train2019
      <<: *input_setting
      with_crowd: False
      with_mask: False
      with_keypoints: False
      skip_img_without_anno: False
      augmentation:
        random_flip:
          flip_ratio: 0.5
        ioa_random_crop:
          random_stretch: True
        color:
          brightness: [0.8, 1.2]
          saturation: [0.8, 1.4]
          color_shift: [0.8, 1.2]

    val:
      dataset_name: eco_ground
      ann_path: /eco_ground/annotations/instances_val2019.json
      img_path: /eco_ground/val2019
      <<: *input_setting
      with_crowd: False
      with_mask: False
      with_keypoints: False
      skip_img_without_anno: False
log:
  interval: 10

