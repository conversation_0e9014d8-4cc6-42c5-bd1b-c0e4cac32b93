#Config File example
save_dir: workspace/multi_task/eco_indoor
class_names: ['<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>',
              '<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
              '<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
              '<PERSON><PERSON><PERSON>', 'uchair']
model:
  architecture: CenterNet
  backbone:
    name: MobileNetV2
    width_mult: 1.0
    out_stages: (1,2,4,6)
    last_channel: 512
    activation: ReLU6
  neck:
    name: DepthwiseShortcutDeconv
    in_channels: (24,32,96,512,)
    deconv_channels: (256, 128, 64)
    activation: ReLU6
  task_head:
    name: DepthwiseCenterNet
    input_channel: 64
    num_classes: 11
    head_conv: 64
    strides: [4]
    norm_wh: False
    activation: ReLU6
    loss:
      loss_hm:
        name: CenterNetFocalLoss
        weight: 1.0
      loss_wh:
        name: L1Loss
        weight: 0.1
      loss_offset:
        name: L1Loss
        weight: 1.0
train:
  num_epochs: 72
  batchsize: 2
  num_workers: 4
  gpu_ids: [1]
  optimizer:
    name: SGD
    momentum: 0.9
    lr: 3.0e-3
    weight_decay: 5.0e-5
  lr_schedule:
    name: multi_step
    milestones: [54,66]
  warmup:
    name: linear
    steps: 500
    ratio: 0.3
  val_intervals: 2
  val_type: coco_bbox
#  resume:
#  load_model: D:\Projects\ECO_NN\workspace\model_last.pth
#val:

data:
  train:
    dataset_name: eco_indoor
    ann_path: /data/Dataset/eco_indoor/annotations/ecov5.4_instances_train2019.json
    img_path: /data/Dataset/eco_indoor/train2019
    input_size: [320,320] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: False
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False
    augmentation:
      use_albu: False
      random_flip:
        flip_ratio: 0.5
      ioa_random_crop:
        random_stretch: False
      color:
        brightness: [0.8, 1.2]
        saturation: [0.8, 1.4]
        color_shift: [0.8, 1.2]
        noise: [1, 1.1]
  val:
    dataset_name: eco_indoor
    ann_path: /data/Dataset/eco_indoor/annotations/ecov5.4_instances_val2019.json
    img_path: /data/Dataset/eco_indoor/val2019
    input_size: [320,320] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: False
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False

log:
  interval: 10








