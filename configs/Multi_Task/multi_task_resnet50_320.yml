save_dir: ../workspace/multi_task/ground_indoor_centernet_resnet50
class_names:
  task1_indoor: ['Pu<PERSON>ong<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>',
                 '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
                 '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
                 '<PERSON><PERSON><PERSON>', 'uchair']
  task2_ground: ['bin', 'charge', 'cloth', 'rug', 'shoe',
                 'wire', 'rail']
model:
  architecture: MultiTask
  backbone:
    name: ResNet
    depth: 50
    out_stages: (1,2,3,4,)
    activation: ReLU
    # load_from: /home/<USER>/thh/code/pretrained_model/resnet50-19c8e357.pth
  neck:
    name: DepthwiseShortcutDeconv
    in_channels: (256,512,1024,2048,)
    deconv_channels: (256, 128, 64)
    activation: ReLU
  task_head:
    task1_indoor:
      name: DepthwiseCenterNet
      input_channel: 64
      num_classes: 11
      head_conv: 64
      strides: [4]
      norm_wh: False
      activation: ReLU
      loss:
        loss_hm:
          name: CenterNetFocalLoss
          weight: 1.0
        loss_wh:
          name: L1Loss
          weight: 0.1
        loss_offset:
          name: L1Loss
          weight: 1.0
    task2_ground:
      name: DepthwiseCenterNet
      input_channel: 64
      num_classes: 7
      head_conv: 64
      strides: [4]
      norm_wh: False
      activation: ReLU
      loss:
        loss_hm:
          name: CenterNetFocalLoss
          weight: 1.0
        loss_wh:
          name: L1Loss
          weight: 0.1
        loss_offset:
          name: L1Loss
          weight: 1.0
train:
  num_epochs: 300
  batchsize: 2
  num_workers: 4
  gpu_ids: [1]
  optimizer:
    name: Adam
    lr: 1.75e-4
    weight_decay: 5.0e-5
  lr_schedule:
    name: multi_step
    milestones: [240,280]
  warmup:
    name: linear
    steps: 500
    ratio: 0.1
  val_intervals: 5
  val_type:
    task1_indoor: coco_bbox
    task2_ground: coco_bbox
resume: 
#  load_model:
input_setting: &input_setting
  input_size: [320,320] #[w,h]
  img_norm:
    mean: [0.408, 0.447, 0.470]
    std: [0.289, 0.274, 0.278]
  resize_keep_ratio: False
data:
  task1_indoor:
    train:
      dataset_name: eco_indoor
      ann_path: /data/Dataset/eco_indoor/annotations/ecov5.4_instances_train2019.json
      img_path: /data/Dataset/eco_indoor/train2019
      <<: *input_setting
      with_crowd: False
      with_mask: False
      with_keypoints: False
      skip_img_without_anno: False
      augmentation:
        random_flip:
          flip_ratio: 0.5
        ioa_random_crop:
          random_stretch: False
        color:
          brightness: [0.8, 1.2]
          saturation: [0.8, 1.2]
          color_shift: [0.8, 1.2]

    val:
      dataset_name: eco_indoor
      ann_path: /data/Dataset/eco_indoor/annotations/ecov5.4_instances_val2019.json
      img_path: /data/Dataset/eco_indoor/val2019
      <<: *input_setting
      with_crowd: False
      with_mask: False
      with_keypoints: False
      skip_img_without_anno: False
  task2_ground:
    train:
      dataset_name: eco_ground
      # sftp://************/data/Dataset/eco_ground/train2019
      ann_path: /data/Dataset/eco_ground/instances_train2019.json
      img_path: /data/Dataset/eco_ground/train2019
      <<: *input_setting
      with_crowd: False
      with_mask: False
      with_keypoints: False
      skip_img_without_anno: False
      augmentation:
        random_flip:
          flip_ratio: 0.5
        ioa_random_crop:
          random_stretch: True
        color:
          brightness: [0.8, 1.2]
          saturation: [0.8, 1.2]
          color_shift: [0.8, 1.2]

    val:
      dataset_name: eco_ground
      ann_path: /data/Dataset/eco_ground/instances_val2019.json
      img_path: /data/Dataset/eco_ground/val2019
      <<: *input_setting
      with_crowd: False
      with_mask: False
      with_keypoints: False
      skip_img_without_anno: False
log:
  interval: 10

