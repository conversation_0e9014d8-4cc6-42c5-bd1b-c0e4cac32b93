#Config File example
save_dir: ../workspace/line_points
class_names: ["body","face"]
model:
  architecture: GFL
  backbone:
    name: MobileNeXt
    width_mult: 1.0
    identity_tensor_multiplier: 1.0
    out_stages: (2,4,7)
    last_channel: 1280
    activation: ReLU  
  neck:
    name: FPN
    in_channels: (192, 384, 1280)
    out_channels: 64
    start_level: 0
    add_extra_convs: True
    extra_convs_on_inputs: False  # use P5
    num_outs: 3
    relu_before_extra_convs: True
  task_head:
    name: LineHead
    num_classes: 1
    input_channel: 64
    feat_channels: 64
    stacked_convs: 4
    octave_base_scale: 8
    scales_per_octave: 1
    anchor_ratios: [1.0]
    anchor_strides: [8, 16, 32, 64]
    target_means: [.0, .0, .0, .0]
    target_stds: [0.1, 0.1, 0.2, 0.2]
    reg_max: 16
    loss:
      loss_qfl:
        name: BCELoss
        use_sigmoid: True
        beta: 2.0
        loss_weight: 1.0
train:
  num_epochs: 300
  batchsize: 64
  num_workers: 8
  qat: False
  gpu_ids: [0]
  qat: False
  optimizer:
    name: SGD
    #lr: 0.002 #batch_size_ 64  
    lr: 0.01 #batch_size_64
    momentum: 0.9
    weight_decay: 0.0001
  lr_schedule:
    name: multi_step
    milestones: [200,300]
  warmup:
    name: linear
    steps: 300
    ratio: 0.3
  val_intervals: 5
  val_type: line_point
  #resume: 
  #load_model: exps_shx/outputs/line_points/model_last.pth
#val:

data:
  train:
    dataset_name: LinePoint
    ann_path: /raid/Multi_Task/wire_1841/annotations/train.json
    img_path: /raid/Multi_Task/wire_1841/images/train
    input_size: [512,384] #[w,h]
    img_norm:
      #mean: [0.408, 0.447, 0.470] #104.04 113.985 119.85
      #std: [0.289, 0.274, 0.278] #73.695 69.87 70.89
      mean: [0.456, 0.456, 0.456] #104.04 113.985 119.85
      std: [0.225, 0.225, 0.225] #73.695 69.87 70.
    resize_keep_ratio: False  
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False
    augmentation:
      #  resize:
        random_flip:
          flip_ratio: 0.5
        ioa_random_crop:
          random_stretch: False
        color:
          brightness: [0.8, 1.2]
          saturation: [0.8, 1.2]
          color_shift: [0.8, 1.2]

  val:
    dataset_name: LinePoint
    ann_path: /raid/Multi_Task/wire_1841/annotations/val.json
    img_path: /raid/Multi_Task/wire_1841/images/val
    input_size: [512,384] #[w,h]
    img_norm:
      mean: [0.456, 0.456, 0.456] #104.04 113.985 119.85
      std: [0.225, 0.225, 0.225] #73.695 69.87 70.
    resize_keep_ratio: False
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False
  test:
    dataset_name: LinePoint
    ann_path: /raid/Multi_Task/wire_1841/annotations/val.json
    img_path: /raid/Multi_Task/wire_1841/images/val
    input_size: [512,384] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: False
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False

log:
  interval: 5




#debug_config:
#  debug: 2
#  eval_oracle_hm: True
#  eval_oracle_dis: True
