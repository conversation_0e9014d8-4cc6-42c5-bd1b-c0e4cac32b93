save_dir: ../workspace/multi_task/rug_line_ground_resnet50
class_names:
  task1_seg: ['background',  'rug']
  task2_ground: ['bin', 'charge', 'cloth', 'rug', 'shoe',
                 'wire', 'rail']
  task3_line: ["body","face"]
model:
  architecture: MultiTask
  backbone:
    name: ResNet
    depth: 50
    out_stages: (1,2,3)
    activation: ReLU
    load_from: /data/Code/pretrained_model/resnet50-19c8e357.pth
  neck:
    name: FPN
    in_channels: (256, 512, 1024)
    out_channels: 256
    start_level: 0
    add_extra_convs: True
    extra_convs_on_inputs: False  # use P5
    num_outs: 3
    relu_before_extra_convs: True

  task_head:
    task1_seg:
      name: RethinkBiSeNetHead
  #    input_channel: 64
      num_classes: 2
      aux: False
      in_channels: (256, 256, 128)
      loss:
        loss1:
          name: RMILOSS
          loss_weight: 1.0
          aux_weight: 0.4
    task2_ground:
      name: DepthwiseCenterNet
      input_channel: 256
      num_classes: 7
      head_conv: 64
      strides: [4]
      norm_wh: False
      activation: ReLU
      loss:
        loss_hm:
          name: CenterNetFocalLoss
          weight: 1.0
        loss_wh:
          name: L1Loss
          weight: 0.1
        loss_offset:
          name: L1Loss
          weight: 1.0
    task3_line:
      name: LineHead
      num_classes: 1
      input_channel: 256
      feat_channels: 256
      stacked_convs: 4
      octave_base_scale: 8
      scales_per_octave: 1
      anchor_ratios: [1.0]
      anchor_strides: [8, 16, 32, 64]
      target_means: [.0, .0, .0, .0]
      target_stds: [0.1, 0.1, 0.2, 0.2]
      reg_max: 16
      loss:
        loss_qfl:
          name: BCELoss
          use_sigmoid: True
          beta: 2.0
          loss_weight: 1.0
train:
  use_amp: False
  num_epochs: 300
  batchsize: 2
  num_workers: 4
  gpu_ids: [1]
  optimizer:
  #   name: Adam
  #   lr: 1.75e-4
  #   weight_decay: 5.0e-5
  # lr_schedule:
  #   name: multi_step
  #   milestones: [240,280]
    name: SGD
    lr: 0.001
    lr_separate:
      backbone: 0.001 #单独配置lr
      neck: 0.001
      task_heads: 0.001 #单独配置lr
    weight_decay: 5.0e-4
    momentum: 0.9
  lr_schedule:
    name: Poly
    power: 0.9
  warmup:
    name: linear
    steps: 500
    ratio: 0.1
  val_intervals: 5
  val_type:
    task1_seg: coco_stuff
    task2_ground: coco_bbox
    task3_line: line_point

resume: 
#  load_model:
input_setting: &input_setting
  input_size: [320,320] #[w,h]
  img_norm:
    mean: [0.408, 0.447, 0.470]
    std: [0.289, 0.274, 0.278]
  resize_keep_ratio: False
data:
  task1_seg:
    train:
      dataset_name: eco_semantic_indoor
      ann_path: /data/Dataset/eco_rug_20201229_v1
      img_path: /data/Dataset/eco_rug_20201229_v1
      <<: *input_setting
      base_size: 720
      valid_ids: [0, 2]
      img_norm:
        mean: [0.408, 0.447, 0.470]
        std: [0.289, 0.274, 0.278]

      with_crowd: False
      with_mask: False
      with_keypoints: False
      skip_img_without_anno: False
      # augmentation:
      #   random_flip:
      #     flip_ratio: 0.5
      #   ioa_random_crop:
      #     random_stretch: False
      #   color:
      #     brightness: [0.8, 1.2]
      #     saturation: [0.8, 1.2]
      #     color_shift: [0.8, 1.2]

    val:
      dataset_name: eco_semantic_indoor
      ann_path: /data/Dataset/eco_rug_20201229_v1
      img_path: /data/Dataset/eco_rug_20201229_v1
      <<: *input_setting
      base_size: 720
      valid_ids: [0, 2]
      with_crowd: False
      with_mask: False
      with_keypoints: False
      skip_img_without_anno: False
  task2_ground:
    train:
      dataset_name: eco_ground
      # sftp://************/data/Dataset/eco_ground/train2019
      ann_path: /data/Dataset/eco_ground/instances_train2019.json
      img_path: /data/Dataset/eco_ground/train2019
      <<: *input_setting
      with_crowd: False
      with_mask: False
      with_keypoints: False
      skip_img_without_anno: False
      augmentation:
        random_flip:
          flip_ratio: 0.5
        ioa_random_crop:
          random_stretch: True
        color:
          brightness: [0.8, 1.2]
          saturation: [0.8, 1.2]
          color_shift: [0.8, 1.2]

    val:
      dataset_name: eco_ground
      ann_path: /data/Dataset/eco_ground/instances_val2019.json
      img_path: /data/Dataset/eco_ground/val2019
      <<: *input_setting
      with_crowd: False
      with_mask: False
      with_keypoints: False
      skip_img_without_anno: False
  task3_line:
    train:
      dataset_name: LinePoint
      ann_path: /data/Dataset/wire_1841/annotations/train.json
      img_path: /data/Dataset/wire_1841/images/train
      <<: *input_setting
      with_crowd: False
      with_mask: False
      with_keypoints: False
      skip_img_without_anno: False
      augmentation:
        random_flip:
          flip_ratio: 0.5
        ioa_random_crop:
          random_stretch: False
        color:
          brightness: [0.8, 1.2]
          saturation: [0.8, 1.2]
          color_shift: [0.8, 1.2]

    val:
      dataset_name: LinePoint
      ann_path: /data/Dataset/wire_1841/annotations/val.json
      img_path: /data/Dataset/wire_1841/images/val
      <<: *input_setting
      with_crowd: False
      with_mask: False
      with_keypoints: False
      skip_img_without_anno: False
log:
  interval: 10

