#Config File example
save_dir: D:\Projects\ECO_NN\workspace\coco_kp
class_names: ['person']
model:
  architecture: CenterNet
  backbone:
    name: MobileNetV2
    input_channel: 32
    width_mult: 1.0
    out_stages: (6,)
    last_channel: 1280
    activation: ReLU6
  neck:
    name: DepthwiseDeconv
    input_channel: (1280,)
    activation: ReLU6
  task_head:
    name: Depthwise_CenterNet_Keypoints
    input_channel: 64
    num_classes: 1
    head_conv: 64
    strides: [4]
    norm_wh: False
    activation: ReLU6
    loss:
      loss_hm:
        name: CenterNetFocalLoss
        weight: 1.0
      loss_wh:
        name: L1Loss
        weight: 0.1
      loss_offset:
        name: L1Loss
        weight: 1.0
      loss_hp:
        name: L1Loss
        weight: 1.0
      loss_hp_offset:
        name: L1Loss
        weight: 1.0
      loss_hm_hp:
        name: CenterNetFocalLoss
        weight: 1.0
train:
  num_epochs: 400
  batchsize: 18
  num_workers: 4
  gpu_ids: [0]
  optimizer:
    name: <PERSON>
    lr: 3.0e-4
    weight_decay: 2.0e-5
  lr_schedule:
    name: multi_step
    milestones: [350,380]
  warmup:
    name: linear
    steps: 1
    ratio: 0.3
  val_intervals: 1
  val_type: coco_keypoints
#  resume:
#  load_model: D:\Projects\ECO_NN\workspace\model_last.pth
#val:

data:
  train:
    dataset_name: coco_hp
    ann_path: D:\Dataset\coco\annotations\person_keypoints_train2017.json
    img_path: D:\Dataset\coco\train2017
    input_size: [320,320] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: False
    with_crowd: False
    with_mask: False
    with_keypoints: True
    skip_img_without_anno: False
    augmentation:
      random_flip:
        flip_ratio: 0.5
      ioa_random_crop:
        random_stretch: False
      color:
        brightness: [0.8, 1.2]
        saturation: [0.8, 1.4]
        color_shift: [0.8, 1.2]
        noise: [1, 1.1]
  val:
    dataset_name: coco_hp
    ann_path: D:\Dataset\coco\annotations\person_keypoints_val2017.json
    img_path: D:\Dataset\coco\val2017
    input_size: [320,320] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: False
    with_crowd: False
    with_mask: False
    with_keypoints: True
    skip_img_without_anno: False

log:
  interval: 5

