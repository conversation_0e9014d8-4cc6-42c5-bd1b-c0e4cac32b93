#Config File example
save_dir: /YOUR_FOLDER/gfocalv2_r50_12e_coco
class_names: ['person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus',
              'train', 'truck', 'boat', 'traffic_light', 'fire_hydrant',
              'stop_sign', 'parking_meter', 'bench', 'bird', 'cat', 'dog',
              'horse', 'sheep', 'cow', 'elephant', 'bear', 'zebra', 'giraffe',
              'backpack', 'umbrella', 'handbag', 'tie', 'suitcase', 'frisbee',
              'skis', 'snowboard', 'sports_ball', 'kite', 'baseball_bat',
              'baseball_glove', 'skateboard', 'surfboard', 'tennis_racket',
              'bottle', 'wine_glass', 'cup', 'fork', 'knife', 'spoon', 'bowl',
              'banana', 'apple', 'sandwich', 'orange', 'broccoli', 'carrot',
              'hot_dog', 'pizza', 'donut', 'cake', 'chair', 'couch',
              'potted_plant', 'bed', 'dining_table', 'toilet', 'tv', 'laptop',
              'mouse', 'remote', 'keyboard', 'cell_phone', 'microwave',
              'oven', 'toaster', 'sink', 'refrigerator', 'book', 'clock',
              'vase', 'scissors', 'teddy_bear', 'hair_drier', 'toothbrush']
model:
  architecture: GFL
  backbone:
    name: ResNet
    depth: 50
    out_stages: (1, 2, 3, 4)
    activation: ReLU
  neck:
    name: FPN
    in_channels: (256, 512, 1024, 2048)
    out_channels: 256
    start_level: 1
    add_extra_convs: True
    extra_convs_on_inputs: False  # use P5
    num_outs: 5
    relu_before_extra_convs: True
  task_head:
    name: GFocalV2Head
    num_classes: 80
    input_channel: 256
    feat_channels: 256
    stacked_convs: 4
    octave_base_scale: 8
    scales_per_octave: 1
    anchor_ratios: [1.0]
    anchor_strides: [8, 16, 32, 64, 128]
    target_means: [.0, .0, .0, .0]
    target_stds: [0.1, 0.1, 0.2, 0.2]
    reg_max: 16
    reg_topk: 4
    reg_channels: 64
    add_mean: True
    loss:
      loss_qfl:
        name: QualityFocalLoss
        use_sigmoid: True
        beta: 2.0
        loss_weight: 1.0
      loss_dfl:
        name: DistributionFocalLoss
        loss_weight: 0.25
      loss_bbox:
        name: GIoULoss
        loss_weight: 2.0
train:
  num_epochs: 12
  batchsize: 48
  num_workers: 32
  gpu_ids: [0,1,2,3]
  optimizer:
    name: SGD
    lr: 0.03 # for single gpu, batch size = 12
    momentum: 0.9
    weight_decay: 0.0001
  lr_schedule:
    name: multi_step
    milestones: [8,11]
  warmup:
    name: linear
    steps: 500
    ratio: 0.3
  val_intervals: 2
  val_type: coco_bbox
#  resume:
#  load_model: 
#val:

data:
  train:
    dataset_name: coco
    ann_path: /coco/annotations/instances_train2017.json
    img_path: /coco/train2017
    input_size: [1333,800] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: True
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False
    augmentation:
      random_flip:
        flip_ratio: 0.5
      resize: True # no extra augmentation

  val:
    dataset_name: coco
    ann_path: /coco/annotations/instances_val2017.json
    img_path: /coco/val2017
    input_size: [1333,800] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: True
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False

log:
  interval: 10
