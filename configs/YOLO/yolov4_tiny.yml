#Config File example
save_dir: /YOUR_FOLDER/YoloV4_Tiny
class_names: ['person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus',
              'train', 'truck', 'boat', 'traffic_light', 'fire_hydrant',
              'stop_sign', 'parking_meter', 'bench', 'bird', 'cat', 'dog',
              'horse', 'sheep', 'cow', 'elephant', 'bear', 'zebra', 'giraffe',
              'backpack', 'umbrella', 'handbag', 'tie', 'suitcase', 'frisbee',
              'skis', 'snowboard', 'sports_ball', 'kite', 'baseball_bat',
              'baseball_glove', 'skateboard', 'surfboard', 'tennis_racket',
              'bottle', 'wine_glass', 'cup', 'fork', 'knife', 'spoon', 'bowl',
              'banana', 'apple', 'sandwich', 'orange', 'broccoli', 'carrot',
              'hot_dog', 'pizza', 'donut', 'cake', 'chair', 'couch',
              'potted_plant', 'bed', 'dining_table', 'toilet', 'tv', 'laptop',
              'mouse', 'remote', 'keyboard', 'cell_phone', 'microwave',
              'oven', 'toaster', 'sink', 'refrigerator', 'book', 'clock',
              'vase', 'scissors', 'teddy_bear', 'hair_drier', 'toothbrush']
model:
  architecture: Yolo
  backbone:
    name: Yolov4Tiny
  neck:
    name: None
  task_head:
    name: YoloHead
    num_classes: 80
    in_channels: [512, 256]
train:
  num_epochs: 12
  batchsize: 48
  num_workers: 32
  gpu_ids: [0,1,2,3]
  optimizer:
    name: SGD
    lr: 0.03 # for single gpu, batch size = 12
    momentum: 0.9
    weight_decay: 0.0001
  lr_schedule:
    name: multi_step
    milestones: [8,11]
  warmup:
    name: linear
    steps: 500
    ratio: 0.3
  val_intervals: 2
  val_type: coco_bbox
#  resume:
  load_model: D:\Projects\ECO_NN\workspace\coco\YoloV4Tiny\tiny_yolov4.pth
#val:

data:
  train:
    dataset_name: coco
    ann_path: D:\Dataset\coco\annotations\instances_val2017.json
    img_path: D:\Dataset\coco\val2017
    input_size: [416,416] #[w,h]
    img_norm:
      mean: [0.0, 0.0, 0.0]
      std: [1., 1., 1.]
    resize_keep_ratio: True
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False
    augmentation:
      random_flip:
        flip_ratio: 0.5
      resize: True # no extra augmentation

  val:
    dataset_name: coco
    ann_path: D:\Dataset\coco\annotations\instances_val2017.json
    img_path: D:\Dataset\coco\val2017
    input_size: [416,416] #[w,h]
    img_norm:
      mean: [0.0, 0.0, 0.0]
      std: [1., 1., 1.]
    resize_keep_ratio: False
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False

log:
  interval: 10
