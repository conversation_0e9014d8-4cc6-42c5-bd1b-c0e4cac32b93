save_dir: D:\Projects\ECO_NN\workspace\coco\one2one_test
class_names: ['person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus',
              'train', 'truck', 'boat', 'traffic_light', 'fire_hydrant',
              'stop_sign', 'parking_meter', 'bench', 'bird', 'cat', 'dog',
              'horse', 'sheep', 'cow', 'elephant', 'bear', 'zebra', 'giraffe',
              'backpack', 'umbrella', 'handbag', 'tie', 'suitcase', 'frisbee',
              'skis', 'snowboard', 'sports_ball', 'kite', 'baseball_bat',
              'baseball_glove', 'skateboard', 'surfboard', 'tennis_racket',
              'bottle', 'wine_glass', 'cup', 'fork', 'knife', 'spoon', 'bowl',
              'banana', 'apple', 'sandwich', 'orange', 'broccoli', 'carrot',
              'hot_dog', 'pizza', 'donut', 'cake', 'chair', 'couch',
              'potted_plant', 'bed', 'dining_table', 'toilet', 'tv', 'laptop',
              'mouse', 'remote', 'keyboard', 'cell_phone', 'microwave',
              'oven', 'toaster', 'sink', 'refrigerator', 'book', 'clock',
              'vase', 'scissors', 'teddy_bear', 'hair_drier', 'toothbrush']
model:
  architecture: CenterNet
  backbone:
    name: ResNet
    depth: 50
    out_stages: (1, 2, 3, 4)
    activation: ReLU
  neck:
    name: DeconvFPN
    in_channels: (256, 512, 1024, 2048)
    out_channels: 128
    start_level: 0
    end_level: 1
    add_extra_convs: True
    num_outs: 1
  task_head:
    name: One2OneCenterHead
    input_channel: 128
    num_classes: 80
    head_conv: 64
    strides: [4]
    class_weight: 2.0
    giou_weight: 2.0
    l1_weight: 5.0
    loss:
      loss_cls:
        name: FocalLoss
      loss_bbox:
        name: L1Loss
      loss_giou:
        name: GIOULoss
train:
  num_epochs: 140
  batchsize: 48
  num_workers: 4
  gpu_ids: [0]
  optimizer:
    name: AdamW
    lr: 3.0e-4
    weight_decay: 0.0
  lr_schedule:
    name: multi_step
    milestones: [90,120]
  warmup:
    name: linear
    steps: 1000
    ratio: 0.01
  val_intervals: 100000
  val_type: coco_bbox
  resume:
#  load_model: 

data:
  train:
    dataset_name: coco
    ann_path: D:\Dataset\coco\annotations\instances_val2017.json
    img_path: D:\Dataset\coco\val2017
    input_size: [512,512] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: True
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False
    augmentation:
      random_flip:
        flip_ratio: 0.5
      ioa_random_crop:
        random_stretch: False
      color:
        brightness: [0.8, 1.2]
        saturation: [0.8, 1.2]
        color_shift: [0.8, 1.2]

  val:
    dataset_name: coco
    ann_path: D:\Dataset\coco\annotations\instances_val2017.json
    img_path: D:\Dataset\coco\val2017
    input_size: [512,512] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: True
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False

log:
  interval: 10