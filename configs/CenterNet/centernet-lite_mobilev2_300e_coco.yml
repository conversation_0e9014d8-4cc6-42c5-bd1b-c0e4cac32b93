#Config File example
save_dir: /YOUR_FOLDER/centernet-lite_mobilev2_300e_coco
class_names: ['person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus',
              'train', 'truck', 'boat', 'traffic_light', 'fire_hydrant',
              'stop_sign', 'parking_meter', 'bench', 'bird', 'cat', 'dog',
              'horse', 'sheep', 'cow', 'elephant', 'bear', 'zebra', 'giraffe',
              'backpack', 'umbrella', 'handbag', 'tie', 'suitcase', 'frisbee',
              'skis', 'snowboard', 'sports_ball', 'kite', 'baseball_bat',
              'baseball_glove', 'skateboard', 'surfboard', 'tennis_racket',
              'bottle', 'wine_glass', 'cup', 'fork', 'knife', 'spoon', 'bowl',
              'banana', 'apple', 'sandwich', 'orange', 'broccoli', 'carrot',
              'hot_dog', 'pizza', 'donut', 'cake', 'chair', 'couch',
              'potted_plant', 'bed', 'dining_table', 'toilet', 'tv', 'laptop',
              'mouse', 'remote', 'keyboard', 'cell_phone', 'microwave',
              'oven', 'toaster', 'sink', 'refrigerator', 'book', 'clock',
              'vase', 'scissors', 'teddy_bear', 'hair_drier', 'toothbrush']
model:
  architecture: CenterNet
  backbone:
    name: MobileNetV2
    input_channel: 32
    width_mult: 1.0
    out_stages: (6,)
    last_channel: 1280
    adaptive_channel: 2048
    activation: ReLU
  neck:
    name: DepthwiseDeconv
    activation: ReLU
    input_channel: (1280,)
  task_head:
    name: DepthwiseCenterNet
    activation: ReLU
    input_channel: 64
    num_classes: 80
    head_conv: 64
    strides: [4]
    norm_wh: True
    loss:
      loss_hm:
        name: CenterNetFocalLoss
        weight: 1.0
      loss_wh:
        name: L1Loss
        weight: 0.3
      loss_offset:
        name: L1Loss
        weight: 1.0
train:
  num_epochs: 300
  batchsize: 200
  num_workers: 16
  gpu_ids: [0,1,2,3]
  optimizer:
    name: Adam
    lr: 2.0e-4  #for single gpu, batch size = 50
    weight_decay: 3.0e-4
  lr_schedule:
    name: multi_step
    milestones: [250,280]
  warmup:
    name: linear
    steps: 500
    ratio: 0.3
  val_intervals: 10
  val_type: coco_bbox
#  resume:
#  load_model: 

data:
  train:
    dataset_name: coco
    ann_path: /coco/annotations/instances_train2017.json
    img_path: /coco/train2017
    input_size: [512,512] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: True
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False
    augmentation:
      random_flip:
        flip_ratio: 0.5
      ioa_random_crop:
        random_stretch: false
      color:
        brightness: [0.8, 1.2]
        saturation: [0.8, 1.4]
        color_shift: [0.8, 1.2]
        noise: [1, 1.1]
  val:
    dataset_name: coco
    ann_path: /coco/annotations/instances_val2017.json
    img_path: /coco/val2017
    input_size: [512,512] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: True
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False

log:
  interval: 10





#debug_config:
#  debug: 2
#  eval_oracle_hm: True
#  eval_oracle_dis: True
