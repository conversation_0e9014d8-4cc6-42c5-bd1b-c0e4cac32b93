# Average Precision  (AP) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = 0.314
# Average Precision  (AP) @[ IoU=0.50      | area=   all | maxDets=100 ] = 0.495
# Average Precision  (AP) @[ IoU=0.75      | area=   all | maxDets=100 ] = 0.329
# Average Precision  (AP) @[ IoU=0.50:0.95 | area= small | maxDets=100 ] = 0.124
# Average Precision  (AP) @[ IoU=0.50:0.95 | area=medium | maxDets=100 ] = 0.348
# Average Precision  (AP) @[ IoU=0.50:0.95 | area= large | maxDets=100 ] = 0.471
# Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=  1 ] = 0.280
# Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets= 10 ] = 0.441
# Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = 0.458
# Average Recall     (AR) @[ IoU=0.50:0.95 | area= small | maxDets=100 ] = 0.207
# Average Recall     (AR) @[ IoU=0.50:0.95 | area=medium | maxDets=100 ] = 0.502
# Average Recall     (AR) @[ IoU=0.50:0.95 | area= large | maxDets=100 ] = 0.698

save_dir: /YOUR_FOLDER/centernet_r101_140e_coco/
class_names: ['person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus',
              'train', 'truck', 'boat', 'traffic_light', 'fire_hydrant',
              'stop_sign', 'parking_meter', 'bench', 'bird', 'cat', 'dog',
              'horse', 'sheep', 'cow', 'elephant', 'bear', 'zebra', 'giraffe',
              'backpack', 'umbrella', 'handbag', 'tie', 'suitcase', 'frisbee',
              'skis', 'snowboard', 'sports_ball', 'kite', 'baseball_bat',
              'baseball_glove', 'skateboard', 'surfboard', 'tennis_racket',
              'bottle', 'wine_glass', 'cup', 'fork', 'knife', 'spoon', 'bowl',
              'banana', 'apple', 'sandwich', 'orange', 'broccoli', 'carrot',
              'hot_dog', 'pizza', 'donut', 'cake', 'chair', 'couch',
              'potted_plant', 'bed', 'dining_table', 'toilet', 'tv', 'laptop',
              'mouse', 'remote', 'keyboard', 'cell_phone', 'microwave',
              'oven', 'toaster', 'sink', 'refrigerator', 'book', 'clock',
              'vase', 'scissors', 'teddy_bear', 'hair_drier', 'toothbrush']
model:
  architecture: CenterNet
  backbone:
    name: ResNet
    depth: 101
    out_stages: (4,)
    activation: ReLU
  neck:
    name: Deconv
    input_channel: (2048,)
    activation: ReLU
  task_head:
    name: CenterNet
    input_channel: 64
    num_classes: 80
    head_conv: 64
    strides: [4]
    norm_wh: False
    activation: ReLU
    loss:
      loss_hm:
        name: CenterNetFocalLoss
        weight: 1.0
      loss_wh:
        name: L1Loss
        weight: 0.1
      loss_offset:
        name: L1Loss
        weight: 1.0
train:
  num_epochs: 140
  batchsize: 128
  num_workers: 64
  gpu_ids: [0,1,2,3]
  optimizer:
    name: Adam
    lr: 1.25e-4 #for single gpu, batch size = 32
    weight_decay: 5.0e-5
  lr_schedule:
    name: multi_step
    milestones: [90,120]
  warmup:
    name: linear
    steps: 500
    ratio: 0.3
  val_intervals: 10
  val_type: coco_bbox
#  resume:
#  load_model: 

data:
  train:
    dataset_name: coco
    ann_path: /coco/annotations/instances_train2017.json
    img_path: /coco/train2017
    input_size: [512,512] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: True
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False
    augmentation:
      random_flip:
        flip_ratio: 0.5
      ioa_random_crop:
        random_stretch: False
      color:
        brightness: [0.8, 1.2]
        saturation: [0.8, 1.2]
        color_shift: [0.8, 1.2]

  val:
    dataset_name: coco
    ann_path: /coco/annotations/instances_val2017.json
    img_path: /coco/val2017
    input_size: [512,512] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: True
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False

log:
  interval: 10