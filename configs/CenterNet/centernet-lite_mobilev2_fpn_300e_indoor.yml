#Config File example
save_dir: /raid/Indoor/Model/centernet-lite_mobilev2_fpn_indoor_4stride/
class_names: ['Pu<PERSON>ong<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>',
              '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
              '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
              '<PERSON><PERSON><PERSON>', 'uchair']
model:
  architecture: CenterNet
  backbone:
    name: MobileNetV2
    width_mult: 1.0
    out_stages: (1,2,4,6) # 4 16 32
    last_channel: 512
    activation: ReLU
  neck:
    name: FPN
    in_channels: (24, 32, 96, 512)
    out_channels: 64
    start_level: 0
    add_extra_convs: True
    extra_convs_on_inputs: False  # use P5
    num_outs: 4
    relu_before_extra_convs: True
  task_head:
    name: DepthwiseCenterNet
    activation: ReLU
    input_channel: 64
    num_classes: 11
    head_conv: 64
    strides: [4]
    norm_wh: True
    loss:
      loss_hm:
        name: CenterNetFocalLoss
        weight: 1.0
      loss_wh:
        name: L1Loss
        weight: 0.1
      loss_offset:
        name: L1Loss
        weight: 1.0
train:
  num_epochs: 300
  batchsize: 48
  num_workers: 16
  gpu_ids: [0,1] #[2,3]
  optimizer:
    name: SGD
    lr: 0.003 # 0.005
    momentum: 0.9
    weight_decay: 0.0001
#    name: Adam
#    lr: 2.0e-4  #for single gpu, batch size = 50
#    weight_decay: 3.0e-4
  lr_schedule:
   name: one_cycle
   max_factor: 1.0
   min_factor: 0.2
    # name: multi_step
    # milestones: [120,180]
  warmup:
    name: linear
    steps: 500
    ratio: 0.3
  val_intervals: 10
  val_type: coco_bbox
#  resume: /raid/Indoor/Model/centernet-lite_mobilev2_fpn_indoor/model_last.pth
#  load_model:

data:
  train:
    dataset_name: eco_indoor
    ann_path: /raid/Indoor/Data/eco_indoor/annotations/ecov5.4_instances_train2019.json
    img_path: /raid/Indoor/Data/eco_indoor/train2019
    input_size: [512,512] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: True
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False
    augmentation:
      random_flip:
        flip_ratio: 0.5
      ioa_random_crop:
        random_stretch: True
      color:
        brightness: [0.8, 1.2]
        saturation: [0.8, 1.4]
        color_shift: [0.8, 1.2]
        noise: [1, 1.1]
  val:
    dataset_name: eco_indoor
    ann_path: /raid/Indoor/Data/eco_indoor/annotations/ecov5.4_instances_val2019.json
    img_path: /raid/Indoor/Data/eco_indoor/val2019
    input_size: [512,512] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: False
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False

log:
  interval: 10





#debug_config:
#  debug: 2
#  eval_oracle_hm: True
#  eval_oracle_dis: True
