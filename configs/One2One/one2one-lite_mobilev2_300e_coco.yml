save_dir: D:\Projects\ECO_NN\workspace\coco\one2one_lite_test
class_names: ['person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus',
              'train', 'truck', 'boat', 'traffic_light', 'fire_hydrant',
              'stop_sign', 'parking_meter', 'bench', 'bird', 'cat', 'dog',
              'horse', 'sheep', 'cow', 'elephant', 'bear', 'zebra', 'giraffe',
              'backpack', 'umbrella', 'handbag', 'tie', 'suitcase', 'frisbee',
              'skis', 'snowboard', 'sports_ball', 'kite', 'baseball_bat',
              'baseball_glove', 'skateboard', 'surfboard', 'tennis_racket',
              'bottle', 'wine_glass', 'cup', 'fork', 'knife', 'spoon', 'bowl',
              'banana', 'apple', 'sandwich', 'orange', 'broccoli', 'carrot',
              'hot_dog', 'pizza', 'donut', 'cake', 'chair', 'couch',
              'potted_plant', 'bed', 'dining_table', 'toilet', 'tv', 'laptop',
              'mouse', 'remote', 'keyboard', 'cell_phone', 'microwave',
              'oven', 'toaster', 'sink', 'refrigerator', 'book', 'clock',
              'vase', 'scissors', 'teddy_bear', 'hair_drier', 'toothbrush']
model:
  architecture: FCOS
  backbone:
    name: MobileNetV2
    input_channel: 32
    width_mult: 1.0
    out_stages: (2,4,6)
    last_channel: 512
    activation: ReLU
  neck:
    name: DeconvFPN
    in_channels: (32,96,512)
    out_channels: 128
    start_level: 0
    add_extra_convs: True
    extra_convs_on_inputs: False
    num_outs: 3
    relu_before_extra_convs: True
#    activation: ReLU
  task_head:
    name: LiteOne2OneHead
    num_classes: 80
    input_channel: 128
    feat_channels: 64
    stacked_convs: 2
    strides: [8, 16, 32]
    loss:
      loss_cls:
        name: FocalLoss
        gamma: 2.0
        alpha: 0.25
        loss_weight: 2.0
      loss_bbox:
        name: L1Loss
        loss_weight: 5.0
      loss_iou:
        name: GIoULoss
        loss_weight: 2.0
train:
  num_epochs: 300
  batchsize: 64
  num_workers: 4
  gpu_ids: [0]
  optimizer:
    name: AdaBelief
    lr: 5.0e-5
    betas: [0.9, 0.999]
    eps: 1.0e-8
    weight_decay: 0.0001
  lr_schedule:
    name: multi_step
    milestones: [240,280]
  warmup:
    name: linear
    steps: 1000
    ratio: 0.01
  val_intervals: 100000
  val_type: coco_bbox
#  resume:
#  load_model: 

data:
  train:
    dataset_name: coco
    ann_path: D:\Dataset\coco\annotations\instances_val2017.json
    img_path: D:\Dataset\coco\val2017
    input_size: [320,320] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: True
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False
    augmentation:
      random_flip:
        flip_ratio: 0.5
      ioa_random_crop:
        random_stretch: False
      color:
        brightness: [0.8, 1.2]
        saturation: [0.8, 1.2]
        color_shift: [0.8, 1.2]

  val:
    dataset_name: coco
    ann_path: D:\Dataset\coco\annotations\instances_val2017.json
    img_path: D:\Dataset\coco\val2017
    input_size: [320,320] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: True
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False

log:
  interval: 10