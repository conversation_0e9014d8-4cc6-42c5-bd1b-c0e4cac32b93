# Average Precision  (AP) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = 0.389
# Average Precision  (AP) @[ IoU=0.50      | area=   all | maxDets=100 ] = 0.582
# Average Precision  (AP) @[ IoU=0.75      | area=   all | maxDets=100 ] = 0.421
# Average Precision  (AP) @[ IoU=0.50:0.95 | area= small | maxDets=100 ] = 0.240
# Average Precision  (AP) @[ IoU=0.50:0.95 | area=medium | maxDets=100 ] = 0.422
# Average Precision  (AP) @[ IoU=0.50:0.95 | area= large | maxDets=100 ] = 0.495
# Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=  1 ] = 0.327
# Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets= 10 ] = 0.555
# Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = 0.598
# Average Recall     (AR) @[ IoU=0.50:0.95 | area= small | maxDets=100 ] = 0.412
# Average Recall     (AR) @[ IoU=0.50:0.95 | area=medium | maxDets=100 ] = 0.628
# Average Recall     (AR) @[ IoU=0.50:0.95 | area= large | maxDets=100 ] = 0.760

save_dir: D:\Projects\ECO_NN\workspace\coco\one2one_fcos_test
class_names: ['person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus',
              'train', 'truck', 'boat', 'traffic_light', 'fire_hydrant',
              'stop_sign', 'parking_meter', 'bench', 'bird', 'cat', 'dog',
              'horse', 'sheep', 'cow', 'elephant', 'bear', 'zebra', 'giraffe',
              'backpack', 'umbrella', 'handbag', 'tie', 'suitcase', 'frisbee',
              'skis', 'snowboard', 'sports_ball', 'kite', 'baseball_bat',
              'baseball_glove', 'skateboard', 'surfboard', 'tennis_racket',
              'bottle', 'wine_glass', 'cup', 'fork', 'knife', 'spoon', 'bowl',
              'banana', 'apple', 'sandwich', 'orange', 'broccoli', 'carrot',
              'hot_dog', 'pizza', 'donut', 'cake', 'chair', 'couch',
              'potted_plant', 'bed', 'dining_table', 'toilet', 'tv', 'laptop',
              'mouse', 'remote', 'keyboard', 'cell_phone', 'microwave',
              'oven', 'toaster', 'sink', 'refrigerator', 'book', 'clock',
              'vase', 'scissors', 'teddy_bear', 'hair_drier', 'toothbrush']
model:
  architecture: FCOS
  backbone:
    name: ResNet
    depth: 50
    out_stages: (1, 2, 3, 4)
    activation: ReLU
  neck:
    name: FPN
    in_channels: (256, 512, 1024, 2048)
    out_channels: 256
    start_level: 1
    add_extra_convs: True
    extra_convs_on_inputs: False  # use P5
    num_outs: 5
    relu_before_extra_convs: True
  task_head:
    name: One2OneFCOSHead
    num_classes: 80
    input_channel: 256
    feat_channels: 256
    stacked_convs: 4
    strides: [8, 16, 32, 64, 128]
    loss:
      loss_cls:
        name: FocalLoss
        gamma: 2.0
        alpha: 0.25
        loss_weight: 2.0
      loss_bbox:
        name: L1Loss
        loss_weight: 5.0
      loss_iou:
        name: GIoULoss
        loss_weight: 2.0
train:
  num_epochs: 240
  batchsize: 1
  num_workers: 4
  gpu_ids: [0]
  optimizer:
    name: AdamW
    lr: 3.0e-4
    weight_decay: 0.0
  lr_schedule:
    name: multi_step
    milestones: [150,190]
  warmup:
    name: linear
    steps: 1000
    ratio: 0.01
  val_intervals: 100000
  val_type: coco_bbox
#  resume:
#  load_model: 

data:
  train:
    dataset_name: coco
    ann_path: D:\Dataset\coco\annotations\instances_val2017.json
    img_path: D:\Dataset\coco\val2017
    input_size: [1333,800] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: True
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False
    augmentation:
      random_flip:
        flip_ratio: 0.5
      ioa_random_crop:
        random_stretch: False
      color:
        brightness: [0.8, 1.2]
        saturation: [0.8, 1.2]
        color_shift: [0.8, 1.2]

  val:
    dataset_name: coco
    ann_path: D:\Dataset\coco\annotations\instances_val2017.json
    img_path: D:\Dataset\coco\val2017
    input_size: [1333,800] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: True
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False

log:
  interval: 10