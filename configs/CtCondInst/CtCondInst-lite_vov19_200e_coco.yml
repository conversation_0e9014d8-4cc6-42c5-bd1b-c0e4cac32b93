save_dir: /YOUR_FOLDER/CtCondInst-lite_vov19_coco
class_names: ['person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus',
              'train', 'truck', 'boat', 'traffic_light', 'fire_hydrant',
              'stop_sign', 'parking_meter', 'bench', 'bird', 'cat', 'dog',
              'horse', 'sheep', 'cow', 'elephant', 'bear', 'zebra', 'giraffe',
              'backpack', 'umbrella', 'handbag', 'tie', 'suitcase', 'frisbee',
              'skis', 'snowboard', 'sports_ball', 'kite', 'baseball_bat',
              'baseball_glove', 'skateboard', 'surfboard', 'tennis_racket',
              'bottle', 'wine_glass', 'cup', 'fork', 'knife', 'spoon', 'bowl',
              'banana', 'apple', 'sandwich', 'orange', 'broccoli', 'carrot',
              'hot_dog', 'pizza', 'donut', 'cake', 'chair', 'couch',
              'potted_plant', 'bed', 'dining_table', 'toilet', 'tv', 'laptop',
              'mouse', 'remote', 'keyboard', 'cell_phone', 'microwave',
              'oven', 'toaster', 'sink', 'refrigerator', 'book', 'clock',
              'vase', 'scissors', 'teddy_bear', 'hair_drier', 'toothbrush']
model:
  architecture: CtCondInst
  backbone:
    name: VoVNet
    conv_body: V-19-eSE
    out_features: ['stage2', 'stage3', 'stage4', 'stage5']
  neck:
    name: DepthwiseShortcutDeconv
    in_channels: (256, 512, 768, 1024,)
    deconv_channels: (256, 128, 128)
    activation: ReLU
  task_head:
    name: Depthwise_CtCondInst
    in_channels: 128
    num_classes: 80
    head_conv: 64
    strides: [4]
    loss:
      loss_hm:
        name: CenterNetFocalLoss
        weight: 1.0
      loss_mask:
        name: DiceLoss
        weight: 5.0
    activation: ReLU
train:
  num_epochs: 200
  batchsize: 200
  num_workers: 32
  gpu_ids: [0,1,2,3]
  optimizer:
    name: Adam
    lr: 2.5e-4 # for single gpu, batch size = 50
    weight_decay: 5.0e-5
  lr_schedule:
    name: multi_step
    milestones: [120,180]
  warmup:
    name: linear
    steps: 500
    ratio: 0.3
  val_intervals: 10
  val_type: coco_instance
#  resume:
#  load_model: 

data:
  train:
    dataset_name: coco
    ann_path: /coco/annotations/instances_train2017.json
    img_path: /coco/train2017
    input_size: [512,512] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: True
    with_crowd: False
    with_mask: True
    with_keypoints: False
    skip_img_without_anno: False
    augmentation:
      random_flip:
        flip_ratio: 0.5
      ioa_random_crop:
        random_stretch: False
      color:
        brightness: [0.8, 1.2]
        saturation: [0.8, 1.2]
        color_shift: [0.8, 1.2]

  val:
    dataset_name: coco
    ann_path: /coco/annotations/instances_val2017.json
    img_path: /coco/val2017
    input_size: [512,512] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: True
    with_crowd: False
    with_mask: True
    with_keypoints: False
    skip_img_without_anno: False

log:
  interval: 10

