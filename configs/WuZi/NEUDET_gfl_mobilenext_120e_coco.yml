#Config File example
save_dir: /raid/WuZi/runs/gfl_Stage3_0628/exp/
class_names: ['wuzi'] #['crazing','inclusion','patches','pitted_surface','rolled-in_scale','scratches']
model:
  architecture: GFL
#  backbone:
#    name: CSPNet
#    depth_multiple: 0.33
#    width_multiple: 0.50
#    out_stages: [4,6,9]
  backbone:
    name: MobileNeXt
    width_mult: 1.0
    identity_tensor_multiplier: 1.0
    out_stages: (2,4,7)
    last_channel: 1280
    activation: ReLU
  neck:
    name: FPN
    in_channels: (192, 384, 1280) #(128,256,512)
    out_channels: 64
    start_level: 0
    add_extra_convs: True
    extra_convs_on_inputs: False  # use P5
    num_outs: 4
    relu_before_extra_convs: True
  task_head:
    name: LiteGFLHead
    num_classes: 1
    input_channel: 64
    feat_channels: 64
    stacked_convs: 4
    octave_base_scale: 8
    scales_per_octave: 1
    anchor_ratios: [1.0]
    anchor_strides: [8, 16, 32]
    target_means: [.0, .0, .0, .0]
    target_stds: [0.1, 0.1, 0.2, 0.2]
    reg_max: 16
    norm_cfg:
      type: BN
    loss:
      loss_qfl:
        name: QualityFocalLoss
        use_sigmoid: True
        beta: 2.0
        loss_weight: 1.0
      loss_dfl:
        name: DistributionFocalLoss
        loss_weight: 0.25
      loss_bbox:
        name: GIoULoss
        loss_weight: 2.0
train:
  num_epochs: 200
  batchsize: 16
  num_workers: 8
  gpu_ids: [2]
  optimizer:
    name: SGD
    lr: 0.002
    momentum: 0.9
    weight_decay: 0.0001
  lr_schedule:
    name: one_cycle
    max_factor: 1.0
    min_factor: 0.2
#  lr_schedule:
#    name: multi_step
#    factor: 0.1
#    milestones: [80,110]
  warmup:
    name: linear
    steps: 500 #500
    ratio: 0.3
  val_intervals: 5
  val_type: coco_bbox
  ema: False
  load_model: /raid/WuZi/runs/gfl_mobilenext_200e_wuzi_relabel_2/model_best_score/model_best_score.pth
#  resume:

data:
  train:
    dataset_name: coco
    ann_path: /raid/WuZi/Data/Detectioin/Stage2_640_0615_Stage3_0628/CocoAnno/train.json
    img_path: '/raid/WuZi/Data/Detectioin/'
    input_size: [1280,320] #[800, 128] #[w,h]
    img_norm:
      mean: [0, 0, 0] #[0.408, 0.447, 0.470]
      std: [1, 1, 1] #[0.289, 0.274, 0.278]
    resize_keep_ratio: True
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False
    augmentation:
      random_flip:
        flip_ratio: 0.5
      ioa_random_crop:
        random_stretch: False
      color:
        brightness: [0.8, 1.2]
        saturation: [0.8, 1.2]
        color_shift: [0.8, 1.2]

  val:
    dataset_name: coco
    ann_path: /raid/WuZi/Data/Detectioin/wuzi_ReLabel/CocoAnno/val.json #/raid/WuZi/Data/Detectioin/Stage2_640_0615_Stage3_0628/CocoAnno/val.json
    img_path: '/raid/WuZi/Data/Detectioin/'
    input_size: [1280,320] #[w,h]
    img_norm:
      mean:  [0.408, 0.447, 0.470] #[0, 0, 0]
      std:  [0.289, 0.274, 0.278] #[1, 1, 1]
    resize_keep_ratio: True
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False

log:
  interval: 5
