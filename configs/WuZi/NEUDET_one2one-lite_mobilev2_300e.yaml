save_dir: /raid/WuZi/econn_train/one2one-lite_mobilenetv2_120e_coco/exp1
class_names: ['crazing','inclusion','patches','pitted_surface','rolled-in_scale','scratches']
model:
  architecture: FCOS
  backbone:
    name: MobileNetV2
    width_mult: 1.0
    out_stages: (2,4,6)
    last_channel: 512
    activation: ReLU
  neck:
    name: DeconvFPN
    in_channels: (32,96,512)
    out_channels: 128
    start_level: 0
    add_extra_convs: True
    extra_convs_on_inputs: False
    num_outs: 3
    relu_before_extra_convs: True
#    activation: ReLU
  task_head:
    name: LiteOne2OneHead
    num_classes: 6
    input_channel: 128
    feat_channels: 64
    stacked_convs: 2
    strides: [8, 16, 32]
    loss:
      loss_cls:
        name: FocalLoss
        gamma: 2.0
        alpha: 0.25
        loss_weight: 2.0
      loss_bbox:
        name: L1Loss
        loss_weight: 5.0
      loss_iou:
        name: GIoULoss
        loss_weight: 2.0
train:
  num_epochs: 300
  batchsize: 16
  num_workers: 4
  gpu_ids: [0]
  optimizer:
    name: <PERSON><PERSON><PERSON><PERSON>
    lr: 0.001 #5.0e-5
    betas: [0.9, 0.999]
    eps: 1.0e-8
    weight_decay: 0.0001
  lr_schedule:
    name: multi_step
    milestones: [240,280]
  warmup:
    name: linear
    steps: 1000
    ratio: 0.01
  val_intervals: 2
  val_type: coco_bbox
#  resume:
#  load_model: 

data:
  train:
    dataset_name: neudet
    ann_path: /raid/WuZi/COCO_NEUDET/train.json
    img_path: ''
    input_size: [640,640] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: True
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False
    augmentation:
      random_flip:
        flip_ratio: 0.5
      ioa_random_crop:
        random_stretch: False
      color:
        brightness: [0.8, 1.2]
        saturation: [0.8, 1.2]
        color_shift: [0.8, 1.2]

  val:
    dataset_name: neudet
    ann_path: /raid/WuZi/COCO_NEUDET/val.json
    img_path: ''
    input_size: [640,640] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: True
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False

log:
  interval: 5