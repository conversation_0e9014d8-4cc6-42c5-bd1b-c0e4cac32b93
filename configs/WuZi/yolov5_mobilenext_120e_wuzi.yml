#Config File example
save_dir: /data/workspace/stainsDetection/yolov5_mobilenext_120e_wuzi
class_names: ['wuzi']
model:
  architecture: GFL
  backbone:
    name: MobileNeXt
    width_mult: 1.0
    identity_tensor_multiplier: 1.0
    out_stages: (2,4,7)
    last_channel: 1280
    activation: ReLU
  neck:
    name: FPN
    in_channels: (192, 384, 1280)
    out_channels: 64
    start_level: 0
    add_extra_convs: True
    extra_convs_on_inputs: False  # use P5
    num_outs: 3
    relu_before_extra_convs: True
  task_head:
    name: Yolov5Head
    num_classes: 1
    anchors: [[30,61, 39,121, 77,73],
              [94,124, 245,52, 51,272],
              [205,115, 113,241, 277,285]]
    in_channels: [64,64,64]
    strides: [8, 16, 32]
    anchor_t: 4
    ann_path: /data/datasets/wuzi/coco_ann/annotations/train.json
    input_size_w: 640 # 长边尺寸

#    stacked_convs: 4
#    octave_base_scale: 8
#    scales_per_octave: 1
#    anchor_ratios: [1.0]
#    anchor_strides: [8, 16, 32]
#    target_means: [.0, .0, .0, .0]
#    target_stds: [0.1, 0.1, 0.2, 0.2]
#    reg_max: 16
#    norm_cfg:
#      type: BN
#    loss:
#      loss_qfl:
#        name: QualityFocalLoss
#        use_sigmoid: True
#        beta: 2.0
#        loss_weight: 1.0
#      loss_dfl:
#        name: DistributionFocalLoss
#        loss_weight: 0.25
#      loss_bbox:
#        name: GIoULoss
#        loss_weight: 2.0
train:
  num_epochs: 200
  batchsize: 4
  num_workers: 1
  gpu_ids: [0]
  optimizer:
    name: SGD
    lr: 0.005 # 0.005
    momentum: 0.9
    weight_decay: 0.0001
  lr_schedule:
    name: multi_step
    milestones: [80,120]
  warmup:
    name: linear
    steps: 300 # 500
    ratio: 0.3
  val_intervals: 5
  val_type: coco_bbox
#  resume:

data:
  train:
    dataset_name: wuzi
    ann_path: /data/datasets/wuzi/coco_ann/annotations/train.json
    img_path: /data/datasets/wuzi
    input_size: [ 640,100 ] #[w,h]
    img_norm:
      mean: [ 0.408, 0.447, 0.470 ]
      std: [ 0.289, 0.274, 0.278 ]
    resize_keep_ratio: True
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False
    augmentation:
      random_flip:
        flip_ratio: 0.5
      ioa_random_crop:
        random_stretch: False
      color:
        brightness: [0.8, 1.2]
        saturation: [0.8, 1.2]
        color_shift: [0.8, 1.2]

  val:
    dataset_name: wuzi
    ann_path: /data/datasets/wuzi/coco_ann/annotations/val.json
    img_path: /data/datasets/wuzi
    input_size: [ 640,100 ] #[w,h]
    img_norm:
      mean: [ 0.408, 0.447, 0.470 ]
      std: [ 0.289, 0.274, 0.278 ]
    resize_keep_ratio: True
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False

log:
  interval: 5
