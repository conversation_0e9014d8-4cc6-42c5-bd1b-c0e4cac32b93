#Config File example
save_dir: /raid/Indoor/Model/
class_names: ['crazing','inclusion','patches','pitted_surface','rolled-in_scale','scratches']
model:
  architecture: CenterNet
  backbone:
    name: MobileNetV2
    width_mult: 1.0
    out_stages: (6,)
    last_channel: 1280
    adaptive_channel: 2048
    activation: ReLU
  neck:
    name: DepthwiseDeconv
    activation: ReLU
    input_channel: (1280,)
  task_head:
    name: DepthwiseCenterNet
    activation: ReLU
    input_channel: 64
    num_classes: 6
    head_conv: 64
    strides: [4]
    norm_wh: True
    loss:
      loss_hm:
        name: CenterNetFocalLoss
        weight: 1.0
      loss_wh:
        name: L1Loss
        weight: 0.3
      loss_offset:
        name: L1Loss
        weight: 1.0
train:
  num_epochs: 300
  batchsize: 16
  num_workers: 16
  gpu_ids: [1]
  optimizer:
    name: Adam
    lr: 2.0e-5  #for single gpu, batch size = 50,default=2.0e-4
    weight_decay: 3.0e-4
  lr_schedule:
    name: multi_step
    milestones: [250,280]
  warmup:
    name: linear
    steps: 500
    ratio: 0.3
  val_intervals: 10
  val_type: coco_bbox
#  resume:
#  load_model: 

data:
  train:
    dataset_name: neudet
    ann_path: /raid/WuZi/COCO_NEUDET/train.json
    img_path: ''
    input_size: [640,640] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: True
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False
    augmentation:
      random_flip:
        flip_ratio: 0.5
      ioa_random_crop:
        random_stretch: false
      color:
        brightness: [0.8, 1.2]
        saturation: [0.8, 1.4]
        color_shift: [0.8, 1.2]
        noise: [1, 1.1]
  val:
    dataset_name: neudet
    ann_path: /raid/WuZi/COCO_NEUDET/val.json
    img_path: ''
    input_size: [640,640] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: True
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False

log:
  interval: 5





#debug_config:
#  debug: 2
#  eval_oracle_hm: True
#  eval_oracle_dis: True
