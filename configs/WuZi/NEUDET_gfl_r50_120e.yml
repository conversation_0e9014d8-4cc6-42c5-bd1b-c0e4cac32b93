
save_dir: /raid/WuZi/econn_train/gfl_r50_12e_coco/exp1
class_names: ['crazing','inclusion','patches','pitted_surface','rolled-in_scale','scratches']
model:
  architecture: GFL
  backbone:
    name: ResNet
    depth: 50
    out_stages: (1, 2, 3, 4)
    activation: ReLU
  neck:
    name: FPN
    in_channels: (256, 512, 1024, 2048)
    out_channels: 256
    start_level: 1
    add_extra_convs: True
    extra_convs_on_inputs: False  # use P5
    num_outs: 5
    relu_before_extra_convs: True
  task_head:
    name: GFLHead
    num_classes: 6
    input_channel: 256
    feat_channels: 256
    stacked_convs: 4
    octave_base_scale: 8
    scales_per_octave: 1
    anchor_ratios: [1.0]
    anchor_strides: [8, 16, 32, 64, 128]
    target_means: [.0, .0, .0, .0]
    target_stds: [0.1, 0.1, 0.2, 0.2]
    reg_max: 16
    loss:
      loss_qfl:
        name: QualityFocalLoss
        use_sigmoid: True
        beta: 2.0
        loss_weight: 1.0
      loss_dfl:
        name: DistributionFocalLoss
        loss_weight: 0.25
      loss_bbox:
        name: GIoULoss
        loss_weight: 2.0
train:
  num_epochs: 200
  batchsize: 8
  num_workers: 8
  gpu_ids: [1]
  optimizer:
    name: SGD
    lr: 0.005 # for single gpu, batch size = 12
    momentum: 0.9
    weight_decay: 0.0001
  lr_schedule:
    name: multi_step
    milestones: [8,11]
  warmup:
    name: linear
    steps: 500
    ratio: 0.3
  val_intervals: 2
  val_type: coco_bbox
#  resume:
  load_model: '/raid/WuZi/econn_train/gfl_r50_12e_coco/exp1/model_best_score/model_best_score.pth'
#val:

data:
  train:
    dataset_name: neudet
    ann_path: /raid/WuZi/COCO_NEUDET/train.json
    img_path: ''
    input_size: [640,640] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: True
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False
    augmentation:
      random_flip:
        flip_ratio: 0.5
      resize: True # no extra augmentation

  val:
    dataset_name: neudet
    ann_path: /raid/WuZi/COCO_NEUDET/val.json
    img_path: ''
    input_size: [640,640] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: True
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False

log:
  interval: 5
