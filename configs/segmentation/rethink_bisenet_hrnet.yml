#Config File example
save_dir: ../workspace/lz_seg/
class_names: ['background',  'rug']

model:
  architecture: Bisenet
  backbone:
    name: HrNet
    depth: 32    #1
    out_stages: (1, 2, 3, 4)
    activation: ReLU
    load_from: '/home/<USER>/.cache/torch/checkpoints/hrnetv2_w32_imagenet_pretrained.pth'
    deploy: False
  # backbone:
  #   name: create_RepVGG_A2
  #   out_stages: (0, 1, 2, 3, 4)
  #   activation: ReLU
  #   # load_from: ''
  #   load_from: 'pretrained_models/RepVGG-A2-train.pth'
    # deploy: False
  neck:
    name: None      
    # name: FPN
    # in_channels: (384, 1408)
    # out_channels: 256
    # start_level: 0
    # add_extra_convs: True
    # extra_convs_on_inputs: False  # use P5
    # num_outs: 2
    # relu_before_extra_convs: True
  task_head:
    name: RethinkBiSeNetHrNetHead
#    input_channel: 64
    num_classes: 2
    aux: False
    in_channels: (256, 256, 128)
    loss:
      loss1:
        name: RMILOSS
        loss_weight: 1.0
        aux_weight: 0.4
#      loss2:
#        name: FocalLoss
#        loss_weight: 0.5
#        aux_weight: 0.4
train:
  use_amp: False
  num_epochs: 300
  batchsize: 2
  num_workers: 2
  gpu_ids: [0]
  optimizer:
    name: SGD
    lr: 0.01
    lr_separate:
      backbone: 0.01 #单独配置lr
      # neck: 0.1
      task_head: 0.01 #单独配置lr
    weight_decay: 5.0e-4
    momentum: 0.9
  lr_schedule:
    name: Poly
    power: 0.9
    milestones: [50, 100]
  warmup:
    name: linear
    steps: 0 #TODO
    ratio: 0.3
  val_intervals: 1
  val_type: coco_stuff
  
  # resume: /raid/lz/code_results/econn/work_dirs/rethink_bisenet_hrnet_jiaju_keliwu_bak_copypaste13360/model_best_score/model_best_score.pth
  # load_model: /raid/lz/code_results/econn/work_dirs/rethink_bisenet_hrnet_keliwu_server_quantized_epoch_multi/model_best_score/model_best_score.pth
#val:

data:
  train:
    dataset_name: eco_semantic_indoor
    ann_path: /data/Dataset/eco_rug_20201229_v1
    img_path: /data/Dataset/eco_rug_20201229_v1
    input_size: [512,512] #[w,h]
    base_size: 720
    valid_ids: [0, 2]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: False
    with_crowd: False
    with_mask: False
    skip_img_without_anno: False
    with_keypoints: False
#    augmentation:
#      random_flip:
#        flip_ratio: 0.5
#      ioa_random_crop:
#        random_stretch: False
#      color:
#        brightness: (0.5, 1.5)
#        saturation: (0.5, 1.2)
#        color_shift: (0.8, 1.2)

  val:
    dataset_name: eco_semantic_indoor
    ann_path: /data/Dataset/eco_rug_20201229_v1
    img_path: /data/Dataset/eco_rug_20201229_v1
    input_size: [512, 512] #[w,h]
    base_size: 720
    valid_ids: [0, 2]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: False
    #TODO
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False
  test:
    dataset_name: eco_semantic_indoor
    ann_path: /data/Dataset/eco_rug_20201229_v1
    img_path: /data/Dataset/eco_rug_20201229_v1
    input_size: [512, 512] #[w,h]
    base_size: 720
    valid_ids: [0, 2]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: False
    #TODO
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False
log:
  interval: 100



