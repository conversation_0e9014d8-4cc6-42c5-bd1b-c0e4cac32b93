#Config File example
save_dir: D:\Projects\ECO_NN\workspace\shufflenet_centernet_test
class_names: ['PuTong<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>',
              '<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
              '<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
              '<PERSON><PERSON><PERSON>', 'uchair']
model:
  architecture: CenterNet
  backbone:
    name: ShuffleNetV2
    model_size: 1.5x
    out_stages: (4,)
    with_last_conv: true
    activation: ReLU
  neck:
    name: DepthwiseDeconv
    input_channel: (1024,)
    activation: ReLU
  task_head:
    name: DepthwiseCenterNet
    input_channel: 64
    num_classes: 11
    head_conv: 64
    strides: [4]
    norm_wh: False
    activation: ReLU
    loss:
      loss_hm:
        name: CenterNetFocalLoss
        weight: 1.0
      loss_wh:
        name: L1Loss
        weight: 0.1
      loss_offset:
        name: L1Loss
        weight: 1.0
train:
  num_epochs: 240
  batchsize: 28
  num_workers: 4
  gpu_ids: [0]
  optimizer:
    name: <PERSON>
    lr: 1.0e-4
    weight_decay: 5.0e-5
  lr_schedule:
    name: multi_step
    milestones: [120,220]
  warmup:
    name: linear
    steps: 500
    ratio: 0.3
  val_intervals: 5
  val_type: coco_bbox
#  resume:
#  load_model: D:\Projects\ECO_NN\workspace\model_last.pth
#val:

data:
  train:
    dataset_name: eco_indoor
    ann_path: D:\Dataset\eco_indoor_2020\annotations\ecov5.5_instances_val2019.json
    img_path: D:\Dataset\eco_indoor_2020\val2019
    input_size: [320,320] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: False
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False
    augmentation:
      random_flip:
        flip_ratio: 0.5
      ioa_random_crop:
        random_stretch: False
      color:
        brightness: [0.8, 1.2]
        saturation: [0.8, 1.4]
        color_shift: [0.8, 1.2]
        noise: [1, 1.1]
  val:
    dataset_name: eco_indoor
    ann_path: D:\Dataset\eco_indoor_2020\annotations\ecov5.5_instances_val2019.json
    img_path: D:\Dataset\eco_indoor_2020\val2019
    input_size: [320,320] #[w,h]
    img_norm:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
    resize_keep_ratio: False
    with_crowd: False
    with_mask: False
    with_keypoints: False
    skip_img_without_anno: False

log:
  interval: 10








#debug_config:
#  debug: 2
#  eval_oracle_hm: True
#  eval_oracle_dis: True
