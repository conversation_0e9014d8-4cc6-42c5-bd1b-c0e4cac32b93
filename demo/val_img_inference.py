"""
Create by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
2020/06/21
"""

from tools.inference import Detector, MultiTaskDetector
from econn.utils.config import cfg, update_config
import cv2
import numpy as np
import os
import torch
import shutil
from glob import glob

# demo = r"D:\Videos\YDXJ0175_0226.mp4"
# demo = r"D:\Dataset\coco\val2017"
# demo = r'D:\Dataset\eco_indoor_2020_old\val2019'
# demo = r"/data/datasets/2021-06-24-15-53cut/2021_06_07_17_48_22_02.mp4_0"
# demo = r"/data/datasets/wuzi/Stage2_640/images/"
#/raid/WuZi/Data/Detectioin/Stage3_0628/ImageSets/Main/val.txt"
# demo = r"/data/datasets/wuzi/wuzi1/train.txt"
# demo = r'D:\Pictures\uchair'
# demo = 'webcam'
camera_id = 0


# config_path = r"/raid/Indoor/Model/eco_ground_model/t10/eco_indoor_MobileNeXt_GFL_20200831_X3_finalmodel/train_config.yml"
# model_path = r"/raid/Indoor/Model/eco_ground_model/t10/eco_indoor_MobileNeXt_GFL_20200831_X3_finalmodel/model_best_score/model_best_score.pth"
# config_path = r"/raid/Indoor/Model/CenterNet512_20210811/mobilenextdwdeconv_centernet_512_202100727.yml"
# model_path = r"/raid/Indoor/Model/CenterNet512_20210811/model_best_score/model_best_score.pth"
config_path = r"/raid/Indoor/Model/GFL512_20210816/train_config.yml"
model_path = r"/raid/Indoor/Model/GFL512_20210816/model_best_score/model_best_score.pth"

#save_dir = '/'.join(model_path.split('/')[:-2]) + '/inference/'
#os.makedirs(save_dir, exist_ok=True)
#dir_num = len(os.listdir(save_dir))
#save_dir = save_dir+'/exp%s/' % dir_num
# demo = r"/raid/Indoor/Data/TestData/T9AI/17/"
# save_dir = '/raid/Indoor/Data/DetectedData/T9AI/17/'
# print("Current dir: ",demo)
# os.makedirs(save_dir)

update_config(cfg, config_path)

class_names = cfg.class_names
if cfg.model.architecture == 'MultiTask':
    detector = MultiTaskDetector(cfg, model_path, device='cuda:0')
else:
    detector = Detector(cfg, model_path, 'cuda:0')


def read_map(filepath):
    f = open(filepath)
    map = np.fromfile(f, dtype=np.float32)
    return map


# mapx = read_map(r"D:\mapx\186\mapx")
# mapx = np.reshape(mapx, (720, 1280))
# mapy = read_map(r"D:\mapx\186\mapy")
# mapy = np.reshape(mapy, (720, 1280))

torch.backends.cudnn.enabled = True
torch.backends.cudnn.benchmark = True

image_ext = ['.jpg', '.jpeg', '.webp', '.bmp', '.png']
video_ext = ['mp4', 'mov', 'avi', 'mkv']
dirs = r"/raid/Indoor/Data/TestData/T9AI/"
dirs = glob(dirs+'*')
dirs.sort()
print(dirs,'<'*50)
idx = int(input('Family idx'))
for demo in dirs[idx:idx+1]:
    dir = demo.split('/')[-1]
    save_dir = '/raid/Indoor/Data/DetectedData/GFL0816/T9AI/' + dir + '/'
    os.makedirs(save_dir, exist_ok=True)
    print("Current dir: ", save_dir)
    if demo == 'webcam' or demo[demo.rfind('.') + 1:].lower() in video_ext:
        camera = cv2.VideoCapture(camera_id if demo == 'webcam' else demo)
        camera.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
        camera.set(cv2.CAP_PROP_FRAME_HEIGHT, 960)
        print('Press "Esc", "q" or "Q" to exit.')
        while True:
            ret_val, img = camera.read()
            # img = cv2.remap(img, mapx, mapy, cv2.INTER_LINEAR)
            dets, meta = detector.inference(img)
            detector.show(dets, meta, class_names, 0.3, wait=1)
            ch = cv2.waitKey(1)
            if ch == 27 or ch == ord('q') or ch == ord('Q'):
                break
    else:
        if os.path.isdir(demo):
            image_names = []
            for maindir, subdir, file_name_list in os.walk(demo):
                for filename in file_name_list:
                    apath = os.path.join(maindir, filename)
                    ext = os.path.splitext(apath)[1]
                    if ext in image_ext:
                        image_names.append(apath)
        elif os.path.isfile(demo):
            with open(demo, 'r') as f:
                image_names = f.read().splitlines()  # .readlines() 会有换行符号 '\n'
                if not image_names[0].endswith('.jpg'):
                    prefix = '/'.join(demo.split('/')[:-3])
                    image_names = [(prefix + '/images/%s.jpg' % x) for x in image_names]
        else:
            image_names = [demo]
        image_names.sort()

        for image_name in image_names:
            # img = cv2.imread(image_name)
            # img = cv2.remap(img, mapx, mapy, cv2.INTER_LINEAR)
            # dets, meta = detector.inference(img)
            dets, meta = detector.inference(image_name)
            #detector.show(dets, meta, class_names, 0.3, wait=1)
            detector.save(dets, meta, class_names, 0.3, image_name, save_dir)
            ch = cv2.waitKey(0)
            if ch == 27 or ch == ord('q') or ch == ord('Q'):
                break
