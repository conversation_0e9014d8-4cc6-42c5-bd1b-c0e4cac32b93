"""
Create by mxy
2020/12/23
"""

from tools.inference import Detector
from econn.utils.config import cfg, update_config
import cv2
import numpy as np
import os
import torch
from tools.get_location_t10 import Location
from tools.aimap.aiMap import MapBuilder
import csv

# Smean = [0.59, 0.59, 0.61, 0.56, 0.64, 0.618, 0.719, 0.64, 0.65, 0.78, 0.67] #1211 mobileNext_gfl_320
# Smean = [0.682, 0.663, 0.669, 0.669, 0.727, 0.684, 0.727, 0.701, 0.703, 0.651, 0.853] #0331_mobilenet_320_11cls_model
# Smean = [0.65, 0.64, 0.65, 0.625, 0.703, 0.676, 0.779, 0.688, 0.708, 0.842, 0.743] #0831_mobilenext_512_11cls_model


# Smean = [0.64, 0.63, 0.659, 0.619, 0.68, 0.658, 0.747, 0.68, 0.678, 0.80, 0.70] #1211 mobilenext_gfl_320

# Smean = [0.62, 0.63, 0.601, 0.598, 0.672, 0.654, 0.721, 0.686, 0.679, 0.796, 0.693] #1231 mobilenext_gfl_v6.5.1_v5.4_320
# Smean = [0.627, 0.629, 0.609, 0.613, 0.679, 0.653, 0.722, 0.674, 0.691, 0.789, 0.699] #1231 mobilenext_gfl_v6.5_v5.4_320
# Smean = [0.624, 0.627, 0.594, 0.596, 0.664, 0.646, 0.714, 0.683, 0.672, 0.797, 0.700] #0101 mobilenext_gfl_v6.5



# Smean = [0.628, 0.647, 0.601, 0.611, 0.655, 0.645, 0.700, 0.688, 0.658, 0.824, 0.687] #0101 mobilenext_gfl_v6.5.1
# Smean = [0.624, 0.649, 0.601, 0.613, 0.655, 0.647, 0.702, 0.687, 0.656, 0.827, 0.686] #0101 mobilenext_gfl_v6.5.1
# Smean = [0.674, 0.674, 0.689, 0.639, 0.697, 0.672, 0.788, 0.702, 0.691, 0.873, 0.741] #20201219 mobilev2_one2one

# 第二次模型
# Smean = [0.69, 0.68, 0.69, 0.67, 0.73, 0.71, 0.79, 0.72, 0.73, 0.84, 0.77]#0831_mobilenext_512_11cls_model

# Smean = [0.696, 0.681, 0.700, 0.671, 0.732, 0.712, 0.800, 0.720, 0.730, 0.842, 0.770] #20200831 mobilenext_gfl　机器上x3模型

Smean = [0.67, 0.714, 0.672, 0.645, 0.730, 0.671, 0.770, 0.666, 0.697, 0.848, 0.807] #eco_Mobilenext_Centernet_512_V100_20210727

def score_refine(score, smean, smin=0.55):
    k = (1 - smin)/(smean -smin)
    refinescore = smin + k*(score - smin)
    return min(refinescore, 1)


def read_map(filepath):
    f = open(filepath)
    map = np.fromfile(f, dtype=np.float32)
    return map


show_label_List = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
#['ShaFa' 'ZhuoZi', 'ChaJi',,'DianShiGui', 'Chuang','MaTong']
# show_label_List = [2, 4, 5, 6, 8, 10]
camera_id = 0
# config_path = r"/raid/Indoor/Model/eco_ground_model/t10/eco_indoor_MobileNeXt_GFL_20200831_X3_finalmodel/train_config.yml"
# model_path = r"/raid/Indoor/Model/eco_ground_model/t10/eco_indoor_MobileNeXt_GFL_20200831_X3_finalmodel/model_best_score/model_best_score.pth"
# config_path = r"/raid/Indoor/Model/CenterNet512_20210811/mobilenextdwdeconv_centernet_512_202100727.yml"
# model_path = r"/raid/Indoor/Model/CenterNet512_20210811/model_best_score/model_best_score.pth"
config_path = r"/raid/Indoor/Model/GFL512_20210816/train_config.yml"
model_path = r"/raid/Indoor/Model/GFL512_20210816/model_best_score/model_best_score.pth"
update_config(cfg, config_path)
class_names = cfg.class_names
detector = Detector(cfg, model_path, 'cuda:0')
image_ext = ['.jpg', '.jpeg', '.webp', '.bmp', '.png']
video_ext = ['mp4', 'mov', 'avi', 'mkv']


# 所有待测试家庭环境路径文件
images_file_path = "/raid/Indoor/SegmanticPython/ai_map/all_family_images_dirs_path_20210727.txt"
robot_para_dir = '/raid/Indoor/SegmanticPython/ai_map/'

ai_map_name = r'语义地图/'
room_id = r'2020_48'
# room_id = r'18_0.2.7_5floor_feitao'

# log_path_dir = "/home/<USER>/oldsys/PycharmProjects/models_gitlab/econn_mas/exp/obj_detection/eco_indoor/eco_Mobilenext_Centernet_512_V100_20210727/语义地图/18_0.2.7_5floor_feitao"
# log_path_dir = "/home/<USER>/oldsys/PycharmProjects/models_gitlab/econn_mas/exp/obj_detection/eco_indoor/eco_Mobilenext_Centernet_512_V100_20210727/语义地图/19_0.2.7_5floor_E户型"

log_path_dir = os.path.join(os.path.dirname(config_path), ai_map_name, room_id)
score_thresh_by_class = [0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4]
# score_thresh_by_class = [0.55, 0.55, 0.55, 0.55, 0.55, 0.55, 0.55, 0.55, 0.55, 0.55, 0.55]
# score_thresh_by_class = [0.5, 0.55, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5]

with open(images_file_path, 'r') as f:
    csv_reader = csv.reader(f, delimiter=" ")
    for lines in csv_reader:
        if (len(lines) >= 3):

            # if(int(lines[0]) <= 5):
            #     continue
            robotpara = lines[1]
            image_path = lines[2]
            pose_path = os.path.join(image_path, 'pose.txt')

            print("current family:", image_path)
            table_path = os.path.join(robot_para_dir, robotpara, 'distance_table')
            AI_para_path = os.path.join(robot_para_dir, robotpara, 'AI_para.txt')
            Loc = Location(table_path, AI_para_path, int(robotpara[5:]))

            # 根据当前一户家庭的pose.txt计算当户家庭的离线log
            if not os.path.exists(pose_path):
                continue
            if not os.path.exists(log_path_dir):
                os.makedirs(log_path_dir)
            current_log_path = os.path.join(log_path_dir, lines[0] + ".txt")
            # if os.path.exists(current_log_path):
            #     continue

            fo = open(current_log_path, "w")
            fo.close()

            show_label_count = {}
            with open(pose_path) as f:
                csv_reader = csv.reader(f, delimiter=" ")
                aimap = MapBuilder(class_nums=11, map_size=1200, pixel_size=50, map_threshold=8) #map_threshold=4
                for row in csv_reader:
                    print(row)
                    # pose.txt有时存储7个数据，有时9个数据 
                    if(len(row) == 9):
                        file, posex, posey, angle, _, _, _ , _, _= row
                    if(len(row) == 7):
                        file, posex, posey, angle, _, _, _ = row
                    if(len(row) == 4):
                        file, posex, posey, angle = row


                    filename = file.split('/')[-1]
                    # print(filename)
                    pose = (int(posex), int(posey))
                    # print(pose, float(angle))
                    # CV_PI = 3.1415926535897932384626433832795
                    # angle = float((float(angle) / 100 * CV_PI)/180) #角度进行转化
                    # print(angle)
                    img_file = os.path.join(image_path, filename)
                    print(img_file)
                    img_raw = cv2.imread(img_file)
                    if img_raw is None:
                        continue

                    dets, meta = detector.inference(img_raw)

                    #detector.show(dets, meta, class_names, 0.3, wait=1)
                    results = dets[0]

                    aimap.build_pose_map((int(float(pose[0])), int(float(pose[1]))))
                    for label in range(1, 11 + 1):
                        for bbox in results[label]:
                            if label not in show_label_List:
                                continue

                            if bbox[4] > 0.3:
                                row_max = int(bbox[3])
                                row_min = int(bbox[1])

                                if(row_max > 680): #680
                                    continue

                                score = score_refine(bbox[4], Smean[label - 1])
                                if(score >= 0.5):
                                    bbox_tmp = bbox[0:5]

                                    if int(label) not in show_label_count:
                                        show_label_count[int(label)] = 1
                                    else:
                                        show_label_count[int(label)] += 1


                                    left_loc, right_loc = Loc.cla_loc_pixel(bbox_tmp, label)
                                    object_data_single = []
                                    if (left_loc[0] == -1 or left_loc[1] == -1) or (
                                            right_loc[0] == -1 or right_loc[1] == -1):
                                        continue
                                    else:
                                        object_data_single.append((float(left_loc[0]) + 17) * 10)
                                        object_data_single.append(float(left_loc[1]) * 10)
                                        object_data_single.append((float(right_loc[0]) + 17) * 10)
                                        object_data_single.append(float(right_loc[1]) * 10)
                                        object_data_single.append(float(pose[0]))
                                        object_data_single.append(float(pose[1]))
                                        object_data_single.append(float(angle))
                                        object_data_single.append(float(score))
                                        object_data_single.append(int(label) - 1)

                                        fo = open(current_log_path, "a")
                                        for i in range(len(object_data_single) - 1):
                                            fo.write('{:.2f}'.format(object_data_single[i]) + ' ')
                                        fo.write(str(object_data_single[8]))
                                        fo.write("\n")
                                        fo.close()

                                    if int((float(left_loc[0]) + 17) * 10) > 0:
                                        CV_PI = 3.1415926535897932384626433832795
                                        angle = float((float(angle) / 100 * CV_PI) / 180)  # 角度进行转化
                                        aimap.build_map(int(label), float(score),
                                                        (int((float(left_loc[0]) + 17) * 10),
                                                         int(float(left_loc[1]) * 10)),
                                                        (int((float(right_loc[0]) + 17) * 10),
                                                         int(float(right_loc[1]) * 10)),
                                                        (int(float(pose[0])), int(float(pose[1]))), float(angle))
                                        aimap.erase_map(int(label), float(score),
                                                        (int((float(left_loc[0]) + 17) * 10),
                                                         int(float(left_loc[1]) * 10)),
                                                        (int((float(right_loc[0]) + 17) * 10),
                                                         int(float(right_loc[1]) * 10)),
                                                        (int(float(pose[0])), int(float(pose[1]))), float(angle))
                                        aimap.draw_color_map()

                    # detector.show(dets, meta, class_names, 0.3, wait=1)
                    # cv2.namedWindow('map', 0)
                    # cv2.imshow('map', aimap.color_map + aimap.pose_map)
                    # ch = cv2.waitKey(0)
                    # if ch == ord('d'):
                    #     continue
                    # elif ch == ord('q'):
                    #     exit()
            cv2.imencode('.png', aimap.color_map + aimap.pose_map)[1].tofile('%s/aimap.png' % (log_path_dir))
            cv2.waitKey(0)
            print(show_label_count)
            cv2.destroyAllWindows()

print("'1PuTongMen'银色, '2ShaFa'浅蓝色,'3ChuangHu'紫色,'4ZhuoZi'黑色, '5ChaJi'红色, '6DianShiGui'亮绿色,'7DianShi'橙色, '8Chuang'大红, '9ChuangTouGui'蓝色,'10MaTong'深紫色, '11uchair'亮绿色'")














