

from tools.inference import Detector, MultiTaskDetector
from econn.utils.config import cfg, update_config
import cv2
import numpy as np
import os
import torch

# demo = r"D:\Videos\YDXJ0175_0226.mp4"
# demo = r"D:\Dataset\coco\val2017"
# demo = r'D:\Dataset\eco_indoor_2020_old\val2019'
demo = r"/home/<USER>/datasets/ppt_show"
camera_id = 0
config_path = r"/home/<USER>/code_project/nn/exp/GFL_0503_gpu2/train_config.yml"
model_path = r"/home/<USER>/code_project/nn/exp/GFL_0503_gpu2/model_best_score/model_best_score.pth"

update_config(cfg, config_path)
class_names = cfg.class_names
class_names = ['Trash can', 'Charging dock', 'Cleaning cloth', 'Rug', 'Shoes',
              'Wire', 'Sliding rail', 'Wheels']
if cfg.model.architecture == 'MultiTask':
    detector = MultiTaskDetector(cfg, model_path, device='cuda:0')
else:
    detector = Detector(cfg, model_path, 'cuda:0')


def read_map(filepath):
    f = open(filepath)
    map = np.fromfile(f, dtype=np.float32)
    return map


# mapx = read_map(r"D:\mapx\186\mapx")
# mapx = np.reshape(mapx, (720, 1280))
# mapy = read_map(r"D:\mapx\186\mapy")
# mapy = np.reshape(mapy, (720, 1280))

torch.backends.cudnn.enabled = True
torch.backends.cudnn.benchmark = True

image_ext = ['.jpg', '.jpeg', '.webp', '.bmp', '.png']
video_ext = ['mp4', 'mov', 'avi', 'mkv']

if demo == 'webcam' or demo[demo.rfind('.') + 1:].lower() in video_ext:
    camera = cv2.VideoCapture(camera_id if demo == 'webcam' else demo)
    camera.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
    camera.set(cv2.CAP_PROP_FRAME_HEIGHT, 960)
    print('Press "Esc", "q" or "Q" to exit.')
    while True:
        ret_val, img = camera.read()
        # img = cv2.remap(img, mapx, mapy, cv2.INTER_LINEAR)
        dets, meta = detector.inference(img)
        detector.show(dets, meta, class_names, 0.3, wait=1)
        ch = cv2.waitKey(1)
        if ch == 27 or ch == ord('q') or ch == ord('Q'):
            break
else:
    if os.path.isdir(demo):
        image_names = []
        for maindir, subdir, file_name_list in os.walk(demo):
            for filename in file_name_list:
                apath = os.path.join(maindir, filename)
                ext = os.path.splitext(apath)[1]
                if ext in image_ext:
                    image_names.append(apath)
    else:
        image_names = [demo]
    image_names.sort()
    for image_name in image_names:
        # img = cv2.imread(image_name)
        # img = cv2.remap(img, mapx, mapy, cv2.INTER_LINEAR)
        # dets, meta = detector.inference(img)
        dets, meta = detector.inference(image_name)
        img_result = detector.show(dets, meta, class_names, 0.3, wait=1)
        save_path = image_name.replace('.jpg', '_result.jpg').replace('ppt_show','ppt_show_result')
        print(save_path)
        cv2.imwrite(save_path, img_result)
        cv2.waitKey(1000)
        cv2.destroyAllWindows()
        ch = cv2.waitKey(0)
        if ch == 27 or ch == ord('q') or ch == ord('Q'):
            break

