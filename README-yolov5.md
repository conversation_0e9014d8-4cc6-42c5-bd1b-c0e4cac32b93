|     日期      |  功能     | 版本|
|--------------|-----------|-----|
|2021.5.19    |添加yolov5taskhead，当前状态：可以训练、验证，指标和GFL mobilenext基本一致。  <br> 问题：收敛速度和官方yolov5相比慢很多,需要调试。<br>TODO：1)CSP backbone  2)自动计算anchor
|2021.5.21    |添加CSPNet Backbone；修正输入为矩形时的decode函数；添加yolov5的show_results函数。<br>小目标效果和官方yolov5相比差很多。 <br>TODO:1)继续调试效果差的原因 2)自动计算anchor
|2021.5.21    |添加autoanchor；在YOLOv5Head 初始化时自动计算anchor|
|2021.5.25    |增加onecycle学习率衰减策略，效果更好。
|2021.5.31    |增加了完整的CSPNet实现;<br>修正了解码相关的问题，现在方形输入时解码应该没有问题了，矩形暂时没有测试。<br> 目前验证精度低0.7,可能是由于模型两个框架输入不一致、model.eval()输出不一致导致的。<br>TODO:ema训练策略
|2021.6.03    |增加了EMA策略，loss乘batchsize再反传，目前需要配合cspnet使用。<br>当前状态：方形、矩形训练、验证结果正常，相比gfl占用显存更少，收敛速度更快，但是不同的数据集上效果不同，各有优劣。<br>TODO:混合精度训练