"""
Create by <PERSON><PERSON>.Lv
2019.10.11

"""
import cv2
import math
import numpy as np
import time

color_list = np.array(
    [
        123, 123, 123,#紫色
        123, 123, 123,#银色
        255,255,11,#浅蓝色
        125, 60, 150,#紫色
        33,33,33,#黑色
        127,22,255,#红色
        5,255,55,#亮绿色
        23, 123, 255,#橙色
        23,23,255,#大红
        255, 8, 88,#蓝色
        177,7,77,#深紫色
        0, 255, 0,
        255, 99, 99,
        255, 255, 0,
        255, 255, 255,
        100, 100, 100,
        127, 0, 255,
        127, 22, 255,
        156, 180, 190
    ]
).astype(np.float32)
color_list = color_list.reshape((-1, 3))

label2depth = {1: 50, 2: 1500, 3: 100, 4: 100, 5: 800, 6: 800, 7: 400, 8: 2000, 9: 0, 10: 500, 11:1200}
#label2depth = {1: 10, 2: 10, 3: 10, 4: 10, 5: 10, 6: 10, 7: 10, 8: 10, 9: 10, 10: 10, 11: 10}



def get_dis_weight(pleft, pright):
    """
    按距离对得分加权
    :param pleft: 左边点相对坐标 (x,y) 单位 mm
    :param pright: 右边点相对坐标 (x,y) 单位 mm
    :return: disScale： 权重
    """
    dis_weight = [5.13 / 5.13, 4.41 / 5.13, 3.83 / 5.13, 3.37 / 5.13, 3.04 / 5.13, 2.71 / 5.13, 2.40 / 5.13]
    dis_weight = [1, 1, 1, 1, 1, 1, 1, 1, 1,1 ,1 ,1, 1, 1]
    distance = (pleft[0] + pright[0]) / 2 - 170
    if distance < 240:
        disScale = dis_weight[0]
    elif distance < 340:
        disScale = dis_weight[1]
    elif distance < 440:
        disScale = dis_weight[2]
    elif distance < 540:
        disScale = dis_weight[3]
    elif distance < 640:
        disScale = dis_weight[4]
    elif distance < 740:
        disScale = dis_weight[5]
    else:
        disScale = dis_weight[6]
    return disScale


class MapBuilder(object):
    def __init__(self, class_nums, map_size=800, pixel_size=50, map_threshold=2.5):
        """
        初始化
        :param class_nums: 类别数量
        :param pixel_size: 地图像素对应的大小（单位：mm）
        :param map_size: 地图大小（单位：像素）
        :param map_threshold: 叠图阈值
        """
        self.class_nums = class_nums  # 类别数量
        self.pixel_size = pixel_size
        self.map_size = map_size  # 地图大小
        self.map_threshold = map_threshold  # 叠图阈值
        self.PRM = dict()  # 概率地图
        self.label_map = dict()  # 阈值过滤后的类别地图
        self.color_map = np.zeros((map_size, map_size, 3), dtype=np.uint8)  # 用于显示的类别地图
        self.pose_map = np.zeros((map_size, map_size, 3), dtype=np.uint8)  # 路径地图

    def map_point(self, pose, angle, point):
        """
        把相对机器的坐标点转换到地图坐标系
        :param pose:
        :param angle:
        :param point:
        :return:
        """
        rot = [[math.cos(angle), math.sin(angle)],
               [-math.sin(angle), math.cos(angle)]]
        rot = np.array(rot)
        rot_p = np.dot(np.linalg.inv(rot), np.array([[point[0]], [point[1]]]))
        map_p = [round((rot_p[0, 0] + pose[0]) / self.pixel_size + self.map_size / 2),
                 round((rot_p[1, 0] + pose[1]) / self.pixel_size + self.map_size / 2)]
        return map_p

    def map_area(self, pose, angle, pleft, pright, depth=50):
        """
        根据测距得到的两个点，计算地图坐标系下的区域
        :param pose:  机器人坐标 (x,y) 单位 mm
        :param angle: 机器人角度:
        :param pleft: 左边点相对坐标 (x,y) 单位 mm
        :param pright: 右边点相对坐标 (x,y) 单位 mm
        :param depth: 估计的物体的深度 单位 mm
        :return: 叠图的区域
        """
        map_p1 = self.map_point(pose, angle, pleft)
        map_p2 = self.map_point(pose, angle, [pleft[0] + depth, pleft[1]])
        map_p3 = self.map_point(pose, angle, pright)
        map_p4 = self.map_point(pose, angle, [pright[0] + depth, pright[1]])

        xmin = int(min(map_p1[0], map_p2[0], map_p3[0], map_p4[0]))
        xmax = int(max(map_p1[0], map_p2[0], map_p3[0], map_p4[0]))
        ymin = int(min(map_p1[1], map_p2[1], map_p3[1], map_p4[1]))
        ymax = int(max(map_p1[1], map_p2[1], map_p3[1], map_p4[1]))
        return xmin, ymin, xmax, ymax

    def protect_area(self, pose, angle, pleft, pright, depth=50, protect_size=250):
        """
        :param pose:  机器人坐标 (x,y) 单位 mm
        :param angle: 机器人角度:
        :param pleft: 左边点相对坐标 (x,y) 单位 mm
        :param pright: 右边点相对坐标 (x,y) 单位 mm
        :param depth: 估计的物体的深度 单位 mm
        :param protect_size: 保护范围大小 单位 mm
        :return: 保护的区域
        """
        d = int(depth/2)
        p_p1 = self.map_point(pose, angle, [pleft[0] + d + protect_size, pleft[1] - protect_size])
        p_p2 = self.map_point(pose, angle, [pleft[0] + d - protect_size, pleft[1] - protect_size])
        p_p3 = self.map_point(pose, angle, [pright[0] + d + protect_size, pright[1] + protect_size])
        p_p4 = self.map_point(pose, angle, [pright[0] + d - protect_size, pright[1] + protect_size])

        xmin = int(min(p_p1[0], p_p2[0], p_p3[0], p_p4[0]))
        xmax = int(max(p_p1[0], p_p2[0], p_p3[0], p_p4[0]))
        ymin = int(min(p_p1[1], p_p2[1], p_p3[1], p_p4[1]))
        ymax = int(max(p_p1[1], p_p2[1], p_p3[1], p_p4[1]))
        return xmin, ymin, xmax, ymax

    def build_pose_map(self, pose, color=(0, 0, 255)):
        """
        绘制机器人路径
        :param color:
        :param pose: 机器人坐标 (x,y) 单位 mm
        """
        self.pose_map[round(pose[0] / self.pixel_size + self.map_size / 2),
                      round(pose[1] / self.pixel_size + self.map_size / 2)] = color

    def build_map(self, label, score, pleft, pright, pose, angle):
        """
        叠图函数
        绘制概率地图PRM，生成阈值后地图label_map
        :param label: 类别
        :param score: 得分
        :param pleft: 左边点相对坐标 (x,y) 单位 mm
        :param pright: 右边点相对坐标 (x,y) 单位 mm
        :param pose:  机器人坐标 (x,y) 单位 mm
        :param angle: 机器人角度
        """
        xmin, ymin, xmax, ymax = self.map_area(pose, angle, pleft, pright, depth=label2depth[label])
        label_weight = np.array([0.00723369, 0.03901726, 0.07485534, 0.17127917, 0.077141,
       0.13296672, 0.07167001, 0.02286306, 0.12955732, 0.2296698,
       0.04374663]) ### 根据label衰减得分
        score *= label_weight[label-1]*11
        dis_weight = get_dis_weight(pleft, pright)
        for i in range(xmin, xmax + 1):
            for j in range(ymin, ymax + 1):
                # 叠概率地图
                if (i, j, label) in self.PRM:
                    self.PRM[(i, j, label)] += 1 * dis_weight * score
                else:
                    self.PRM.update({(i, j, label): 1 * dis_weight * score})
                # 概率地图转类别地图
                if self.PRM[(i, j, label)] > self.map_threshold:
                    if (i, j) in self.label_map:
                        self.label_map[(i, j)] = label
                    else:
                        self.label_map.update({(i, j): label})
                else:
                    if (i, j) in self.label_map and self.label_map[(i, j)] == label:
                        del self.label_map[(i, j)]

    def erase_map(self, label, score, pleft, pright, pose, angle, same_class_protect=True):
        """
        消点函数
        :param label: 类别
        :param score: 得分
        :param pleft: 左边点相对坐标 (x,y) 单位 mm
        :param pright: 右边点相对坐标 (x,y) 单位 mm
        :param pose:  机器人坐标 (x,y) 单位 mm
        :param angle: 机器人角度
        :param same_class_protect:
        """
        erase_pleft = (200, -1000)
        erase_pright = (200, 1000)
        erase_xmin, erase_ymin, erase_xmax, erase_ymax = self.map_area(pose, angle, erase_pleft, erase_pright, depth=700)
        if same_class_protect:
            p_xmin, p_ymin, p_xmax, p_ymax = self.protect_area(pose, angle, pleft, pright, depth=label2depth[label])
        for i in range(erase_xmin, erase_xmax + 1):
            for j in range(erase_ymin, erase_ymax + 1):
                for cl in range(1, self.class_nums+1):
                    if (i, j, cl) in self.PRM:  # 元素存在
                        if same_class_protect and cl == label:  # 开启同类保护
                            if not(p_xmin<i<p_xmax and p_ymin<j<p_ymax):  # 不在保护范围内
                                    self.PRM[(i, j, cl)] = max(self.PRM[(i, j, cl)] - 1 * 0.04, 0)
                        else:
                                self.PRM[(i, j, cl)] = max(self.PRM[(i, j, cl)] - 1 * 0.04, 0)

                        # 概率地图转类别地图
                        if self.PRM[(i, j, cl)] > self.map_threshold:
                            if (i, j) in self.label_map:
                                # max_prm_rj =  max([self.PRM.get((i,j,c+1), 0) for c in range(11)])
                                # if self.PRM[(i, j, cl)] >= max_prm_rj:
                                #     self.label_map[(i, j)] = cl
                                self.label_map[(i, j)] = cl
                            else:
                                self.label_map.update({(i, j): cl})
                        else:
                            if (i, j) in self.label_map and self.label_map[(i, j)] == cl:
                                del self.label_map[(i, j)]

    def draw_color_map(self):
        """
        生成显示用的彩色地图
        """
        for position, label in self.label_map.items():
            # print(position,label)
            self.color_map[position[0], position[1]] = color_list[label]


if __name__ == "__main__":
    import csv
    data_list = []

    aimap = MapBuilder(class_nums=9, map_size=300, pixel_size=50, map_threshold=2.5)
    with open("log_room10_10_refine.txt") as f:
        csv_reader = csv.reader(f, delimiter=" ")
        for row in csv_reader:
            p1x, p1y, p2x, p2y, a, b, posx, posy, angle, score, label = row
            aimap.build_pose_map((int(posx), int(posy)))
            # if int(p1x) > 0:
            if int(p1x) > 0 and int(label) <= 9:
                # time1 = time.time()
                aimap.build_map(int(label), float(score), (int(p1x), int(p1y)), (int(p2x), int(p2y)),
                                (int(posx), int(posy)), float(angle))
                aimap.erase_map(int(label), float(score), (int(p1x), int(p1y)), (int(p2x), int(p2y)),
                                (int(posx), int(posy)), float(angle))
                aimap.draw_color_map()
                # print((time.time()-time1)*1000, 'ms')
            cv2.imshow('map', aimap.pose_map + aimap.color_map)
            # cv2.imshow('pose map', )
            cv2.waitKey(1)
    cv2.waitKey(0)
