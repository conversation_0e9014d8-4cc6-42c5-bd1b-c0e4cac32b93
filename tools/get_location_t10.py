import numpy as np
import cv2, os
import socket
import ctypes
from ctypes import *
import csv
# from tools.ros_publisher import AIPubilsher

class obj_detect(Structure):
    _fields_ = [("row1", c_int), ("col1", c_int), ("row2", c_int), ("col2", c_int), ("cl", c_int), ("score", c_float)]

class location(Structure):
    _fields_ = [("x1", c_float), ("y1", c_float), ("x2", c_float), ("y2", c_float), ("cl", c_int), ("score", c_float)]


def read_file(file_path, height, width):
    f = open(file_path, 'rb')
    npdata = np.fromfile(f, dtype=np.ubyte)
    width = int(width/2)
    xtable = np.zeros((height, width), dtype=np.ubyte)
    ytable = np.zeros((height, width), dtype=np.ubyte)
    for i in range(height):
        for j in range(width):
            xtable[i][j] = npdata[i*width*2+j*2]
            ytable[i][j] = npdata[i*width*2+j*2+1]
    xtable = xtable.astype(np.float) / 255 * 70 + 0
    ytable = ytable.astype(np.float) / 255 * 150 + (-75)
    return xtable, ytable

def get_eco_ranging_maxinfo(robot_id):
    maxRowInfo=[600,600]
    if robot_id == 181:
        maxRowInfo[0] =  660
        maxRowInfo[1] = 798

    elif robot_id == 346:
        maxRowInfo[0] = 660
        maxRowInfo[1] = 798

    elif robot_id == 187:
        maxRowInfo[0] =  662
        maxRowInfo[1] = 169

    elif robot_id == 177:
        maxRowInfo[0] = 665
        maxRowInfo[1] = 1106

    elif robot_id == 178:
        maxRowInfo[0] =  667
        maxRowInfo[1] =  176

    elif robot_id == 186:
        maxRowInfo[0] =  660
        maxRowInfo[1] = 171

    elif robot_id == 108:
        maxRowInfo[0] =  662
        maxRowInfo[1] = 169
    else:
        print("No robot id")
    return maxRowInfo


class Location(object):
    def __init__(self, table_path, AI_para_path, robot_id=108, model_id=1, CPP_on=True, socket_on=False, ros_on=False):
        self.x_table, self.y_table = read_file(table_path, 251, 2442)
        self.socket_on = socket_on
        self.ros_on = ros_on
        self.CPP_on = CPP_on
        self.flag = True
        self.model_id = model_id
        if self.socket_on:
            self.socket_init(12345)
        if CPP_on:
            cdll = ctypes.cdll.LoadLibrary
            self.loc_lib = cdll('/raid/Indoor/SegmanticPython/tools/location_lib_t10/libpixel2location.so')
            f = open(table_path, 'r')
            data = np.fromfile(f, dtype=np.uint8)
            f.close()
            with open(AI_para_path, 'r') as f:
                csv_reader = csv.reader(f, delimiter=" ")
                for lines in csv_reader:
                    if(len(lines) <= 5):
                        self.row_label = 508
                    else:
                        self.row_label = int(float(lines[1]))

            self.carray = (ctypes.c_uint8 * len(data))(*data)
            self.loc_lib.pixelTolocWithTable.restype = location
            # self.loc_lib.pixelToloc.argtypes = [(ctypes.c_uint8 * len(data))(*data),
            #                            location,data_type]
            line = c_int * 2
            maxRowInfo = line()
            maxRowInfo_ = get_eco_ranging_maxinfo(robot_id)
            maxRowInfo[0] = maxRowInfo_[0]
            maxRowInfo[1] = maxRowInfo_[1]
            self.maxRowInfo = maxRowInfo
            self.roi = obj_detect()


    def socket_init(self, port):
        # ----------socket_init-------------
        self.skt = socket.socket()  # 创建 socket 对象
        host = socket.gethostname()  # 获取本地主机名
        self.skt.bind((host, port))  # 绑定端口

        print('等待客户端连接')
        self.skt.listen(5)  # 等待客户端连接
        self.c, addr = self.skt.accept()  # 建立客户端连接
        print('连接地址：', addr)
        self.socket_on = True

    def cal_loc(self, row , col):
        if 450 <= row <= 700 and 30 <= col <= 1250:
            # print(row,col)
            X = self.x_table[row-450][col-30]
            Y = self.y_table[row-450][col-30]
            if X==0 :
                return None
            else:
                return [X, Y]
        else:
            return None

    def cla_loc_pixel(self, bbox, label):

        if self.CPP_on:

            if(int(bbox[1]) >= self.row_label):
                self.flag = True
            else:
                self.flag = False
            self.roi.col1 = int(bbox[0])
            self.roi.row1 = int(bbox[1])
            self.roi.col2 = int(bbox[2])
            self.roi.row2 = int(bbox[3])
            self.roi.score = bbox[4]
            self.roi.cl = label
            if(self.flag):
                loc = self.loc_lib.pixelTolocWithTable(self.carray, self.roi, self.model_id, self.maxRowInfo)
            else:
                loc = self.loc_lib.pixelTolocWithTable(self.carray, self.roi, self.model_id, self.maxRowInfo)

            left_loc = (loc.x1, loc.y1)
            right_loc = (loc.x2, loc.y2)

            return left_loc, right_loc
        else:
            left_loc = self.cal_loc( int(bbox[3]), int(bbox[0]))
            right_loc = self.cal_loc( int(bbox[3]), int(bbox[2]))
            return left_loc, right_loc





if __name__ == '__main__':
    map_path = '/mnt/bbf9fa85-6696-4aec-82a3-ee4fefb25980/zhuguotao/share/语义叠图/T10/ai_map/robot346/distance_table'
    ai_para_path = "/mnt/bbf9fa85-6696-4aec-82a3-ee4fefb25980/zhuguotao/share/语义叠图/T10/ai_map/robot346/AI_para.txt"
    bbox = [100, 560, 200, 600, 0.7]
    Loc = Location(map_path, AI_para_path=ai_para_path, robot_id=108, model_id=1)

    left_loc, right_loc = Loc.cla_loc_pixel(bbox, 1)
    print(left_loc, right_loc)
