"""
Create by Chengqi.Lv
2020.02.10

"""

import pycocotools.coco as coco
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.ticker import MultipleLocator
import os


def calculate_IOU(dt, gt):
    """
    计算IOU
    dt; gt: cocoAPI format det and gt
    """
    dtbbox = dt['bbox']  # x, y, w, h
    x1 = dtbbox[0]
    y1 = dtbbox[1]
    width1 = dtbbox[2]
    height1 = dtbbox[3]

    gtbbox = gt['bbox']
    x2 = gtbbox[0]
    y2 = gtbbox[1]
    width2 = gtbbox[2]
    height2 = gtbbox[3]

    endx = max(x1 + width1, x2 + width2)
    startx = min(x1, x2)
    width = width1 + width2 - (endx - startx)

    endy = max(y1 + height1, y2 + height2)
    starty = min(y1, y2)
    height = height1 + height2 - (endy - starty)

    if width <= 0 or height <= 0:
        ratio = 0  # 重叠率为 0
    else:
        Area = width * height  # 两矩形相交面积
        Area1 = width1 * height1
        Area2 = width2 * height2
        ratio = Area * 1. / (Area1 + Area2 - Area)
    return ratio


def plot_confusion_matrix(confusion_matrix,
                          labels,
                          savepath=None,
                          show=True,
                          title='Normalized Confusion Matrix',
                          color_theme='plasma'):
    """
    绘制混淆矩阵函数
    :param confusion_matrix: 混淆矩阵
    :param labels: 类别标签名
    :param savepath: 保存目录，若是None则不保存
    :param show: 是否显示图像
    :param title: 图表标题
    :param color_theme: 配色
    """
    fig, ax = plt.subplots(figsize=(10, 8), dpi=110)
    cmap = plt.get_cmap(color_theme)
    im = ax.imshow(confusion_matrix, cmap=cmap)
    plt.colorbar(mappable=im, ax=ax)

    title_font = {'family': 'Times New Roman',
                  'weight': 'bold',
                  'size': 23,
                  }
    ax.set_title(title, fontdict=title_font)

    label_font = {'size': 15}
    plt.ylabel('Ground Truth Label', fontdict=label_font)
    plt.xlabel('Predicted label', fontdict=label_font)

    # 设置主刻度副刻度
    xmajorLocator = MultipleLocator(1)
    xminorLocator = MultipleLocator(0.5)
    ax.xaxis.set_major_locator(xmajorLocator)
    ax.xaxis.set_minor_locator(xminorLocator)
    ymajorLocator = MultipleLocator(1)
    yminorLocator = MultipleLocator(0.5)
    ax.yaxis.set_major_locator(ymajorLocator)
    ax.yaxis.set_minor_locator(yminorLocator)

    # 画副刻度网格
    ax.grid(True, which='minor', linestyle='-')

    # 设置坐标轴和标签
    ax.set_xticks(np.arange(len(labels)))
    ax.set_yticks(np.arange(len(labels)))

    ax.set_xticklabels(labels)
    ax.set_yticklabels(labels)

    ax.tick_params(axis="x", bottom=False, top=True, labelbottom=False, labeltop=True)  # x轴移到上方
    plt.setp(ax.get_xticklabels(), rotation=45, ha="left", rotation_mode="anchor")  # x标签旋转45度

    # 填写内容
    for i in range(len(labels)):
        for j in range(len(labels)):
            text = ax.text(j, i, "%0.3f" % confusion_matrix[i, j],
                           ha="center", va="center", color="w", size=8)

    ax.set_ylim(len(confusion_matrix) - 0.5, -0.5)  # matplotlib版本>3.1.1时需要移位

    fig.tight_layout()
    if savepath is not None:
        plt.savefig(os.path.join(savepath, 'confusion_matrix.png'), format='png')
    if show:
        plt.show()


class ConfusionMatrixCalculator:
    def __init__(self,
                 cocoDT,
                 cocoGT,
                 save_path=None,
                 show=True,
                 score_threshold=0.1,
                 iou_threshold=0.5):
        self.cocoDT = cocoDT
        self.cocoGT = cocoGT
        self.save_path = save_path
        self.show = show
        self.score_threshold = score_threshold
        self.iou_threshold = iou_threshold
        self.class_num = len(self.cocoGT.cats) + 1
        self.confusion_matrix = np.zeros(shape=[self.class_num, self.class_num])
        self.normalized_confusion_matrix = np.zeros(shape=[self.class_num, self.class_num])
        self.labels = ['background']
        for catid, category in self.cocoGT.cats.items():
            self.labels.append(category['name'])

    def analyze(self):
        for img_id, per_img_dt in self.cocoDT.imgToAnns.items():
            per_img_gt = self.cocoGT.imgToAnns[img_id]
            for per_dt in per_img_dt:
                if per_dt['score'] > self.score_threshold:
                    for per_gt in per_img_gt:
                        iou = calculate_IOU(per_dt, per_gt)
                        if iou >= self.iou_threshold:
                            gt_category = per_gt['category_id']
                            dt_category = per_dt['category_id']
                            self.confusion_matrix[gt_category][dt_category] += 1
                        else:
                            dt_category = per_dt['category_id']
                            self.confusion_matrix[0][dt_category] += 1
            # miss match
            for per_gt in per_img_gt:
                match_num = 0
                for per_dt in per_img_dt:
                    if per_dt['score'] > self.score_threshold:
                        iou = calculate_IOU(per_dt, per_gt)
                        if iou >= self.iou_threshold:
                            match_num += 1
                if match_num < 1:
                    gt_category = per_gt['category_id']
                    self.confusion_matrix[gt_category][0] += 1

        self.normalized_confusion_matrix = \
            self.confusion_matrix.astype('float') / self.confusion_matrix.sum(axis=1)[:, np.newaxis]
        self.normalized_confusion_matrix[0, 0] = 1

        title = "{} @{}iou @{}score".format('Normalized Confusion Matrix', self.iou_threshold, self.score_threshold)

        plot_confusion_matrix(self.normalized_confusion_matrix, self.labels, savepath=self.save_path, show=self.show)


def analyze(GT_json_path,
            DT_json_path,
            score_threshold=0.1,
            iou_threshold=0.5):
    confusion_matrix = np.zeros(shape=[8, 8])
    cocoGT = coco.COCO(GT_json_path)
    cocoDT = cocoGT.loadRes(DT_json_path)
    for img_id, per_img_dt in cocoDT.imgToAnns.items():
        per_img_gt = cocoGT.imgToAnns[img_id]
        for per_dt in per_img_dt:
            if per_dt['score'] > score_threshold:
                for per_gt in per_img_gt:
                    iou = calculate_IOU(per_dt, per_gt)
                    if iou >= iou_threshold:
                        gt_category = per_gt['category_id']
                        dt_category = per_dt['category_id']
                        confusion_matrix[gt_category][dt_category] += 1
                    else:
                        dt_category = per_dt['category_id']
                        confusion_matrix[0][dt_category] += 1
        # miss match
        for per_gt in per_img_gt:
            match_num = 0
            for per_dt in per_img_dt:
                if per_dt['score'] > score_threshold:
                    iou = calculate_IOU(per_dt, per_gt)
                    if iou >= iou_threshold:
                        match_num += 1
            if match_num < 1:
                gt_category = per_gt['category_id']
                confusion_matrix[gt_category][0] += 1

    normalized_confusion_matrix = confusion_matrix.astype('float') / confusion_matrix.sum(axis=1)[:, np.newaxis]

    print(confusion_matrix)
    print(normalized_confusion_matrix)

    labels = ['background']
    for catid, category in cocoGT.cats.items():
        labels.append(category['name'])
    plot_confusion_matrix(normalized_confusion_matrix, labels)


if __name__ == '__main__':
    # annotation
    GT_path = r"验证集json路径"
    # result.json
    DT_path = r"results.json路径"
    cocoGT = coco.COCO(GT_path)
    cocoDT = cocoGT.loadRes(DT_path)
    conf_mat = ConfusionMatrixCalculator(cocoDT,
                                         cocoGT,
                                         save_path=r'./',
                                         score_threshold=0.1,
                                         show=True)
    conf_mat.analyze()
