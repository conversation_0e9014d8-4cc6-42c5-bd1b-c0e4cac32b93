import numpy as np
import copy
from pycocotools.coco import COCO
from pycocotools.cocoeval import COCOeval
import matplotlib.pyplot as plt

def plot_pr_curve(px, py, ap, save_dir='./', names=()):
    # Precision-recall curve
    fig, ax = plt.subplots(1, 1, figsize=(9, 6), tight_layout=True)
    py = np.stack(py, axis=1)

    if 0 < len(names) < 21:  # display per-class legend if < 21 classes
        for i, y in enumerate(py.T):
            ax.plot(px, y, linewidth=1, label=f'{names[i]} {ap[i, 0]:.3f}')  # plot(recall, precision)
    else:
        ax.plot(px, py, linewidth=1, color='grey')  # plot(recall, precision)

    ax.plot(px, py.mean(1), linewidth=3, color='blue', label='all classes %.3f mAP@0.5' % ap[:, 0].mean())
    ax.set_xlabel('Recall')
    ax.set_ylabel('Precision')
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    plt.legend(bbox_to_anchor=(1.04, 1), loc="upper left")
    fig.savefig(save_dir+'/pr_curve.png', dpi=250)


if __name__ == '__main__':
    result_json_path = '/raid/Indoor/Model/GFL512_20210903/model_best_score/results0.json' #<<<<<<<<<<< 1 <<<<<<<<<<<<<
    gt_json_path = '/raid/Indoor/Data/JsonFiles/val_2020_2021_copy.json' #<<<<<<<<<<< 2 <<<<<<<<<<<<<
    pig_save_dir = './' #<<<<<<<<<<< 3 <<<<<<<<<<<<<
    names = ['PuTongMen', 'ShaFa', #<<<<<<<<<<< 4 <<<<<<<<<<<<<
                    'ChuangHu', 'ZhuoZi', 'ChaJi', 'DianShiGui',
                    'DianShi', 'Chuang', 'ChuangTouGui',
                    'MaTong', 'uchair']
    print("Detected json path: ",result_json_path)
    print("Labeled json path: ", gt_json_path)

    coco = COCO(annotation_file=gt_json_path)
    coco_dets = coco.loadRes(result_json_path)
    coco_eval = COCOeval(copy.deepcopy(coco), copy.deepcopy(coco_dets), "bbox")
    coco_eval.evaluate()
    coco_eval.accumulate()
    coco_eval.summarize()

    precisions = coco_eval.eval['precision'] # (10, 101, 11, 4, 3)
    '''
    precisions[T, R, K, A, M]
    T: iou thresholds [0.5 : 0.05 : 0.95], idx from 0 to 9
    R: recall thresholds [0 : 0.01 : 1], idx from 0 to 100
    K: category, idx from 0 to ...
    A: area range, (all, small, medium, large), idx from 0 to 3 
    M: max dets, (1, 10, 100), idx from 0 to 2
    '''
    px = np.arange(0.0, 1.01, 0.01)
    py = precisions[0,:,:,0,2].T # (n_cls, 101)
    ap = precisions[:,:,:,0,2].mean(1).T # (n_cls, n_iouv))
    plot_pr_curve(px,py,ap,names=names)