"""
Create by Chengqi.Lv
2020/12/16
"""

from econn.utils.config import cfg, update_config
from econn.datasets.builder import build_dataset
from econn.models.architecture.builder import build_model
from econn.trainers.multi_task_trainer import MultiTaskTrainer
from econn.utils.collate import collate_function
from econn.utils.logger.tensorboard import TensorboardLogger
from econn.evaluate.builder import build_evaluator
import os
import torch
import logging
import argparse
import torch.distributed as dist


parser = argparse.ArgumentParser()
parser.add_argument('config', help='train config file path')
parser.add_argument('--local_rank', default=-1, type=int,
                    help='node rank for distributed training')
args = parser.parse_args()


def main(config):
    if not os.path.exists(config.save_dir):
        os.makedirs(config.save_dir)

    fmt = '%(asctime)s %(levelname)s: %(message)s'
    logging.basicConfig(level=logging.INFO,
                        format=fmt,
                        filename=os.path.join(config.save_dir, 'logs.txt'),
                        filemode='w',
                        datefmt='%a, %d %b %Y %H:%M:%S'
                        )
    console = logging.StreamHandler()
    console.setLevel(logging.INFO)
    formatter = logging.Formatter(fmt)
    console.setFormatter(formatter)
    logging.getLogger().addHandler(console)

    SEED = 0
    torch.manual_seed(SEED)

    torch.backends.cudnn.enabled = True
    torch.backends.cudnn.benchmark = True

    device = torch.device('cuda')

    logging.info('Creating model...')

    model = build_model(config.model)
    logger = TensorboardLogger(config.save_dir, int(args.local_rank))

    train_datasets = {}
    val_datasets = {}
    evaluators = {}
    for task in config.data:
        # 可以选择在dataloder里提前encode，也可以选择在计算loss时encode

        if task in model.encoder:
            encoder = model.encoder[task]
        else:
            encoder = None

        train_datasets[task] = build_dataset(config.data[task].train, 'train', encoder=encoder)
        val_datasets[task] = build_dataset(config.data[task].val, 'val', encoder=encoder)
        evaluators[task] = build_evaluator(config.train.val_type[task], val_datasets[task], logger)
    num_task = len(train_datasets)
    use_distributed = False
    if len(config.train.gpu_ids) > 1:
        use_distributed = True
        rank = int(args.local_rank)
        print('rank = ', args.local_rank)
        num_gpus = torch.cuda.device_count()
        torch.cuda.set_device(rank % num_gpus)

        dist.init_process_group(backend='nccl')

    if use_distributed:
        batchsize = config.train.batchsize//num_gpus
        assert batchsize % num_task == 0
        batchsize //= num_task
        trainer = MultiTaskTrainer(config, model=model, evaluator=evaluators, logger=logger)
        trainer.set_device(int(args.local_rank), batchsize=batchsize, device=device)
    else:
        batchsize = config.train.batchsize
        assert batchsize % num_task == 0
        batchsize //= num_task
        trainer = MultiTaskTrainer(config, model=model, evaluator=evaluators, logger=logger)
        trainer.set_device(config.train.gpu_ids, batchsize=batchsize, device=device)

    trainer.build_optimizer()

    if 'load_model' in config.train or 'resume' in config.train:
        trainer.load_model(config)

    logging.info('Setting up data...')

    train_loaders = {}
    val_loaders = {}
    for k in config.data:
        if use_distributed:
            train_sampler = torch.utils.data.distributed.DistributedSampler(train_datasets[k])
            train_loader = torch.utils.data.DataLoader(train_datasets[k],
                                                       batch_size=batchsize,
                                                       num_workers=config.train.num_workers//num_gpus,
                                                       pin_memory=True,
                                                       collate_fn=collate_function,
                                                       sampler=train_sampler,
                                                       drop_last=True)
        else:
            train_loader = torch.utils.data.DataLoader(
                train_datasets[k],
                batch_size=batchsize,
                shuffle=True,
                num_workers=config.train.num_workers,
                pin_memory=True,
                collate_fn=collate_function,
                drop_last=True,
            )

        val_loader = torch.utils.data.DataLoader(
            val_datasets[k],
            batch_size=1,
            shuffle=False,
            num_workers=1,
            pin_memory=True,
            collate_fn=collate_function,
            drop_last=True,
        )
        train_loaders[k] = train_loader
        val_loaders[k] = val_loader

    logging.info('Starting training...')

    trainer.run(train_loaders, val_loaders)



if __name__ == '__main__':
    update_config(cfg, args.config)
    main(cfg)
