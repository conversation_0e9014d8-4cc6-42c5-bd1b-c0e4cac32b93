"""
Create by Chengqi.Lv
2020/4/23
"""
import torch
from thop import profile
from econn.utils.config import cfg, update_config
from econn.models.architecture.builder import build_model


def main(config, input_shape=(512, 512)):
    model = build_model(config.model)
    print('build model success!')
    dummy_input = torch.autograd.Variable(torch.randn(1, 3, input_shape[0], input_shape[1]))
    flops, params = profile(model, inputs=(dummy_input,))
    print('flops:{}\nparams:{}'.format(flops, params))


if __name__ == '__main__':
    cfg_path = r'D:\Projects\ECO_NN\configs\FCOS_example.yml'
    update_config(cfg, cfg_path)
    main(config=cfg,
         input_shape=(512, 512)
         )