from econn.utils.config import cfg, update_config
from econn.models.architecture.builder import build_model
import torch
import os

"""
SWA 模型平均
训练完后再多训练12个epoch，每一个epoch都保存一个模型
最后把模型的参数平均一下
可以涨一些点 
https://arxiv.org/pdf/2012.12645.pdf
"""

def load_model(load_path):
    checkpoint = torch.load(load_path, map_location=lambda storage, loc: storage)
    print('loaded {}, epoch {}'.format(load_path, checkpoint['epoch']))
    state_dict_ = checkpoint['state_dict']
    state_dict = {}

    # convert data_parallal to model
    for k in state_dict_:
        if k.startswith('module') and not k.startswith('module_list'):
            state_dict[k[7:]] = state_dict_[k]
        else:
            state_dict[k] = state_dict_[k]
    return state_dict

def reduce_all_weights(load_path, nums):
    merged_state_dict = load_model(os.path.join(load_path, 'model_1.pth'))
    for i in range(2, nums+1):
        state_dict = load_model(os.path.join(load_path, 'model_{}.pth'.format(i)))
        for k,v in state_dict.items():
            merged_state_dict[k] += v
    for k in merged_state_dict:
        merged_state_dict[k] = merged_state_dict[k] / nums
    return merged_state_dict

def main(config, model_folder, nums=12):
    model = build_model(config.model)
    state_dict = reduce_all_weights(model_folder, nums)
    model.load_state_dict(state_dict, strict=True)
    data = {'epoch': 0,
            'state_dict': model.state_dict(),
            'iter': 0}
    torch.save(data, os.path.join(model_folder,'model_merged.pth'))
    print('finished SWA ')


if __name__ == '__main__':
    cfg_path = r"/home/<USER>/Projects/econn/workspace/ecoindoor2020/MobileV2_OneFCOS_20210208/SWA_ada_fix_lr/train_config.yml"
    model_folder = r"/home/<USER>/Projects/econn/workspace/ecoindoor2020/MobileV2_OneFCOS_20210208/SWA_ada_fix_lr"
    update_config(cfg, cfg_path)
    main(cfg, model_folder, 12)

