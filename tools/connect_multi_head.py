"""
Create by zhangdezheng
2022/06/27
"""
import torch
from econn.models.architecture.builder import build_model
from econn.utils.config import cfg, update_config

class get_state_dict():
    def __init__(self, one_model_path):
        super(get_state_dict, self).__init__()
        self.model_path = one_model_path
        self.state_dict_tmp = {}

    def model_state_dict(self, num):
        m_checkpoint = torch.load(self.model_path, map_location=lambda storage, loc: storage)
        print('loaded {}, epoch {}'.format(self.model_path, m_checkpoint['epoch']))
        state_dict_tmp = m_checkpoint['state_dict']
        for k in state_dict_tmp:
            if k.startswith('module') and not k.startswith('module_list'):
                self.state_dict_tmp[k[7:]] = m_checkpoint['state_dict'][k]
            else:
                flg = m_checkpoint['state_dict'][k]
                if k.split('.')[0] == 'neck':
                    k = k.replace('neck', 'neck.neck_'+str(num))
                if k.split('.')[0] == 'task_head':
                    k = k.replace('task_head', 'task_heads.task_'+str(num))
                self.state_dict_tmp[k] = flg
        return self.state_dict_tmp

def load_model(heads_model_path, model):
    multi_model_state_dict = model.state_dict()
    heads_state_dict = []
    for i, head_path in enumerate(heads_model_path):
        model_state = get_state_dict(head_path)
        state_dict_ = model_state.model_state_dict(i+1)
        heads_state_dict.append(state_dict_)
    for k in multi_model_state_dict:
        for head_state_dict in heads_state_dict:
            if k in head_state_dict:
                if head_state_dict[k].shape != multi_model_state_dict[k].shape:
                    print('Skip loading parameter {}, required shape{}, ' \
                          'but loaded shape{}.'.format(
                        k, multi_model_state_dict[k].shape, head_state_dict[k].shape))
                multi_model_state_dict[k] = head_state_dict[k]

    model.load_state_dict(multi_model_state_dict, strict=False)

    return model

def creat_model(config_path):
    update_config(cfg, config_path)
    model = build_model(cfg.model)
    model = model.to('cuda:0')
    return model

if __name__ == '__main__':
    config_path1 = r"/home/<USER>/code_paper/econn-master/exp/multi_task/experiment_48/multi_head_config.yml"
    model_path1 = r"/home/<USER>/code_paper/econn-master/exp/multi_task/experiment_48/base_model/model_best_score.pth"
    model_path2 = r"/home/<USER>/code_paper/econn-master/exp/multi_task/experiment_48/3_class_model/model_best_score_48_change.pth"
    save_path = '/home/<USER>/code_paper/econn-master/exp/multi_task/experiment_48/test/multi_task_model.pth'

    multi_model = creat_model(config_path1)
    models_path = [model_path1, model_path2]
    multi_model = load_model(models_path, multi_model)
    state_dict = multi_model.state_dict()
    data = {'epoch': 165,
            'state_dict': state_dict,
            'iter': 1
            }
    torch.save(data, save_path)

