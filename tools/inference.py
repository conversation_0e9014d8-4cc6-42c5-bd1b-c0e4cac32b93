"""
Create by Chengqi.Lv
2020/3/26
"""

import torch
import os
import cv2
import numpy as np
import time
import copy
from econn.models.architecture.builder import build_model
from econn.utils.config import cfg, update_config
from econn.utils.transforms import get_affine_transform


def load_model(load_path, model):
    checkpoint = torch.load(load_path, map_location=lambda storage, loc: storage)
    print('loaded {}, epoch {}'.format(load_path, checkpoint['epoch']))
    state_dict_ = checkpoint['state_dict']
    state_dict = {}

    # convert data_parallal to model
    for k in state_dict_:
        if k.startswith('module') and not k.startswith('module_list'):
            state_dict[k[7:]] = state_dict_[k]
        else:
            state_dict[k] = state_dict_[k]
    model_state_dict = model.state_dict()

    # check loaded parameters and created model parameters
    for k in state_dict:
        if k in model_state_dict:
            if state_dict[k].shape != model_state_dict[k].shape:
                print('Skip loading parameter {}, required shape{}, ' \
                      'loaded shape{}.'.format(
                    k, model_state_dict[k].shape, state_dict[k].shape))
                state_dict[k] = model_state_dict[k]
        else:
            print('Drop parameter {}.'.format(k))
    for k in model_state_dict:
        if not (k in state_dict):
            print('No param {}.'.format(k))
            state_dict[k] = model_state_dict[k]
    model.load_state_dict(state_dict, strict=False)

    return model



class Detector(object):
    def __init__(self, cfg, checkpoint, device='cuda:0'):
        self.cfg = cfg
        self.device = device
        model = build_model(cfg.model)
        model = load_model(checkpoint, model)
        self.model = model.to(device).eval()
        self.input_shape = tuple(self.cfg.data.val.input_size)
        self.resize_keep_ratio = self.cfg.data.val.resize_keep_ratio
        self.mean = np.array(self.cfg.data.val.img_norm.mean,
                             dtype=np.float32).reshape(1, 1, 3)
        self.std = np.array(self.cfg.data.val.img_norm.std,
                            dtype=np.float32).reshape(1, 1, 3)

    def inference(self, img):
        img_info = {}
        if isinstance(img, str):
            img_info['file_name'] = os.path.basename(img)
            img = cv2.imread(img)
        else:
            img_info['file_name'] = None

        height, width,= img.shape[:2]
        img_info['height'] = height
        img_info['width'] = width
        meta = dict(img_info=img_info,
                    raw_img=img,
                    img=self.preprocess(img))
        with torch.no_grad():
            dets = self.model.inference(meta, self.resize_keep_ratio)
        return dets, meta

    def show(self, dets, meta, class_names, score_thres, wait=0):
        time1 = time.time()
        img = self.model.task_head.show_result(meta['raw_img'], dets, class_names, score_thres=score_thres, show=True)
        cv2.imshow('det', img)
        print('viz time: {:.3f}s'.format(time.time()-time1))
        return img

    def save(self, dets, meta, class_names, score_thres, img_name, save_dir):
        time1 = time.time()
        img = self.model.task_head.show_result(meta['raw_img'], dets, class_names, score_thres=score_thres, show=True)
        cv2.imwrite(save_dir+img_name.split('/')[-1], img)
        print('viz time: {:.3f}s'.format(time.time()-time1))

    def preprocess(self, img):
        time1 = time.time()
        height, width, = img.shape[:2]
        c = np.array([width / 2., height / 2.], dtype=np.float32)
        s = np.array([width, height], dtype=np.float32)

        trans_affine = get_affine_transform(
            c, s, 0, self.input_shape, resize_keep_ratio=self.resize_keep_ratio)

        trans_img = cv2.warpAffine(img, trans_affine, self.input_shape, flags=cv2.INTER_LINEAR)

        trans_img = ((trans_img.astype(np.float32) / 255.) - self.mean) / self.std
        print('pre-process time: {:.3f}s'.format(time.time() - time1), end=' | ')
        return torch.from_numpy(trans_img.transpose(2, 0, 1)).unsqueeze(0).to(self.device)


class MultiTaskDetector(Detector):
    def __init__(self, cfg, checkpoint, device='cuda:0'):
        self.cfg = cfg
        self.device = device
        model = build_model(cfg.model)
        model = load_model(checkpoint, model)
        self.model = model.to(device).eval()
        self.input_shape = tuple(self.cfg.input_setting.input_size)
        self.resize_keep_ratio = self.cfg.input_setting.resize_keep_ratio
        self.mean = np.array(self.cfg.input_setting.img_norm.mean,
                             dtype=np.float32).reshape(1, 1, 3)
        self.std = np.array(self.cfg.input_setting.img_norm.std,
                            dtype=np.float32).reshape(1, 1, 3)

    def show(self, dets, meta, class_names, score_thres, wait=0):
        time1 = time.time()
        for task, det in dets.items():
            img = self.model.task_heads[task].show_result(copy.deepcopy(meta['raw_img']), det, class_names[task], score_thres=score_thres, show=False)
            cv2.imshow('det_'+task, img)
        print('viz time: {:.3f}s'.format(time.time()-time1))

