"""
Create by Chengqi.Lv
2020/5/26
"""

from econn.utils.config import cfg, update_config
from econn.datasets.builder import build_dataset
from econn.models.architecture.builder import build_model
from econn.trainers.base_trainer import BaseTrainer
from econn.utils.collate import collate_function
from econn.utils.logger.tensorboard import TensorboardLogger
from econn.evaluate.builder import build_evaluator
import os
import torch
import logging
from econn.models.backbones.repvgg import whole_model_convert


def main(config):
    if not os.path.exists(config.save_dir):
        os.makedirs(config.save_dir)

    fmt = '%(asctime)s %(levelname)s: %(message)s'
    logging.basicConfig(level=logging.INFO,
                        format=fmt,
                        filename=os.path.join(config.save_dir, 'val_logs.txt'),
                        filemode='w',
                        datefmt='%a, %d %b %Y %H:%M:%S'
                        )
    console = logging.StreamHandler()
    console.setLevel(logging.INFO)
    formatter = logging.Formatter(fmt)
    console.setFormatter(formatter)
    logging.getLogger().addHandler(console)

    SEED = 0
    torch.manual_seed(SEED)
    torch.backends.cudnn.enabled = True
    torch.backends.cudnn.benchmark = True
    device = torch.device('cuda')
    logging.info('Creating model...')
    model = build_model(config.model)

    # TODO: the following part is for repvgg
    # config['model']['backbone']['deploy'] = True
    # # config['model']['neck']['name'] = 'FPN'
    # model_test = build_model(config.model)
    # deploy_model = whole_model_convert(model, model_test)
    # model = deploy_model

    logger = TensorboardLogger(config.save_dir, -1)

    if hasattr(model, 'encoder'):
        encoder = model.encoder
    else:
        encoder = None
    val_dataset = build_dataset(config.data.val, 'test', encoder=encoder)
    evaluator = build_evaluator(config.train.val_type, val_dataset, logger)
    trainer = BaseTrainer(config, model=model, evaluator=evaluator, logger=logger)
    trainer.build_optimizer()
    if 'load_model' in config.train or 'resume' in config.train:
        trainer.load_model(config)
    else:
        raise SystemError('No checkpoint loaded! Please check your config file!!!')

    trainer.set_device(config.train.gpu_ids, batchsize=config.train.batchsize, device=device)
    logging.info('Setting up data...')

    val_loader = torch.utils.data.DataLoader(
        val_dataset,
        batch_size=1,
        shuffle=False,
        num_workers=1,
        pin_memory=True,
        collate_fn=collate_function,
        drop_last=True,
    )

    logging.info('Starting evaluation...')

    with torch.no_grad():
        results, val_loss_dict = trainer.val(trainer.epoch-1, val_loader)
        trainer.evaluator.run_eval(results, cfg.save_dir, trainer.epoch-1)



if __name__ == '__main__':
    update_config(cfg, r"D:\Projects\ECO_NN\workspace\eco_indoor\MobileNeXt_GFLv2_dist_20200911\train_config.yml")
    main(cfg)