"""
Create by Chengqi.Lv
2020/3/25
"""

from econn.utils.config import cfg, update_config
from econn.datasets.builder import build_dataset
from econn.models.architecture.builder import build_model
from econn.trainers.base_trainer import BaseTrainer
from econn.trainers.dist_trainer import DistTrainer
from econn.utils.collate import collate_function
from econn.utils.logger.tensorboard import TensorboardLogger
from econn.evaluate.builder import build_evaluator
import os
import torch
import logging
import argparse
import torch.distributed as dist


parser = argparse.ArgumentParser()
parser.add_argument('config', help='train config file path')
parser.add_argument('--local_rank', default=-1, type=int,
                    help='node rank for distributed training')
args = parser.parse_args()


def main(config):
    if not os.path.exists(config.save_dir):
        os.makedirs(config.save_dir)

    fmt = '%(asctime)s %(levelname)s: %(message)s'
    logging.basicConfig(level=logging.INFO,
                        format=fmt,
                        filename=os.path.join(config.save_dir, 'logs.txt'),
                        filemode='w',
                        datefmt='%a, %d %b %Y %H:%M:%S'
                        )
    console = logging.StreamHandler()
    console.setLevel(logging.INFO)
    formatter = logging.Formatter(fmt)
    console.setFormatter(formatter)
    logging.getLogger().addHandler(console)

    SEED = 0
    torch.manual_seed(SEED)

    torch.backends.cudnn.enabled = True
    torch.backends.cudnn.benchmark = True

    device = torch.device('cuda')

    logging.info('Creating model...')

    model = build_model(config.model)
    logger = TensorboardLogger(config.save_dir, int(args.local_rank))

    # 可以选择在dataloder里提前encode，也可以选择在计算loss时encode
    if hasattr(model, 'encoder'):
        encoder = model.encoder
    else:
        encoder = None

    train_dataset = build_dataset(config.data.train, 'train', encoder=encoder)
    val_dataset = build_dataset(config.data.val, 'test', encoder=encoder)

    use_distributed = False
    if len(config.train.gpu_ids) > 1:
        use_distributed = True
        rank = int(args.local_rank)
        print('rank = ', args.local_rank)
        num_gpus = torch.cuda.device_count()
        torch.cuda.set_device(rank % num_gpus)

        dist.init_process_group(backend='nccl')
        train_sampler = torch.utils.data.distributed.DistributedSampler(train_dataset)

    evaluator = build_evaluator(config.train.val_type, val_dataset, logger)

    if use_distributed:
        trainer = DistTrainer(config, model=model, evaluator=evaluator, logger=logger)
        trainer.set_device(int(args.local_rank), batchsize=config.train.batchsize//num_gpus, device=device)
    else:
        trainer = BaseTrainer(config, model=model, evaluator=evaluator, logger=logger)
        trainer.set_device(config.train.gpu_ids, batchsize=config.train.batchsize, device=device)

    trainer.build_optimizer()

    if 'load_model' in config.train or 'resume' in config.train:
        trainer.load_model(config)

    logging.info('Setting up data...')
    if use_distributed:
        train_loader = torch.utils.data.DataLoader(train_dataset,
                                                   batch_size=config.train.batchsize//num_gpus,
                                                   num_workers=config.train.num_workers//num_gpus,
                                                   pin_memory=True,
                                                   collate_fn=collate_function,
                                                   sampler=train_sampler,
                                                   drop_last=True)
    else:
        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=config.train.batchsize,
            shuffle=True,
            num_workers=config.train.num_workers,
            pin_memory=True,
            collate_fn=collate_function,
            drop_last=True,
        )

    val_loader = torch.utils.data.DataLoader(
        val_dataset,
        batch_size=1,
        shuffle=False,
        num_workers=1,
        pin_memory=True,
        collate_fn=collate_function,
        drop_last=True,
    )

    logging.info('Starting training...')

    trainer.run(train_loader, val_loader)



if __name__ == '__main__':
    update_config(cfg, args.config)
    main(cfg)
