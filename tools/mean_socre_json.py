import numpy as np
import pycocotools.coco as coco


# difficult_score={'bin':[],'bowl':[],'charge':[],'cloth':[],'rug':[],'shoe':[],'wire':[]}

# 计算IOU,frame= xmin,ymin,xmax,ymax
def calculate_iou(pred_box, gt_box):
    x1 = pred_box[0]
    y1 = pred_box[1]
    width1 = pred_box[2] - pred_box[0]
    height1 = pred_box[3] - pred_box[1]

    x2 = gt_box[0]
    y2 = gt_box[1]
    width2 = gt_box[2] - gt_box[0]
    height2 = gt_box[3] - gt_box[1]

    endx = max(pred_box[2], gt_box[2])
    startx = min(x1, x2)
    width = width1 + width2 - (endx - startx)

    endy = max(pred_box[3], gt_box[3])
    starty = min(y1, y2)
    height = height1 + height2 - (endy - starty)

    if width <= 0 or height <= 0:
        ratio = 0  # 重叠率为 0
    else:
        area = width * height  # 两矩形相交面积
        area1 = width1 * height1
        area2 = width2 * height2
        ratio = area * 1. / (area1 + area2 - area)
    return ratio


class MeanScore:
    def __init__(self, annot_path, result_path):
        self.coco = coco.COCO(annot_path)
        self.images = self.coco.getImgIds()
        self.num_samples = len(self.images)
        self.coco_dets = self.coco.loadRes(result_path)
        self.max_objs = 128
        # self.labels = ['bin', 'charge', 'cloth', 'rug', 'shoe', 'wire', 'rail']
        self.labels = ['PuTongMen',
                       'ShaFa',
                       'ChuangHu',
                       'ZhuoZi',
                       'ChaJi',
                       'DianShiGui',
                       'DianShi',
                       'Chuang',
                       'ChuangTouGui',
                       'MaTong',
                       'uchair']
        self.normal_score = {k: [] for k in self.labels}

    def run(self):
        for img_id in self.images:
            file_name = self.coco.loadImgs(ids=[img_id])[0]['file_name']
            gt_ids = self.coco.getAnnIds(imgIds=[img_id])
            if len(gt_ids) > 0:
                gts = self.coco.loadAnns(ids=gt_ids)
                num_objs = min(len(gts), self.max_objs)
                gt_boxes = []
                gt_cls = []
                for gt in gts:
                    cocobox = gt['bbox']
                    gtbox = [cocobox[0], cocobox[1], cocobox[0] + cocobox[2], cocobox[1] + cocobox[3]]
                    gt_boxes.append(tuple(gtbox))
                    gt_cls.append(self.labels[gt['category_id'] - 1])

                dt_ids = self.coco_dets.getAnnIds(imgIds=[img_id])
                dts = self.coco_dets.loadAnns(ids=dt_ids)
                for dt in dts:
                    if dt['score'] > min_score_thresh:
                        dt_cocobox = dt['bbox']
                        dt_box = [dt_cocobox[0], dt_cocobox[1],
                                  dt_cocobox[0] + dt_cocobox[2], dt_cocobox[1] + dt_cocobox[3]]
                        dt_iou = [calculate_iou(dt_box, gt_box) for gt_box in gt_boxes]
                        dt_class = self.labels[dt['category_id'] - 1]
                        if max(dt_iou) >= min_iou_thresh:  # iou满足阈值
                            pre_iou_max_ind = np.argmax(dt_iou)
                            if dt_class == gt_cls[pre_iou_max_ind]:  # 分类正确
                                self.normal_score[dt_class].append(dt['score'])

    def get_result(self):
        for key, value in self.normal_score.items():
            if len(value) != 0:
                print('normal {},number:{},mean_score:{}'.format(key, len(value), sum(value) / len(value)))
            else:
                print('{} has no matched box, please check your json file or model')


if __name__ == '__main__':
    min_score_thresh = 0.4
    min_iou_thresh = 0.4
    annot_path = 'D:/Dataset/eco_indoor_2020_old/annotations/ecov5.4_instances_val2019.json'
    result_path = 'D:/Projects/ECO_NN/workspace/eco_indoor/MobileNeXt_GFL_20200831_X3最终模型/model_best_score/results.json'
    meanscore = MeanScore(annot_path=annot_path, result_path=result_path)
    meanscore.run()
    meanscore.get_result()
