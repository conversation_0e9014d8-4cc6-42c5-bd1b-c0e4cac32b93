"""
Create by Chengqi.Lv
2020/4/17
"""
from econn.utils.config import cfg, update_config
from econn.models.architecture.builder import build_model
import torch


def load_model(load_path, model):
    checkpoint = torch.load(load_path, map_location=lambda storage, loc: storage)
    print('loaded {}, epoch {}'.format(load_path, checkpoint['epoch']))
    state_dict_ = checkpoint['state_dict']
    state_dict = {}

    # convert data_parallal to model
    for k in state_dict_:
        if k.startswith('module') and not k.startswith('module_list'):
            state_dict[k[7:]] = state_dict_[k]
        else:
            state_dict[k] = state_dict_[k]
    model_state_dict = model.state_dict()

    # check loaded parameters and created model parameters
    for k in state_dict:
        if k in model_state_dict:
            if state_dict[k].shape != model_state_dict[k].shape:
                print('Skip loading parameter {}, required shape{}, ' \
                      'loaded shape{}.'.format(
                    k, model_state_dict[k].shape, state_dict[k].shape))
                state_dict[k] = model_state_dict[k]
                raise ImportError('model load fail!!!')
        else:
            print('Drop parameter {}.'.format(k))
            # raise ImportError('model load fail!!!')
    for k in model_state_dict:
        if not (k in state_dict):
            print('No param {}.'.format(k))
            state_dict[k] = model_state_dict[k]
            raise ImportError('model load fail!!!')
    model.load_state_dict(state_dict, strict=False)

    return model


def main(config, model_path, output_path, input_shape=(512, 512)):
    model = build_model(config.model)
    print('build model success!')
    model = load_model(model_path, model)
    dummy_input = torch.autograd.Variable(torch.randn(1, 3, input_shape[0], input_shape[1]))
    torch.onnx.export(model, dummy_input, output_path, verbose=True, keep_initializers_as_inputs=True, opset_version=10)
    print('finished exporting onnx ')


if __name__ == '__main__':
    cfg_path = r"D:\Projects\ECO_NN\workspace\eco_indoor\MobileNeXt_GFL_20200831_X3最终模型\train_config_3stage.yml"
    model_path = r"D:\Projects\ECO_NN\workspace\eco_indoor\MobileNeXt_GFL_20200831_X3最终模型\model_best_score\model_best_score.pth"
    out_path = r'D:\Projects\ECO_NN\workspace\eco_indoor\MobileNeXt_GFL_20200831_X3最终模型\export\MobileNext_indoor_20200831.onnx'
    update_config(cfg, cfg_path)
    main(cfg, model_path, out_path, input_shape=(512, 512))
