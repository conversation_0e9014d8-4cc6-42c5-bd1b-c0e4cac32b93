import torch.nn as nn
import torch
import torch.nn.functional as F
from econn.models.backbones.xception import ConvBnRelu
from econn.models.loss.rmi_loss import RMILoss
from econn.models.loss.focal_loss import SegFocalLoss
from econn.models.loss.offset_losses import OffsetLosses
from pycocotools import mask as coco_mask_util
import numpy as np
from PIL import Image
from matplotlib import pyplot as plt
from econn.utils.visualize import getCMap
import time
from econn.ops import resize
from econn.models.loss.detail_loss import DetailAggregateLoss
loss_dict = {
    'RMILOSS':RMILoss,
   'FocalLoss':SegFocalLoss,
    'OffsetLoss': OffsetLosses,
}

class SpatialPath(nn.Module):
    def __init__(self, in_planes, out_planes, norm_layer=nn.BatchNorm2d):
        super(SpatialPath, self).__init__()
        inner_channel = 64
        self.conv_7x7 = ConvBnRelu(in_planes, inner_channel, 7, 2, 3,
                                   has_bn=True,
                                   has_relu=True, has_bias=False)
        self.conv_3x3_1 = ConvBnRelu(inner_channel, inner_channel, 3, 2, 1,
                                     has_bn=True,
                                     has_relu=True, has_bias=False)
        self.conv_3x3_2 = ConvBnRelu(inner_channel, inner_channel, 3, 2, 1,
                                     has_bn=True,
                                     has_relu=True, has_bias=False)
        self.conv_1x1 = ConvBnRelu(inner_channel, out_planes, 1, 1, 0,
                                   has_bn=True,
                                   has_relu=True, has_bias=False)

    def forward(self, x):
        x = self.conv_7x7(x)
        x = self.conv_3x3_1(x)
        x = self.conv_3x3_2(x)
        output = self.conv_1x1(x)

        return output


class AttentionRefinement(nn.Module):
    def __init__(self, in_planes, out_planes,
                 norm_layer=nn.BatchNorm2d):
        super(AttentionRefinement, self).__init__()
        self.conv_3x3 = ConvBnRelu(in_planes, out_planes, 3, 1, 1,
                                   has_bn=True,
                                   has_relu=True, has_bias=False)
        self.channel_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            ConvBnRelu(out_planes, out_planes, 1, 1, 0,
                       has_bn=True,
                       has_relu=False, has_bias=False),
            nn.Sigmoid()
        )

    def forward(self, x):
        fm = self.conv_3x3(x)
        fm_se = self.channel_attention(fm)
        fm = fm * fm_se

        return fm
# class AttentionRefinementModule(nn.Module):
#     def __init__(self, in_chan, out_chan, *args, **kwargs):
#         super(AttentionRefinementModule, self).__init__()
#         self.conv = ConvBnRelu(in_chan, out_chan, 3, stride=1, pad=1)
#         self.conv_atten = nn.Conv2d(out_chan, out_chan, kernel_size= 1, bias=False)
#         self.bn_atten = ConvBnRelu(out_chan)
#         # self.bn_atten = BatchNorm2d(out_chan, activation='none')

#         self.sigmoid_atten = nn.Sigmoid()
#         self.init_weight()

#     def forward(self, x):
#         feat = self.conv(x)
#         kernel_shape = [s for s in feat.size()[2:]]
#         atten = F.avg_pool2d(feat, feat.size()[2:])
#         atten = self.conv_atten(atten)
#         atten = self.bn_atten(atten)
#         atten = self.sigmoid_atten(atten)
#         out = torch.mul(feat, atten)
#         return out

#     def init_weight(self):
#         for ly in self.children():
#             if isinstance(ly, nn.Conv2d):
#                 nn.init.kaiming_normal_(ly.weight, a=1)
#                 if not ly.bias is None: nn.init.constant_(ly.bias, 0)

class FeatureFusion(nn.Module):
    def __init__(self, in_planes, out_planes,
                 reduction=1, norm_layer=nn.BatchNorm2d):
        super(FeatureFusion, self).__init__()
        self.conv_1x1 = ConvBnRelu(in_planes, out_planes, 1, 1, 0,
                                   has_bn=True, 
                                   has_relu=True, has_bias=False)
        self.channel_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            ConvBnRelu(out_planes, out_planes // reduction, 1, 1, 0,
                       has_bn=False, 
                       has_relu=True, has_bias=False),
            ConvBnRelu(out_planes // reduction, out_planes, 1, 1, 0,
                       has_bn=False, 
                       has_relu=False, has_bias=False),
            nn.Sigmoid()
        )
    def forward(self, x1, x2):
        fm = torch.cat([x1, x2], dim=1)
        fm = self.conv_1x1(fm)
        fm_se = self.channel_attention(fm)
        output = fm + fm * fm_se
        return output


class _GlobalAvgPooling(nn.Module):
    def __init__(self, in_channels, out_channels, norm_layer, **kwargs):
        super(_GlobalAvgPooling, self).__init__()
        self.gap = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(in_channels, out_channels, 1, bias=False),
            norm_layer(out_channels),
            nn.ReLU(True)
        )

    def forward(self, x):
        size = x.size()[2:]
        pool = self.gap(x)
        out = F.interpolate(pool, size,
                            # mode='nearest',
                            mode='bilinear', align_corners=True
                            )
        return out

class ContextPath(nn.Module):
    # channel_dict = {'ResNet34': [512, 256, 128],
    #                 'HrNet18': [144, 72, 36],
    #                 'HrNet48': [384, 192, 128],
    #                 'HrNet32': [256, 128, 128],
    #                 'ResNest50': [256, 256, 128]}
    def __init__(self, in_channels, norm_layer=nn.BatchNorm2d):
        super(ContextPath, self).__init__()

        # self.backbone = backbone


        lastchannel, secondlastchannel, inter_channels = in_channels
        self.global_context = _GlobalAvgPooling(lastchannel, inter_channels, norm_layer)

        self.arms = nn.ModuleList(
            [AttentionRefinement(lastchannel, inter_channels),
             AttentionRefinement(secondlastchannel, inter_channels)]
        )
        self.refines = nn.ModuleList(
            [ConvBnRelu(inter_channels, inter_channels, 3, 1, 1),
             ConvBnRelu(inter_channels, inter_channels, 3, 1, 1)]
            )

    def forward(self, xx):
        # if 'hrnet' in self.backbone:
        #     context_blocks = xx
        #     c4 = context_blocks[-1]
        # elif 'resnet' in self.backbone:
        #     context_blocks = []
        #     for x in xx:
        #         context_blocks.append(x)
        context_blocks = xx
        context_blocks.reverse()

        # for i in range(len(xx)):
        #     tensor0 = F.softmax(xx[i],dim=1).cpu().numpy()[0][1]
        #     plt.imshow(tensor0)
        #     plt.show()
        # tensor1 = F.softmax(xx[1],dim=1).cpu().numpy()[0][0]
        # tensor2 = F.softmax(xx[2],dim=1).cpu().numpy()[0][0]
        # tensor3 = F.softmax(xx[3],dim=1).cpu().numpy()[0][0]
        #
        # tmp0=Image.fromarray(tensor0)
        # tmp1 = Image.fromarray(tensor1)
        # tmp2 = Image.fromarray(tensor2)
        # tmp3 = Image.fromarray(tensor3)
        #
        # tmp0.show()
        # tmp1.show()
        # tmp2.show()
        # tmp3.show()


        global_context = self.global_context(context_blocks[0])
        global_context = F.interpolate(global_context,
                                       size=context_blocks[0].size()[2:],
                                       mode='bilinear', align_corners=True)
        last_feature = global_context
        context_outputs = []
        for i, (feature, arm, refine) in enumerate(zip(context_blocks[:2], self.arms, self.refines)):
            feature = arm(feature)
            feature += last_feature
            last_feature = F.interpolate(feature,
                                         scale_factor=2,
                                         # size=context_blocks[i + 1].size()[2:],
                                         mode='bilinear', align_corners=True)
            last_feature = refine(last_feature)
            context_outputs.append(last_feature)

        return context_outputs

class _BiSeHead(nn.Module):
    def __init__(self, in_channels, inter_channels, out_channels):
        super(_BiSeHead, self).__init__()
        self.block = nn.Sequential(
            ConvBnRelu(in_channels, inter_channels, 3, 1, 1),
            nn.Dropout(0.1),
            nn.Conv2d(inter_channels, out_channels, 1)
        )
    def forward(self, x):
        x = self.block(x)
        return x

class RethinkBiSeNetHrNetHead(nn.Module):
    def __init__(self,
                 num_classes,
                 aux,
                 in_channels,
                 loss_cfg,
                 ):
        super(RethinkBiSeNetHrNetHead, self).__init__()
        self.in_channels = in_channels
        self.spatial_path = SpatialPath(3, 128)
        self.context_path = ContextPath(in_channels)

        self.ffm = FeatureFusion(256, 256, 4)
        self.aux = aux
        self.head = _BiSeHead(256, 64, num_classes)
        if self.aux:
            self.aux_layer1 = _BiSeHead(128, 256, num_classes)
            self.aux_layer2 = _BiSeHead(128, 256, num_classes)

        self.loss_function = {}
        for loss in loss_cfg:
            loss_cfg_ = loss_cfg[loss]
            self.loss_function[loss] = loss_dict[loss_cfg_.name](
                nclass=num_classes,
                aux=self.aux,
                aux_weight=loss_cfg_.aux_weight,
                loss_weight = loss_cfg_.loss_weight,
            )
        # self.loss_function = loss_dict[loss_cfg.name](
        #                                                 nclass =num_classes,
        #                                                 aux=self.aux,
        #                                                 aux_weight = loss_cfg.aux_weight
        #                                                         )
        inplanes = 256
        self.arm32 = AttentionRefinement(inplanes, 128)
        self.conv_head32 = ConvBnRelu(128, 128, ksize=3, stride=1, pad=1)
        self.conv_head16 = ConvBnRelu(128, 128, ksize=3, stride=1, pad=1)
        self.conv_avg = ConvBnRelu(inplanes, 128, ksize=1, stride=1, pad=0)
        self.arm16 = AttentionRefinement(128, 128)
        
        # self.conv_out_sp16 = _BiSeHead(384, 64, 1)
        self.conv_out_sp8 = _BiSeHead(64, 64, 1)
        # self.conv_out_sp4 = _BiSeHead(96, 64, 1)
        # self.conv_out_sp2 = _BiSeHead(64, 64, 1)

        self.conv_out = _BiSeHead(256, 256, num_classes)
        self.conv_out16 = _BiSeHead(128, 64, num_classes)
        self.conv_out32 = _BiSeHead(128, 64, num_classes)
        self.conv64128 = nn.Conv2d(64,128,1)
        self.boundary_loss_func = DetailAggregateLoss()
    def forward(self, x):
        H,W = x[0].size()[2:]

        feat4, feat8, feat16, feat32, feat2 = x[1]
        # feat4, feat8, feat16, feat32, feat2 = x
        H8, W8 = feat8.size()[2:]
        H16, W16 = feat16.size()[2:]
        H32, W32 = feat32.size()[2:]
        # x_shape = [int(x) for x in feat32.size()[2:]]
        x_shape = (16, 16)
        avg = F.avg_pool2d(feat32, x_shape)

        avg = self.conv_avg(avg)
        avg_up = F.interpolate(avg, (H32, W32), mode='nearest')

        feat32_arm = self.arm32(feat32)
        feat32_sum = feat32_arm + avg_up
        feat32_up = F.interpolate(feat32_sum, (H16, W16), mode='nearest')
        feat32_up = self.conv_head32(feat32_up)

        feat16_arm = self.arm16(feat16)
        feat16_sum = feat16_arm + feat32_up
        feat16_up = F.interpolate(feat16_sum, (H8, W8), mode='nearest')
        feat16_up = self.conv_head16(feat16_up)

        # feat_out_sp2 = self.conv_out_sp2(feat2)

        # feat_out_sp4 = self.conv_out_sp4(feat4)
  
        feat_out_sp8 = self.conv_out_sp8(feat8)

        # feat_out_sp16 = self.conv_out_sp16(feat16)


        feat_fuse = self.ffm(self.conv64128(feat8), feat16_up)

        feat_out = self.conv_out(feat_fuse)
        feat_out16 = self.conv_out16(feat16_up)
        feat_out32 = self.conv_out32(feat32_up)

        feat_out = F.interpolate(feat_out, (H, W), mode='bilinear', align_corners=True)
        feat_out16 = F.interpolate(feat_out16, (H, W), mode='bilinear', align_corners=True)
        feat_out32 = F.interpolate(feat_out32, (H, W), mode='bilinear', align_corners=True)
        
        
        final_out = []
        # feat_out = feat_out.permute(0,2,3,1)
        final_out.append(feat_out)
        final_out.append(feat_out16)
        final_out.append(feat_out32)
        final_out.append(feat_out_sp8)
        # if self.training:
            # return feat_out, feat_out16, feat_out32, feat_out_sp8
        # else:
        return tuple(final_out)
        # return tuple(final_out)

    def decode(self, preds, meta, resize_keep_ratio):
        # self.show_heatmap(preds)
        preds_argmax = torch.argmax(preds[0], dim= 1).byte()
        # preds_argmax = preds_argmax.squeeze(0).cpu().numpy()####batchsize > 1 TODO
        # segms = segmentationToCocoResult(preds_argmax, stuffStartId=0)#
        # return tuple([segms, preds_argmax])

        # preds_argmax = torch.argmax(preds[0], dim= 1).byte()
        segms = [segmentationToCocoResult(preds_argmax[i].cpu().numpy(), stuffStartId=0) for i in
                 range(preds_argmax.shape[0])]  #
        return tuple([segms, preds_argmax.cpu().numpy()])

    def show_heatmap(self, preds):
        for i in range(len(list(preds))):
            tt = F.softmax(preds[i], dim=1)
            ttt = tt.cpu().numpy()[0][1]

    def loss(self,
             preds,#tuple
             gt_meta,
             ):
        boundery_bce_loss = 0.
        boundery_dice_loss = 0.
        gt_mask = gt_meta['gt_masks']
        if preds[0].device != gt_mask.device:
            gt_mask = gt_mask.to(preds[0].device)
        
        for loss in self.loss_function.values():
            lossp = loss([preds[0]],gt_mask)
            loss2 = loss([preds[1]],gt_mask)
            loss3 = loss([preds[2]],gt_mask)
        
        
        boundery_bce_loss8,  boundery_dice_loss8 = self.boundary_loss_func(preds[3], gt_mask)
        boundery_bce_loss += boundery_bce_loss8 * 10.0
        boundery_dice_loss += boundery_dice_loss8


        loss = lossp + loss2 + loss3 + boundery_dice_loss + boundery_bce_loss

        # loss = sum(loss(preds,gt_mask) for loss in self.loss_function.values())
        loss = torch.sum(loss)
        loss_states = dict(loss = loss)
        loss_states['boundery bce loss:'] = boundery_bce_loss
        loss_states['boundery dice loss:'] = boundery_dice_loss
        return loss, loss_states

    def show_result(self,img, dets, class_names, score_thres=0.9, show=True, save_dir =None):
        _, mask = dets
        mask = mask[0]
        mask_unique = np.unique(mask)
        mask_tmp = np.zeros_like(mask)
        for i in range(len(mask_unique)):
            mask_tmp[mask == mask_unique[i]] = i * 2#+ 1
        mask_tmp = mask_tmp.astype(np.int8)
        cmap = getCMap(stuffStartId=1, stuffEndId=240)
        cmap = (cmap * 255).astype(int)
        padding = np.zeros((256 - cmap.shape[0], 3), np.int8)
        cmap = np.vstack((cmap, padding))
        cmap = cmap.reshape((-1))
        assert len(cmap) == 768, 'Error: Color map must have exactly 256*3 elements!'

        # Write to png file
        png = Image.fromarray(mask_tmp).convert('P')
        png.putpalette(list(cmap))
        # png.show()
        return png


    def init_weights(self):
        print('initialized weights')
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.normal_(m.weight, std=0.001)
                # torch.nn.init.kaiming_normal_(m.weight.data, nonlinearity='relu')
                # torch.nn.init.xavier_normal_(m.weight.data)
                # if m.bias is not None:
                #     m.bias.data.zero_()
            elif isinstance(m, nn.BatchNorm2d):
                m.weight.data.fill_(1)
                m.bias.data.zero_()


def segmentationToCocoMask(labelMap, labelId):
    '''
    Encodes a segmentation mask using the Mask API.
    :param labelMap: [h x w] segmentation map that indicates the label of each pixel
    :param labelId: the label from labelMap that will be encoded
    :return: Rs - the encoded label mask for label 'labelId'
    '''
    labelMask = labelMap == labelId
    labelMask = np.expand_dims(labelMask, axis=2)
    labelMask = labelMask.astype('uint8')
    labelMask = np.asfortranarray(labelMask)
    Rs = coco_mask_util.encode(labelMask)
    assert len(Rs) == 1
    Rs = Rs[0]

    return Rs

def segmentationToCocoResult(labelMap, stuffStartId=0):
    '''
    Convert a segmentation map to COCO stuff segmentation result format.
    :param labelMap: [h x w] segmentation map that indicates the label of each pixel
    :param imgId: the id of the COCO image (last part of the file name)
    :param stuffStartId: (optional) index where stuff classes start
    :return: anns    - a list of dicts for each label in this image
       .image_id     - the id of the COCO image
       .category_id  - the id of the stuff class of this annotation
       .segmentation - the RLE encoded segmentation of this class
    '''

    # Get stuff labels
    shape = labelMap.shape
    if len(shape) != 2:
        raise Exception(('Error: Image has %d instead of 2 channels! Most likely you '
        'provided an RGB image instead of an indexed image (with or without color palette).') % len(shape))
    [h, w] = shape
    assert h > 0 and w > 0
    labelsAll = np.unique(labelMap)
    labelsStuff = [i for i in labelsAll if i >= stuffStartId]

    # Add stuff annotations
    anns = []
    for labelId in labelsStuff:

        # Create mask and encode it
        Rs = segmentationToCocoMask(labelMap, labelId)

        # Create annotation data and add it to the list
        anndata = {}
        anndata['category_id'] = int(labelId)# + 1
        anndata['segmentation'] = Rs
        anns.append(anndata)
    return anns