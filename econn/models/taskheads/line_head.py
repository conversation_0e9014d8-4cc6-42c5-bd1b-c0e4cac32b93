"""
Create by shen huixiang
2020/8/1
"""
from functools import partial
from os import popen
from PIL.Image import NONE
from matplotlib.pyplot import grid, imshow
from numpy.core.defchararray import index
from numpy.core.fromnumeric import size
import torch
from torch._C import device
import torch.nn as nn
import torch.nn.functional as F
import torch.distributed as dist
import numpy as np
import cv2
import matplotlib.pyplot as plt
from torch.nn.modules.activation import Sigmoid
from econn.utils.visualize import add_coco_bbox
from econn.models.operators.scale import Scale
from econn.models.operators.conv import ConvModule
from econn.models.operators.init_weights import normal_init
from econn.utils.transforms import distance2bbox, bbox2distance
from econn.utils.bbox_nms import multiclass_nms

import econn.models.loss.iou_loss as iou_losses
from econn.models.loss.dice_loss import dice_loss
from econn.models.loss.focal_loss import py_sigmoid_focal_loss
from .anchor.anchor_target import multi_apply
from .anchor.base_anchor_head import AnchorHead


def reduce_mean(tensor):
    if not (dist.is_available() and dist.is_initialized()):
        return tensor
    tensor = tensor.clone()
    dist.all_reduce(tensor.true_divide(
        dist.get_world_size()), op=dist.ReduceOp.SUM)
    return tensor


# class Integral(nn.Module):
#     """A fixed layer for calculating integral result from distribution.
#     This layer calculates the target location by :math: `sum{P(y_i) * y_i}`,
#     P(y_i) denotes the softmax vector that represents the discrete distribution
#     y_i denotes the discrete set, usually {0, 1, 2, ..., reg_max}
#     Args:
#         reg_max (int): The maximal value of the discrete set. Default: 16. You
#             may want to reset it according to your new dataset or related
#             settings.
#     """

#     def __init__(self, reg_max=16):
#         super(Integral, self).__init__()
#         self.reg_max = reg_max
#         self.register_buffer('project',
#                              torch.linspace(0, self.reg_max, self.reg_max + 1))

#     def forward(self, x):
#         """Forward feature from the regression head to get integral result of
#         bounding box location.
#         Args:
#             x (Tensor): Features of the regression head, shape (N, 4*(n+1)),
#                 n is self.reg_max.
#         Returns:
#             x (Tensor): Integral result of box locations, i.e., distance
#                 offsets from the box center in four directions, shape (N, 4).
#         """
#         x = F.softmax(x.reshape(-1, self.reg_max + 1), dim=1)
#         x = F.linear(x, self.project.type_as(x)).reshape(-1, 4)
#         return x


class LineHead(AnchorHead):
    """
    Generalized Focal Loss: Learning Qualified and Distributed Bounding Boxes
    for Dense Object Detection
    GFL head structure is similar with ATSS, however GFL uses
    1) QFL to supervise joint representation of iou score and localization quality
    2) DFL to supervise single peak constraint of distributed bounding boxes
    """

    def __init__(self,
                 num_classes,
                 loss,
                 input_channel,
                 stacked_convs=4,
                 octave_base_scale=4,
                 scales_per_octave=1,
                 conv_cfg=None,
                 norm_cfg=dict(type='GN', num_groups=32, requires_grad=True),
                 reg_max=16,
                 **kwargs):
        self.stacked_convs = stacked_convs
        self.octave_base_scale = octave_base_scale
        self.scales_per_octave = scales_per_octave
        self.loss_cfg = loss
        self.conv_cfg = conv_cfg
        self.norm_cfg = norm_cfg
        self.reg_max = reg_max
        use_sigmoid = True
        octave_scales = np.array(
            [2 ** (i / scales_per_octave) for i in range(scales_per_octave)])
        anchor_scales = octave_scales * octave_base_scale
        super(LineHead, self).__init__(num_classes,
                                       loss,
                                       use_sigmoid,
                                       input_channel,
                                       anchor_scales=anchor_scales,
                                       **kwargs)


    def _init_layers(self):
        self.relu = nn.ReLU(inplace=True)
        self.cls_convs = nn.ModuleList()
        self.reg_convs = nn.ModuleList()
        for i in range(self.stacked_convs):
            chn = self.in_channels if i == 0 else self.feat_channels
            self.cls_convs.append(
                ConvModule(
                    chn,
                    self.feat_channels,
                    3,
                    stride=1,
                    padding=1,
                    conv_cfg=self.conv_cfg,
                    norm_cfg=self.norm_cfg))
            self.reg_convs.append(
                ConvModule(
                    chn,
                    self.feat_channels,
                    3,
                    stride=1,
                    padding=1,
                    conv_cfg=self.conv_cfg,
                    norm_cfg=self.norm_cfg))
        self.gfl_cls = nn.Conv2d(
            self.feat_channels,
            self.cls_out_channels,
            3,
            padding=1)
        self.line_reg =nn.Conv2d(self.feat_channels, 2, 3, padding=1)
        #self.scales = nn.ModuleList([Scale(1.0) for _ in self.anchor_strides])
        self.scale = nn.Sigmoid()
    def init_weights(self):
        for m in self.cls_convs:
            normal_init(m.conv, std=0.01)
        for m in self.reg_convs:
            normal_init(m.conv, std=0.01)
        bias_cls = -4.595  # 用0.01的置信度初始化
        normal_init(self.gfl_cls, std=0.01, bias=bias_cls)
        normal_init(self.line_reg, std=0.01)

    def forward(self, feats):
        return multi_apply(self.forward_single, feats)

    def forward_single(self, x):
        cls_feat = x
        reg_feat = x
        for cls_conv in self.cls_convs:
            cls_feat = cls_conv(cls_feat)
        for reg_conv in self.reg_convs:
            reg_feat = reg_conv(reg_feat)
        cls_score = self.gfl_cls(cls_feat)
        line_pred =self.scale(self.line_reg(reg_feat))
        return cls_score,line_pred

    # def anchor_center(self, anchors):
    #     anchors_cx = (anchors[:, 2] + anchors[:, 0]) / 2
    #     anchors_cy = (anchors[:, 3] + anchors[:, 1]) / 2
    #     return torch.stack([anchors_cx, anchors_cy], dim=1)

    def loss_single(self, fmap_grid_labels,cls_scores,lines_pred=None,dec=None):
        loss=torch.nn.BCEWithLogitsLoss()
        #cls_scores=cls_scores.detach().sigmoid()
        target_index=torch.where(dec>-1)
        target=dec[target_index]
        pred=lines_pred[target_index]
        loss_clf=loss(cls_scores,fmap_grid_labels)
        #loss_clf = py_sigmoid_focal_loss(cls_scores,fmap_grid_labels)
        loss_dice=dice_loss(cls_scores,fmap_grid_labels)
        loss_line_reg=F.mse_loss(pred, target, reduction='mean')
        

        return loss_clf,loss_dice,loss_line_reg

    def loss(self,
             preds,
             gt_meta
             ):
        cls_scores,lines_pred= preds
        gt_lines_points = gt_meta['lines_points']
        input_height, input_width = gt_meta['img'].shape[2:]
        #img_shapes = [[input_height, input_width] for i in range(cls_scores[0].shape[0])]
        featmap_sizes = [featmap.size() for featmap in cls_scores]
        # TODO:根据featmap_sizes生程不同级别的label
        #assert len(featmap_sizes) == len(self.anchor_generators)

        device = cls_scores[0].device
        fmap_grid_labels = self.get_labels(featmap_sizes, self.anchor_strides, gt_lines_points,device)
        fmap_grid_labels,fmap_grid_decs=self.get_labels_with_dec(featmap_sizes, self.anchor_strides, gt_lines_points,device)

        losses_clf,losses_dice,loss_lines_reg = multi_apply( self.loss_single,fmap_grid_labels,cls_scores,lines_pred,fmap_grid_decs)
        losses_clf=sum(losses_clf)
        loss_dice=sum(losses_dice)
        loss_lines_reg=sum(loss_lines_reg)
        loss = losses_clf+loss_dice+loss_lines_reg
        #loss_states = {self.loss_cfg.loss_qfl.name: losses_clf}
        loss_states = {"BCE": losses_clf,"DICE":loss_dice,"lines_reg":loss_lines_reg}
        return loss, loss_states

    def get_labels(self, fmap_size, anchor_strides, gt_lines_points,device):  
        grid_labels = [torch.zeros(size).to(device)
                       for size in fmap_size]
        #fmap_points_index = [[np.unique(np.floor(points/stride), axis=0) for points in gt_lines_points] for stride in anchor_strides]
        fmap_points_index = [[np.unique(np.floor(points/stride), axis=0) for points in gt_lines_points] for stride in anchor_strides] 
        for fmap_labels,points_index in zip(grid_labels,fmap_points_index):
            for x in range(len(points_index)):
                b_index=(np.ones(len(points_index[x]))*x).astype(np.int)
                c_index=(np.ones(len(points_index[x]))*0).astype(np.int)
                w_index=points_index[x][:,0]
                h_index=points_index[x][:,1]
                fmap_labels[b_index,c_index,h_index,w_index]=1
        return grid_labels
    
    def get_labels_with_dec(self, fmap_size, anchor_strides, gt_lines_points,device):

        def get_int_dec(points,stride):
            points=points/stride
            int_index=points.astype(np.int)
            dec=points-int_index
            fmap_points_index,dec_index=np.unique(int_index,axis=0,return_index=True)
            fmap_dec=dec[dec_index]
            return fmap_points_index,fmap_dec

    
        grid_labels = [torch.zeros(size).to(device)
                       for size in fmap_size]
        grid_dec = [torch.zeros([size[0],2,size[2],size[3]]).to(device)-1 for size in fmap_size]
        #fmap_points_index = [[np.unique(np.floor(points/stride), axis=0) for points in gt_lines_points] for stride in anchor_strides]
        fmap_points_index_dec = [[get_int_dec(points,stride) for points in gt_lines_points] for stride in anchor_strides] 
        batch_index_with_dec=[]
        for fmap_labels,dec,points_index_dec in zip(grid_labels,grid_dec,fmap_points_index_dec):
            for x in range(len(points_index_dec)):
                w_index=points_index_dec[x][0][:,0]
                h_index=points_index_dec[x][0][:,1]
                w_dec=torch.Tensor(points_index_dec[x][1][:,0]).to(device)
                h_dec=torch.Tensor(points_index_dec[x][1][:,1]).to(device)
                dec[x,0,h_index,w_index]=w_dec
                dec[x,1,h_index,w_index]=h_dec
                fmap_labels[x,0,h_index,w_index]=1
        return grid_labels,grid_dec

    def decode(self, preds, meta, resize_keep_ratio):
        cls_scores,lines_pred= preds

        org_img_hw=meta["img_info"]["height"],meta["img_info"]["width"]
        multi_app_hw=[org_img_hw for x in range(len(cls_scores))]
        #heatmap=cls_scores[0].sigmoid().cpu().numpy()
        grid_pred,decode_res = multi_apply(self.get_single_pred,cls_scores,lines_pred,multi_app_hw)
        
        #----------------计算grid bbox-------------------
        
        # preds_h,preds_w=grid_pred[0].shape[-2:]
        # size_ratio=img_h/preds_h,img_w/preds_w
        # preds_index=np.where(grid_pred[0]>0.1)
        # points_score=grid_pred[0][preds_index]
        # preds_index_h=(preds_index[2]*size_ratio[0]).astype(np.int)
        # preds_index_w=(preds_index[3]*size_ratio[1]).astype(np.int)
        # results=np.column_stack((preds_index_w,preds_index_h,points_score))


        #--------------------debug----------------------
        #plt.subplot(221)
        # plt.imshow(grid_pred[0][0][0])
        # #plt.subplot(222)
        # #plt.imshow(meta["raw_img"])
        # plt.savefig("demo.png")
        # plt.show()
        return [grid_pred,decode_res[1]]
    
    def get_single_pred(self,cls_scores,lines_pred,org_img_hw):
        device=cls_scores[0].device
        cls_scores=torch.sigmoid(cls_scores)

        preds_h,preds_w=cls_scores.shape[-2:]
        #preds_h=torch.Tensor(preds_h)
        #preds_w=torch.Tensor(preds_w)
        #size_ratio_hw=(org_img_hw[0]/preds_h).to(device),(org_img_hw[1]/preds_w).to(device)
        size_ratio_hw=torch.Tensor(np.array(org_img_hw[0]/preds_h)).to(device),torch.Tensor(np.array(org_img_hw[1]/preds_w)).to(device)
        b_index,c_index,h_index,w_index=torch.where(cls_scores>0.1)
        w_offset=lines_pred[b_index,0,h_index,w_index]
        h_offset=lines_pred[b_index,1,h_index,w_index]
        points_score=cls_scores[b_index,c_index,h_index,w_index].cpu().numpy()
        pred_w=((w_index+w_offset)*size_ratio_hw[1]).cpu().numpy().astype(np.int)
        pred_h=((h_index+h_offset)*size_ratio_hw[0]).cpu().numpy().astype(np.int)
        
        grid_pred=cls_scores.cpu().numpy()
        results=np.column_stack((pred_w,pred_h,points_score))
        return grid_pred,results

    def show_result(self, img, dets, class_names=None, score_thres=0.5, show=True, save_path=None):
        dets = dets[1]
        for points in dets:
            if points[2] > score_thres:
                    cv2.circle(img, tuple(points[:2].astype(np.int)), 1, (0, 0, 255), 4)
        return img
