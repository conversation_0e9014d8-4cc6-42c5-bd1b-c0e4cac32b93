"""
Create by Chengqi.Lv
2020/11/18
"""
import warnings
import torch
import cv2
import numpy as np
import torch.nn as nn
import torch.nn.functional as F
from econn.utils.visualize import add_coco_bbox
from .anchor.base_anchor_head import AnchorHead
from .anchor.anchor_generator import YOLOAnchorGenerator
from .assigner.grid_assigner import GridAssigner
from .sampler.pseudo_sampler import PseudoSampler
from econn.utils.bbox.yolo_bbox_coder import Y<PERSON><PERSON>BBoxCoder
from econn.utils.bbox_nms import multiclass_nms
from ..loss.cross_entropy_loss import CrossEntropyLoss
from ..loss.mse_loss import MSELoss
from .anchor.anchor_target import multi_apply, images_to_levels
from .fcos_head import fcos_post_process


# def show_pos_anchor(img_meta, gt_anchors, gt_bboxes, is_show=True):
#     # 显示正样本
#     assert 'img' in img_meta, print('Collect类中的meta_keys需要新增‘img’，用于可视化调试')
#     img = img_meta['img'].data.numpy()
#     mean = img_meta['img_norm_cfg']['mean']
#     std = img_meta['img_norm_cfg']['std']
#     # 默认输入是rgb数据，需要切换为bgr显示
#     img = np.transpose(img.copy(), (1, 2, 0))[..., ::-1]
#     img = img * std.reshape([1, 1, 3])[..., ::-1] + mean.reshape([1, 1, 3])[..., ::-1]
#     img = img.astype(np.uint8)
#     img = cv_core.show_bbox(img, gt_anchors.cpu().numpy(), is_show=False)
#     return cv_core.show_bbox(img, gt_bboxes.cpu().numpy(), color=(255, 255, 255), is_show=is_show)


class BaseYOLOHead(AnchorHead):
    """YOLOV3Head Paper link: https://arxiv.org/abs/1804.02767.

    Args:
        num_classes (int): The number of object classes (w/o background)
        in_channels (List[int]): Number of input channels per scale.
        out_channels (List[int]): The number of output channels per scale
            before the final 1x1 layer. Default: (1024, 512, 256).
        anchor_generator (dict): Config dict for anchor generator
        bbox_coder (dict): Config of bounding box coder.
        featmap_strides (List[int]): The stride of each scale.
            Should be in descending order. Default: (32, 16, 8).
        one_hot_smoother (float): Set a non-zero value to enable label-smooth
            Default: 0.
        conv_cfg (dict): Config dict for convolution layer. Default: None.
        norm_cfg (dict): Dictionary to construct and config norm layer.
            Default: dict(type='BN', requires_grad=True)
        act_cfg (dict): Config dict for activation layer.
            Default: dict(type='LeakyReLU', negative_slope=0.1).
        loss_cls (dict): Config of classification loss.
        loss_conf (dict): Config of confidence loss.
        loss_xy (dict): Config of xy coordinate loss.
        loss_wh (dict): Config of wh coordinate loss.
        train_cfg (dict): Training config of YOLOV3 head. Default: None.
        test_cfg (dict): Testing config of YOLOV3 head. Default: None.
    """

    def __init__(self,
                 num_classes,
                 in_channels=(512, 256),
                 out_channels=(256, 128),
                 anchor_generator=dict(
                     base_sizes=[[(81, 82), (135, 169), (344, 319)],
                                 [(23, 27), (37, 58), (81, 82)]],
                     strides=[32, 16]),
                 featmap_strides=[32, 16],
                 one_hot_smoother=0.,
                 conv_cfg=None,
                 norm_cfg=dict(type='BN', requires_grad=True),
                 act_cfg=dict(type='LeakyReLU', negative_slope=0.1),
                 loss_cls=dict(
                     type='CrossEntropyLoss',
                     use_sigmoid=True,
                     loss_weight=1.0),
                 loss_conf=dict(
                     type='CrossEntropyLoss',
                     use_sigmoid=True,
                     loss_weight=1.0),
                 loss_xy=dict(
                     type='CrossEntropyLoss',
                     use_sigmoid=True,
                     loss_weight=1.0),
                 loss_wh=dict(type='MSELoss', loss_weight=1.0),
                 train_cfg=dict(assigner=dict(
                     type='GridAssigner', pos_iou_thr=0.5, neg_iou_thr=0.5, min_pos_iou=0),
                     debug=False),
                 test_cfg=dict(nms_pre=1000,
                               min_bbox_size=0,
                               score_thr=0.05,
                               conf_thr=0.005,
                               nms=dict(type='nms', iou_threshold=0.45),
                               max_per_img=100)
                 ):
        super(BaseYOLOHead, self).__init__(num_classes=num_classes,
                                           loss=loss_wh,
                                           use_sigmoid=True,
                                           input_channel=in_channels, )
        # Check params
        assert (len(in_channels) == len(featmap_strides))

        self.num_classes = num_classes
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.featmap_strides = featmap_strides
        self.train_cfg = train_cfg
        self.test_cfg = test_cfg
        self.debug = False
        if self.train_cfg:
            self.assigner = GridAssigner(pos_iou_thr=0.5, neg_iou_thr=0.5, min_pos_iou=0)
            # yolo系列不需随机采样等操作
            self.sampler = PseudoSampler()
            # self.debug = self.train_cfg.debug

        self.one_hot_smoother = one_hot_smoother

        self.conv_cfg = conv_cfg
        self.norm_cfg = norm_cfg
        self.act_cfg = act_cfg

        self.bbox_coder = YOLOBBoxCoder(scale_x_y=1.05)
        self.anchor_generator = YOLOAnchorGenerator(**anchor_generator)

        self.loss_cls = CrossEntropyLoss(True, loss_weight=loss_cls['loss_weight'])
        self.loss_conf = CrossEntropyLoss(True, loss_weight=loss_conf['loss_weight'])
        self.loss_xy = CrossEntropyLoss(True, loss_weight=loss_xy['loss_weight'])
        self.loss_wh = MSELoss(loss_weight=loss_wh['loss_weight'])
        # usually the numbers of anchors for each level are the same
        # except SSD detectors
        self.num_anchors = self.anchor_generator.num_base_anchors[0]
        assert len(
            self.anchor_generator.num_base_anchors) == len(featmap_strides)
        # self._init_layers()

    @property
    def num_levels(self):
        return len(self.featmap_strides)

    @property
    def num_attrib(self):
        """int: number of attributes in pred_map, bboxes (4) +
        objectness (1) + num_classes"""

        return 5 + self.num_classes

    def _init_layers(self):
        pass

    def init_weights(self):
        pass

    def forward(self, feats):
        """Forward features from the upstream network.

        Args:
            feats (tuple[Tensor]): Features from the upstream network, each is
                a 4D-tensor.

        Returns:
            tuple[Tensor]: A tuple of multi-level predication map, each is a
                4D-tensor of shape (batch_size, 5+num_classes, height, width).
        """

        pass

    def decode(self, preds, meta, resize_keep_ratio):
        result_list = self.get_bboxes(preds, meta, resize_keep_ratio=resize_keep_ratio)
        return result_list

    def show_result(self, img, dets, class_names, score_thres=0.3, show=True, save_path=None):
        dets = dets[0]
        for label in dets:
            for bbox in dets[label]:
                if bbox[4] > score_thres:
                    add_coco_bbox(img, bbox[:4], label - 1, bbox[4], class_names)
        return img

    def get_bboxes(self, pred_maps, img_metas, cfg=None, rescale=False, with_nms=True, resize_keep_ratio=True):
        """Transform network output for a batch into bbox predictions.

        Args:
            pred_maps (list[Tensor]): Raw predictions for a batch of images.
            img_metas (list[dict]): Meta information of each image, e.g.,
                image size, scaling factor, etc.
            cfg (mmcv.Config | None): Test / postprocessing configuration,
                if None, test_cfg would be used. Default: None.
            rescale (bool): If True, return boxes in original image space.
                Default: False.
            with_nms (bool): If True, do nms before return boxes.
                Default: True.

        Returns:
            list[tuple[Tensor, Tensor]]: Each item in result_list is 2-tuple.
                The first item is an (n, 5) tensor, where the first 4 columns
                are bounding box positions (tl_x, tl_y, br_x, br_y) and the
                5-th column is a score between 0 and 1. The second item is a
                (n,) tensor where each item is the predicted class label of the
                corresponding box.
        """
        num_levels = len(pred_maps)
        num_classes = self.num_classes
        img_height = img_metas['img_info']['height'].cpu().numpy() \
            if isinstance(img_metas['img_info']['height'], torch.Tensor) else img_metas['img_info']['height']
        img_width = img_metas['img_info']['width'].cpu().numpy() \
            if isinstance(img_metas['img_info']['width'], torch.Tensor) else img_metas['img_info']['width']
        img_shapes = np.array([img_width, img_height], dtype=np.float32).reshape(-1, 2)
        input_height, input_width = img_metas['img'].shape[2:]
        input_shape = [input_height, input_width]
        center = np.array([img_width / 2., img_height / 2.], dtype=np.float32).reshape(-1, 2)

        result_list = []
        for img_id in range(pred_maps[0].shape[0]):
            pred_maps_list = [
                pred_maps[i][img_id].detach() for i in range(num_levels)
            ]
            img_shape = img_shapes[img_id]
            scale_factor = 1
            pad_param = None
            det_bboxes, det_labels = self._get_bboxes_single(pred_maps_list, scale_factor,
                                                             cfg, rescale, pad_param, with_nms)
            c = center[img_id]
            dets = fcos_post_process(det_bboxes,
                                     det_labels,
                                     c,
                                     img_shape,
                                     input_height,
                                     input_width,
                                     num_classes,
                                     resize_keep_ratio)
            result_list.append(dets)
        return result_list

    def _get_bboxes_single(self,
                           pred_maps_list,
                           scale_factor,
                           cfg,
                           rescale=False,
                           pad_param=None,
                           with_nms=True):
        """Transform outputs for a single batch item into bbox predictions.

        Args:
            pred_maps_list (list[Tensor]): Prediction maps for different scales
                of each single image in the batch.
            scale_factor (ndarray): Scale factor of the image arrange as
                (w_scale, h_scale, w_scale, h_scale).
            cfg (mmcv.Config): Test / postprocessing configuration,
                if None, test_cfg would be used.
            rescale (bool): If True, return boxes in original image space.

        Returns:
            Tensor: Labeled boxes in shape (n, 5), where the first 4 columns
                are bounding box positions (tl_x, tl_y, br_x, br_y) and the
                5-th column is a score between 0 and 1.
        """
        cfg = self.test_cfg if cfg is None else cfg
        assert len(pred_maps_list) == self.num_levels
        multi_lvl_bboxes = []
        multi_lvl_cls_scores = []
        multi_lvl_conf_scores = []
        num_levels = len(pred_maps_list)
        featmap_sizes = [
            pred_maps_list[i].shape[-2:] for i in range(num_levels)
        ]
        multi_lvl_anchors = self.anchor_generator.grid_anchors(
            featmap_sizes, pred_maps_list[0][0].device)
        for i in range(self.num_levels):
            # get some key info for current scale
            pred_map = pred_maps_list[i]
            stride = self.featmap_strides[i]

            # (h, w, num_anchors*num_attrib) -> (h*w*num_anchors, num_attrib)
            pred_map = pred_map.permute(1, 2, 0).reshape(-1, self.num_attrib)

            pred_map[..., :2] = torch.sigmoid(pred_map[..., :2])
            bbox_pred = self.bbox_coder.decode(multi_lvl_anchors[i],
                                               pred_map[..., :4], stride)
            # conf and cls
            conf_pred = torch.sigmoid(pred_map[..., 4]).view(-1)
            cls_pred = torch.sigmoid(pred_map[..., 5:]).view(
                -1, self.num_classes)  # Cls pred one-hot.

            # Filtering out all predictions with conf < conf_thr
            conf_thr = cfg.get('conf_thr', -1)
            conf_inds = conf_pred.ge(conf_thr).nonzero().flatten()
            bbox_pred = bbox_pred[conf_inds, :]
            cls_pred = cls_pred[conf_inds, :]
            conf_pred = conf_pred[conf_inds]

            # Get top-k prediction
            nms_pre = cfg.get('nms_pre', -1)
            if 0 < nms_pre < conf_pred.size(0):
                _, topk_inds = conf_pred.topk(nms_pre)
                bbox_pred = bbox_pred[topk_inds, :]
                cls_pred = cls_pred[topk_inds, :]
                conf_pred = conf_pred[topk_inds]

            # Save the result of current scale
            multi_lvl_bboxes.append(bbox_pred)
            multi_lvl_cls_scores.append(cls_pred)
            multi_lvl_conf_scores.append(conf_pred)

        # Merge the results of different scales together
        multi_lvl_bboxes = torch.cat(multi_lvl_bboxes)
        multi_lvl_cls_scores = torch.cat(multi_lvl_cls_scores)
        multi_lvl_conf_scores = torch.cat(multi_lvl_conf_scores)

        if with_nms and multi_lvl_conf_scores.size(0) == 0:
            return torch.zeros((0, 5)), torch.zeros((0,))

        if rescale:
            # 先去掉pad，如果有的话
            if pad_param is not None:
                multi_lvl_bboxes -= multi_lvl_bboxes.new_tensor(
                    [pad_param[2], pad_param[0], pad_param[2], pad_param[0]])
            multi_lvl_bboxes /= multi_lvl_bboxes.new_tensor(scale_factor)

        padding = multi_lvl_cls_scores.new_zeros(multi_lvl_cls_scores.shape[0], 1)
        # in econn bg class is 0
        multi_lvl_cls_scores = torch.cat([padding, multi_lvl_cls_scores], dim=1)

        det_bboxes, det_labels = multiclass_nms(
            multi_lvl_bboxes,
            multi_lvl_cls_scores,
            score_thr=0.05,
            nms_cfg=dict(type='nms', iou_thr=0.6),
            max_num=100,
            score_factors=multi_lvl_conf_scores)
        return det_bboxes, det_labels

    def loss(self,
             preds,
             gt_meta):
        """Compute loss of the head.

        Returns:
            dict[str, Tensor]: A dictionary of loss components.
        """
        num_imgs = preds[0].shape[0]
        device = preds[0][0].device

        gt_bboxes = gt_meta['gt_bboxes']
        gt_labels = gt_meta['gt_labels']
        input_height, input_width = gt_meta['img'].shape[2:]
        img_shapes = [[input_height, input_width] for i in range(num_imgs)]

        featmap_sizes = [
            preds[i].shape[-2:] for i in range(self.num_levels)
        ]
        multi_level_anchors = self.anchor_generator.grid_anchors(
            featmap_sizes, device)
        anchor_list = [multi_level_anchors for _ in range(num_imgs)]

        responsible_flag_list = []
        for img_id in range(num_imgs):
            # 计算当前图片中的bbox中心在特征图上面的位置,以flag形式表示
            responsible_flag_list.append(
                self.anchor_generator.responsible_flags(
                    featmap_sizes, torch.from_numpy(gt_bboxes[img_id]).to(device), device))

        target_maps_list, neg_maps_list = self.get_targets(
            anchor_list, responsible_flag_list, gt_bboxes, gt_labels
            # , img_metas
        )

        losses_cls, losses_conf, losses_xy, losses_wh = multi_apply(
            self.loss_single, preds, target_maps_list, neg_maps_list)
        loss_cls = sum(losses_cls)
        loss_conf = sum(losses_conf)
        loss_xy = sum(losses_xy)
        loss_wh = sum(losses_wh)
        loss = loss_cls + loss_conf + loss_xy + loss_wh
        loss_states = dict(
            loss=loss,
            loss_cls=loss_cls,
            loss_conf=loss_conf,
            loss_xy=loss_xy,
            loss_wh=loss_wh)

        return loss, loss_states

    def loss_single(self, pred_map, target_map, neg_map):
        """Compute loss of a single image from a batch.

        Args:
            pred_map (Tensor): Raw predictions for a single level.
            target_map (Tensor): The Ground-Truth target for a single level.
            neg_map (Tensor): The negative masks for a single level.

        Returns:
            tuple:
                loss_cls (Tensor): Classification loss.
                loss_conf (Tensor): Confidence loss.
                loss_xy (Tensor): Regression loss of x, y coordinate.
                loss_wh (Tensor): Regression loss of w, h coordinate.
        """

        num_imgs = len(pred_map)
        pred_map = pred_map.permute(0, 2, 3,
                                    1).reshape(num_imgs, -1, self.num_attrib)
        neg_mask = neg_map.float()  # 忽略位置图
        pos_mask = target_map[..., 4]  # confidence，也是衡量该位置有没有obj的标志
        pos_and_neg_mask = neg_mask + pos_mask
        pos_mask = pos_mask.unsqueeze(dim=-1)
        if torch.max(pos_and_neg_mask) > 1.:  # 说明有些正样本区域是要忽略的
            warnings.warn('There is overlap between pos and neg sample.')
            pos_and_neg_mask = pos_and_neg_mask.clamp(min=0., max=1.)

        pred_xy = pred_map[..., :2]
        pred_wh = pred_map[..., 2:4]
        pred_conf = pred_map[..., 4]
        pred_label = pred_map[..., 5:]

        target_xy = target_map[..., :2]
        target_wh = target_map[..., 2:4]
        target_conf = target_map[..., 4]
        target_label = target_map[..., 5:]

        loss_cls = self.loss_cls(pred_label, target_label, weight=pos_mask)
        loss_conf = self.loss_conf(
            pred_conf, target_conf, weight=pos_and_neg_mask)
        loss_xy = self.loss_xy(pred_xy, target_xy, weight=pos_mask)
        loss_wh = self.loss_wh(pred_wh, target_wh, weight=pos_mask)

        return loss_cls, loss_conf, loss_xy, loss_wh

    def get_targets(self,
                    anchor_list,
                    responsible_flag_list,
                    gt_bboxes_list,
                    gt_labels_list,
                    # img_metas
                    ):
        """Compute target maps for anchors in multiple images.

        Args:
            anchor_list (list[list[Tensor]]): Multi level anchors of each
                image. The outer list indicates images, and the inner list
                corresponds to feature levels of the image. Each element of
                the inner list is a tensor of shape (num_total_anchors, 4).
            responsible_flag_list (list[list[Tensor]]): Multi level responsible
                flags of each image. Each element is a tensor of shape
                (num_total_anchors, )
            gt_bboxes_list (list[Tensor]): Ground truth bboxes of each image.
            gt_labels_list (list[Tensor]): Ground truth labels of each box.

        Returns:
            tuple: Usually returns a tuple containing learning targets.
                - target_map_list (list[Tensor]): Target map of each level.
                - neg_map_list (list[Tensor]): Negative map of each level.
        """
        num_imgs = len(anchor_list)

        # anchor number of multi levels 每一层anchor的个数，例如输出是10x10，那么10x10x3=300
        num_level_anchors = [anchors.size(0) for anchors in anchor_list[0]]

        results = multi_apply(self._get_targets_single, anchor_list,
                              responsible_flag_list, gt_bboxes_list,
                              gt_labels_list,
                              # img_metas,
                              # num_level_anchors=num_level_anchors
                              )

        all_target_maps, all_neg_maps = results
        assert num_imgs == len(all_target_maps) == len(all_neg_maps)
        # all_target_maps是图片级别list结构，假设有8张图片则len()=8，需要转化为fpn级别的输出，才方便计算Loss,假设有三层输出，则len()=3
        target_maps_list = images_to_levels(all_target_maps, num_level_anchors)
        neg_maps_list = images_to_levels(all_neg_maps, num_level_anchors)

        return target_maps_list, neg_maps_list

    def _get_targets_single(self, anchors, responsible_flags, gt_bboxes,
                            gt_labels,
                            # img_meta,
                            # num_level_anchors=None
                            ):
        """Generate matching bounding box prior and converted GT.

        Args:
            anchors (list[Tensor]): Multi-level anchors of the image.
            responsible_flags (list[Tensor]): Multi-level responsible flags of
                anchors
            gt_bboxes (Tensor): Ground truth bboxes of single image.
            gt_labels (Tensor): Ground truth labels of single image.

        Returns:
            tuple:
                target_map (Tensor): Predication target map of each
                    scale level, shape (num_total_anchors,
                    5+num_classes)
                neg_map (Tensor): Negative map of each scale level,
                    shape (num_total_anchors,)
        """

        anchor_strides = []
        device = anchors[0].device
        gt_bboxes = torch.from_numpy(gt_bboxes).to(device)
        gt_labels = torch.from_numpy(gt_labels).to(device)
        for i in range(len(anchors)):
            anchor_strides.append(
                torch.tensor(self.featmap_strides[i],
                             device=gt_bboxes.device).repeat(len(anchors[i])))
        concat_anchors = torch.cat(anchors)  # 三个输出层的anchor合并
        concat_responsible_flags = torch.cat(responsible_flags)

        anchor_strides = torch.cat(anchor_strides)
        assert len(anchor_strides) == len(concat_anchors) == \
               len(concat_responsible_flags)
        assign_result = self.assigner.assign(concat_anchors,
                                             concat_responsible_flags,
                                             gt_bboxes)

        # if self.debug:
        #     # 统计下正样本个数
        #     print('----anchor分配正负样本后，正样本anchor可视化，白色bbox是gt----')
        #     gt_inds = assign_result.gt_inds  # 0 1 -1
        #     index = gt_inds > 0
        #     gt_inds = gt_inds[index]
        #     gt_anchors = concat_anchors[index]
        #     print('单张图片中正样本anchor个数', len(gt_inds))
        #     if num_level_anchors is None:  # 不分层显示
        #         show_pos_anchor(img_meta, gt_anchors, gt_bboxes)
        #     else:
        #         imgs = []
        #         count = 0
        #         for num_level in num_level_anchors:
        #             gt_inds = assign_result.gt_inds[count:count + num_level]
        #             anchor = concat_anchors[count:count + num_level]
        #             count += num_level
        #             index = gt_inds > 0
        #             gt_anchor = anchor[index]
        #             img = show_pos_anchor(img_meta, gt_anchor, gt_bboxes, is_show=False)
        #             imgs.append(img)
        #         print('从小特征图到大特征图顺序显示')
        #         cv_core.show_img(imgs)

        # 相当于没有，只是为了不报错
        sampling_result = self.sampler.sample(assign_result, concat_anchors,
                                              gt_bboxes)

        # 转化为最终计算Loss所需要的格式
        target_map = concat_anchors.new_zeros(
            concat_anchors.size(0), self.num_attrib)  # 5+class_count

        # 正样本位置anchor bbox；对应的gt bbox；strides
        # target_map前4个是xywh在特征图尺度上面的转化后的label
        target_map[sampling_result.pos_inds, :4] = self.bbox_coder.encode(
            sampling_result.pos_bboxes, sampling_result.pos_gt_bboxes,
            anchor_strides[sampling_result.pos_inds])

        target_map[sampling_result.pos_inds, 4] = 1  # confidence label

        gt_labels_one_hot = F.one_hot(
            gt_labels, num_classes=self.num_classes).float()
        if self.one_hot_smoother != 0:  # label smooth
            gt_labels_one_hot = gt_labels_one_hot * (
                    1 - self.one_hot_smoother
            ) + self.one_hot_smoother / self.num_classes
        target_map[sampling_result.pos_inds, 5:] = gt_labels_one_hot[
            sampling_result.pos_assigned_gt_inds]  # class one hot label

        neg_map = concat_anchors.new_zeros(
            concat_anchors.size(0), dtype=torch.uint8)
        neg_map[sampling_result.neg_inds] = 1

        return target_map, neg_map
