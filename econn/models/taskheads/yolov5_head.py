"""
Created by <PERSON><PERSON><PERSON><PERSON>
"""
from functools import partial
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.distributed as dist
import numpy as np
import math
from econn.utils.visualize import add_coco_bbox
from econn.utils.bbox_nms import multiclass_nms
import cv2
from .anchor.autoanchor import check_anchors


class YOLOv5Head(nn.Module):
    def __init__(self,
                 num_classes,
                 anchors,
                 in_channels=[128, 256, 512],
                 strides=[8, 16, 32],
                 anchor_t=4,
                 ann_path='',
                 input_size_w=640,
                 ):
        super(YOLOv5Head, self).__init__()
        self.nc = num_classes
        self.no = num_classes + 5
        self.nl = len(anchors)
        self.na = len(anchors[0]) // 2
        self.strides = torch.tensor(strides)
        self.anchor_t = anchor_t
        self.grid = [torch.zeros(1)] * self.nl
        a = torch.tensor(anchors).float().view(self.nl, -1 ,2) # (3,-1,2)
        self.register_buffer('anchors',a)
        self.register_buffer('anchor_grid', a.clone().view(self.nl,1,-1,1,1,2)) # (nl,1,na,1,1,2)
        self.anchors /= self.strides.view(-1,1,1)
        self.m = nn.ModuleList(nn.Conv2d(x, self.no*self.na,1) for x in in_channels)
        self._initialize_biases()
        check_anchors(ann_path, model=self, thr=anchor_t, imgsz=input_size_w)
        self.loss_func = YOLOv5Loss(self)

    def forward(self, feats):
        feats = list(feats)
        for i in range(self.nl):
            feats[i] = self.m[i](feats[i])
            bs,_,ny,nx = feats[i].shape
            feats[i] = feats[i].view(bs, self.na, self.no, ny, nx).permute(0,1,3,4,2).contiguous()
        return feats # [(bs,na,ny,nx,no),...]

    def loss(self,preds,gt_meta):
        # [(bs,na,ny,nx,no),...]
        gt_bboxes = gt_meta['gt_bboxes'] # abs x1y1x2y2, from 0 start
        gt_labels = gt_meta['gt_labels']
        shape = gt_meta['img'].shape[-2:] # hw
        loss, loss_states = self.loss_func(preds,(gt_bboxes,gt_labels),shape)
        return loss, loss_states

    def decode(self,preds,meta,resize_keep_ratio=True):
        cls_scores, bbox_preds = [], []
        bs = preds[0].shape[0]
        for i in range(self.nl):
            ny, nx = preds[i].shape[2:4]
            if self.grid[i].shape != preds[i].shape[2:4]:
                self.grid[i] = self._make_grid(nx,ny).to(preds[i].device)
            y = preds[i].sigmoid()
            y[..., 0:2] = (y[...,0:2] * 2. - 0.5 + self.grid[i]) * self.strides[i] # abs xy
            y[..., 2:4] = (y[..., 2:4] * 2) ** 2 * self.anchor_grid[i] # abs wh
            #inds = y[...,4] > 0.001
            cls_score = (y[...,4:5] * y[...,5:]).view(bs,-1,self.no-5)
            bbox_pred = y[...,:4].view(bs,-1,4)
            cls_scores.append(cls_score) # (bs,na*ny*nx,nc)
            bbox_preds.append(bbox_pred) # (bs,na*ny*nx,4)
        result_list = self.get_bboxes(cls_scores, bbox_preds, meta, resize_keep_ratio)
        return result_list

    def show_result(self, img, dets, class_names, score_thres=0.3, show=True, save_path=None):
        dets = dets[0]
        for label in dets:
            for bbox in dets[label]:
                if bbox[4] > score_thres:
                    add_coco_bbox(img, bbox[:4], label - 1, bbox[4], class_names)
        return img


    def get_bboxes(self,
                  cls_scores,
                  bbox_preds,
                  img_metas,
                  resize_keep_ratio,
                  rescale=False):
        assert len(cls_scores) == len(bbox_preds) # cls_scores[0].shape:(1,n_p,nc)
        num_levels = len(cls_scores)
        num_classes = cls_scores[0].shape[2]
        device = cls_scores[0].device
        input_height, input_width = img_metas['img'].shape[2:]
        input_shape = [input_height, input_width]
        img_height = img_metas['img_info']['height'].cpu().numpy() \
            if isinstance(img_metas['img_info']['height'], torch.Tensor) else img_metas['img_info']['height']
        img_width = img_metas['img_info']['width'].cpu().numpy() \
            if isinstance(img_metas['img_info']['width'], torch.Tensor) else img_metas['img_info']['width']
        img_shapes = np.array([img_width, img_height], dtype=np.float32).reshape(-1, 2)


        result_list = []
        for img_id in range(cls_scores[0].shape[0]):
            cls_score_list = [
                cls_scores[i][img_id].detach() for i in range(num_levels)
            ] # [(n_p,nc),...]
            bbox_pred_list = [
                bbox_preds[i][img_id].detach() for i in range(num_levels)
            ]
            img_shape = img_shapes[img_id]
            scale_factor = 1
            det_bboxes, det_labels = self.get_bboxes_single(cls_score_list, bbox_pred_list,
                                                           input_shape, scale_factor, rescale) # x1y1x2y2 in input

            dets = post_process(det_bboxes,det_labels,self.nc,img_shape,input_shape,resize_keep_ratio)
            result_list.append(dets)
        return result_list

    def get_bboxes_single(self,
                          cls_scores,
                          bbox_preds,
                          input_shape,
                          scale_factor,
                          rescale=False):
        assert len(cls_scores) == len(bbox_preds)
        mlvl_bboxes = []
        mlvl_scores = []
        for scores, bbox_pred in zip(cls_scores, bbox_preds):
            # scores: (num of points, n_class)
            nms_pre = 1000
            if nms_pre > 0 and scores.shape[0] > nms_pre:
                max_scores, _ = scores.max(dim=1)
                _, topk_inds = max_scores.topk(nms_pre)
                bboxes = bbox_pred[topk_inds,:]
                scores = scores[topk_inds,:]
            else:bboxes=bbox_pred[:,:]
            # xywh to x1y1x2y2
            bboxes = xywh2xyxy(bboxes,shape=input_shape)
            mlvl_scores.append(scores)
            mlvl_bboxes.append(bboxes)
        mlvl_bboxes = torch.cat(mlvl_bboxes) # (num of points in 3 level,4)
        if rescale:
            mlvl_bboxes /= mlvl_bboxes.new_tensor(scale_factor)
        mlvl_scores = torch.cat(mlvl_scores)
        padding = mlvl_scores.new_zeros(mlvl_scores.shape[0], 1)
        mlvl_scores = torch.cat([padding, mlvl_scores], dim=1)

        det_bboxes, det_labels = multiclass_nms(
            mlvl_bboxes,
            mlvl_scores,
            score_thr=0.001, # 0.05
            nms_cfg=dict(type='nms', iou_thr=0.6),
            max_num=100 # 100
        )
        return det_bboxes, det_labels

    def _make_grid(self,nx=20,ny=20):
        yv, xv = torch.meshgrid([torch.arange(ny), torch.arange(nx)])
        return torch.stack((xv,yv), 2).view((1,1,ny,nx,2)).float()

    def _initialize_biases(self,cf=None):
        for mi,s in zip(self.m, self.strides):
            b = mi.bias.view(self.na, -1)
            b.data[:, 4] += math.log(8 / (640 / np.array(s) ** 2) ) # obj (8 objects per 640 image)
            b.data[:, 5:] += math.log(0.6 / (self.nc - 0.99)) if cf is None else torch.log(cf / cf.sum())  # cls
            mi.bias = torch.nn.Parameter(b.view(-1), requires_grad=True)




class YOLOv5Loss:
    def __init__(self,yolov5head):
        super(YOLOv5Loss, self).__init__()
        #device = yolov5head.parameters().device
        BCEcls = nn.BCEWithLogitsLoss(pos_weight=torch.Tensor([1.0]).cuda())
        BCEobj = nn.BCEWithLogitsLoss(pos_weight=torch.Tensor([1.0]).cuda())

        self.balance = {3: [4.0, 1.0, 0.4]}.get(yolov5head.nl, [4.0, 1.0, 0.25, 0.06, .02])
        self.BCEcls, self.BCEobj, self.gr = BCEcls, BCEobj, 1.0
        for k in 'na','nc', 'nl', 'anchors', 'anchor_t':
            setattr(self, k, getattr(yolov5head, k))

    def __call__(self, p, ecotargets, shape):
        device = p[0].device
        lcls, lbox, lobj = torch.zeros(1, device=device), torch.zeros(1, device=device), torch.zeros(1, device=device)
        tcls, tbox, indices, anchors = self.build_targets(p, ecotargets, shape)

        # losses
        for i, pi in enumerate(p):
            b, a, gj, gi = indices[i]
            tobj = torch.zeros_like(pi[..., 0], device=device)

            n = b.shape[0] # number of targets
            if n:
                ps = pi[b, a, gj, gi]
                pxy = ps[:,:2].sigmoid() * 2. - 0.5
                pwh = (ps[:,2:4].sigmoid() * 2) ** 2 * anchors[i]
                pbox = torch.cat((pxy, pwh), 1)
                iou = bbox_iou(pbox.T, tbox[i], x1y1x2y2=False, CIoU=True)
                lbox += (1.0 - iou).mean()

                tobj[b,a,gj,gi] = (1.0 - self.gr) + self.gr * iou.detach().clamp(0).type(tobj.dtype)

                if self.nc > 1:
                    t = torch.full_like(ps[:, 5:], 0., device=device) ### label smooth
                    t[range(n),tcls[i]] = 1.0 ### label smooth
                    lcls += self.BCEcls(ps[:,5:], t)

            obji = self.BCEobj(pi[..., 4], tobj)
            lobj += obji * self.balance[i]
        lbox *= 0.05 # hyp['box'] *= 3. / nl
        lcls *= 0.5*self.nc/80. # hyp['cls'] *= nc / 80. * 3. / nl
        lobj *= 1.0 #*(shape[1]/640)**2
        bs = tobj.shape[0] # batch size
        loss = lbox + lobj + lcls #(lbox + lobj + lcls) * bs
        loss_states = {'box':lbox, 'obj':lobj,'cls':lcls}
        return loss*bs, loss_states

    def build_targets(self, p, ecotargets, shape):
        H,W = shape
        targets = []
        for i,(bboxes,labels) in enumerate(zip(*ecotargets)):
            bboxes = xyxy2xywh(bboxes,shape) # (n,4), start from 0
            l_b = np.hstack([labels.reshape(-1,1),bboxes])
            targets.append(np.insert(l_b,0,i,axis=1)) # [img_idx, label, norm xywh]
        targets = torch.Tensor(np.concatenate(targets,0)).to(p[0].device) # (nt,6)
        na, nt = self.na, targets.shape[0]
        tcls, tbox, indices, anch = [], [], [], []
        gain = torch.ones(7, device=targets.device)
        ai = torch.arange(na, device=targets.device).float().view(na,1).repeat(1,nt) #(na,nt)
        targets = torch.cat((targets.repeat(na,1,1), ai[:,:,None]), 2) # (img_idx,label,xywh,ai)

        g = 0.5
        off = torch.tensor([[0, 0],
                            [1, 0], [0, 1], [-1, 0], [0, -1],  # j,k,l,m
                            ], device=targets.device).float() * g  # offsets
        for i in range(self.nl):
            anchors = self.anchors[i].to(targets.device)
            gain[2:6] = torch.tensor(p[i].shape)[[3,2,3,2]] # xyxy gain

            t = targets * gain # (na,nt,7)
            if nt:
                r = t[:,:,4:6] / anchors[:,None] # wh ratio # (na,nt,2)
                j = torch.max(r, 1./r).max(2)[0] < self.anchor_t # (na,nt)
                t = t[j] # (na,nt,7) to (n_pos,7), img_idx,label,xywh,ai

                # Offsets
                gxy = t[:, 2:4]
                gxi = gain[[2,3]] - gxy
                j,k = ((gxy % 1. < g) & (gxy > 1.)).T # right top offset bool
                l,m = ((gxi % 1. < g) & (gxi > 1.)).T # left bottom offset bool
                j = torch.stack((torch.ones_like(j), j, k, l, m))
                t = t.repeat((5, 1, 1))[j]
                offsets = (torch.zeros_like(gxy)[None] + off[:,None])[j]
            else:
                t = targets[0]
                offsets = 0

            # Define
            b,c = t[:,:2].long().T
            gxy = t[:,2:4]
            gwh = t[:,4:6]
            gij = (gxy - offsets).long()
            gi, gj = gij.T

            # Append
            a = t[:, 6].long()
            indices.append((b,a,gj.clamp_(0,gain[3]-1), gi.clamp_(0, gain[2]-1)))
            tbox.append(torch.cat((gxy-gij, gwh), 1))
            anch.append(anchors[a])
            tcls.append(c)

        return tcls, tbox, indices, anch

def bbox_iou(box1, box2, x1y1x2y2=True, GIoU=False, DIoU=False, CIoU=False, eps=1e-7):
    # Returns the IoU of box1 to box2. box1 is 4, box2 is nx4
    box2 = box2.T

    # Get the coordinates of bounding boxes
    if x1y1x2y2:  # x1, y1, x2, y2 = box1
        b1_x1, b1_y1, b1_x2, b1_y2 = box1[0], box1[1], box1[2], box1[3]
        b2_x1, b2_y1, b2_x2, b2_y2 = box2[0], box2[1], box2[2], box2[3]
    else:  # transform from xywh to xyxy
        b1_x1, b1_x2 = box1[0] - box1[2] / 2, box1[0] + box1[2] / 2
        b1_y1, b1_y2 = box1[1] - box1[3] / 2, box1[1] + box1[3] / 2
        b2_x1, b2_x2 = box2[0] - box2[2] / 2, box2[0] + box2[2] / 2
        b2_y1, b2_y2 = box2[1] - box2[3] / 2, box2[1] + box2[3] / 2

    # Intersection area
    inter = (torch.min(b1_x2, b2_x2) - torch.max(b1_x1, b2_x1)).clamp(0) * \
            (torch.min(b1_y2, b2_y2) - torch.max(b1_y1, b2_y1)).clamp(0)

    # Union Area
    w1, h1 = b1_x2 - b1_x1, b1_y2 - b1_y1 + eps
    w2, h2 = b2_x2 - b2_x1, b2_y2 - b2_y1 + eps
    union = w1 * h1 + w2 * h2 - inter + eps

    iou = inter / union
    if GIoU or DIoU or CIoU:
        cw = torch.max(b1_x2, b2_x2) - torch.min(b1_x1, b2_x1)  # convex (smallest enclosing box) width
        ch = torch.max(b1_y2, b2_y2) - torch.min(b1_y1, b2_y1)  # convex height
        if CIoU or DIoU:  # Distance or Complete IoU https://arxiv.org/abs/1911.08287v1
            c2 = cw ** 2 + ch ** 2 + eps  # convex diagonal squared
            rho2 = ((b2_x1 + b2_x2 - b1_x1 - b1_x2) ** 2 +
                    (b2_y1 + b2_y2 - b1_y1 - b1_y2) ** 2) / 4  # center distance squared
            if DIoU:
                return iou - rho2 / c2  # DIoU
            elif CIoU:  # https://github.com/Zzh-tju/DIoU-SSD-pytorch/blob/master/utils/box/box_utils.py#L47
                v = (4 / math.pi ** 2) * torch.pow(torch.atan(w2 / h2) - torch.atan(w1 / h1), 2)
                with torch.no_grad():
                    alpha = v / (v - iou + (1 + eps))
                return iou - (rho2 / c2 + v * alpha)  # CIoU
        else:  # GIoU https://arxiv.org/pdf/1902.09630.pdf
            c_area = cw * ch + eps  # convex area
            return iou - (c_area - union) / c_area  # GIoU
    else:
        return iou  # IoU

def xyxy2xywh(xyxy,shape=None):
    # abs x1y1x2y2(n,4) to norm xywh
    if len(xyxy) == 0:
        xywh = np.array([]).reshape(-1,4)
    else:
        x1,y1,x2,y2 = xyxy.T
        w,h = x2-x1, y2-y1
        x,y = (x1+x2)/2.0-1.0, (y1+y2)/2.0-1.0
        if shape is not None:
            H, W = shape
            w,h = w/W, h/H
            x,y = x/W, y/H
        xywh = np.stack([x,y,w,h],1)
    return xywh

def xywh2xyxy(xywh,shape=None):
    # abs xywh to x1y1x2y2
    # have negative value, how to process?
    x,y,w,h = xywh.T
    x1,y1 = x-w/2, y-h/2
    x2,y2 = x+w/2, y+h/2
    # if shape is not None:
    #    x1 = x1.clamp(min=0, max=shape[1])
    #    y1 = y1.clamp(min=0, max=shape[0])
    #    x2 = x2.clamp(min=0, max=shape[1])
    #    y2 = y2.clamp(min=0, max=shape[0])
    return torch.stack([x1,y1,x2,y2], -1)

def post_process(det_bboxes,det_labels,num_classes,img_shape,input_shape,resize_keep_ratio):
    # input x1y1x2y2 to original x1y1wh
    #input_shape: hw, img_shape:wh
    top_preds = {}
    det_bboxes = det_bboxes.cpu().numpy()
    det_bboxes = scale_coords(det_bboxes,img_shape,input_shape) # x1y1x2y2
    classes = det_labels.cpu().numpy()
    for j in range(num_classes):
        inds = (classes == j)
        top_preds[j+1] = np.concatenate([det_bboxes[inds,:4].astype(np.float32),
                                         det_bboxes[inds,4:5].astype(np.float32)], axis=1).tolist()
    return top_preds

def scale_coords(det_bboxes:'x1y1x2y2',img_shape:'wh',input_shape:'hw'):
    assert img_shape[0] >= img_shape[1], 'width less than height'
    gain = min(input_shape[0] / img_shape[1], input_shape[1] / img_shape[0]) # gain=old/new
    pad = (input_shape[1] - img_shape[0] * gain) / 2, (input_shape[0] - img_shape[1] * gain) / 2
    det_bboxes[:,[0,2]] -= pad[0]
    det_bboxes[:,[1,3]] -= pad[1]

    det_bboxes[:,:4] /= gain

    det_bboxes[:,[0,2]] = det_bboxes[:,[0,2]].clip(0,img_shape[0])
    det_bboxes[:, [1, 3]] = det_bboxes[:, [1, 3]].clip(0, img_shape[1])
    return det_bboxes


