"""
create by yang<PERSON><PERSON>ong
date:1/14/21
"""

"""Encoding Custermized NN Module"""
import torch
import torch.nn as nn

from torch.nn import functional as F
from torch.nn import Module, Sequential, Conv2d, ReLU, AdaptiveAvgPool2d, BCELoss, CrossEntropyLoss, NLLLoss

from torch.autograd import Variable

# __all__ = ['SegmentationLosses', 'OffsetLosses', 'PyramidPooling', 'JPU', 'Mean']
class OffsetLosses(Module):
    """2D Cross Entropy Loss with Auxilary Loss"""

    def __init__(self, loss_weight = 1.0, se_loss=True, se_weight=0.2, nclass=-1,
                 aux=False, aux_weight=0.4, offset=True, offset_weight=0.3, location_regression_weight=0.1,
                 weight=None, size_average=True, ignore_index=-1):
        super(OffsetLosses, self).__init__()
        self.loss_weight = loss_weight
        self.se_loss = se_loss
        self.aux = aux
        self.nclass = nclass
        self.offset = offset
        self.se_weight = se_weight
        self.aux_weight = aux_weight
        self.offset_weight = offset_weight
        self.location_regression_weight = location_regression_weight
        self.bceloss = BCELoss(weight, size_average)

        self.logsoftmax = nn.LogSoftmax(dim=1)
        self.nllloss = nn.NLLLoss(reduction='none', ignore_index=ignore_index)
        self.smoothl1 = nn.SmoothL1Loss(reduction='mean')
        self.crossentropy = nn.CrossEntropyLoss(weight, size_average=size_average, ignore_index=ignore_index)

    def forward(self, *inputs):
        if self.se_loss and self.aux:
            pred1_diffdup, se_pred, pred1_detup, grid, pred1_lt_detup, pred1_lb_detup, pred1_rt_detup, pred1_rb_detup, pred2, offsets, target = tuple(
                inputs)
            se_target = self._get_batch_label_vector(target, nclass=self.nclass).type_as(pred1_diffdup)

            pred1_diffup_logsoftmax = self.logsoftmax(pred1_diffdup)

            target_1 = F.interpolate(target.unsqueeze(dim=1).float(),
                                     size=(pred1_diffdup.size(2), pred1_diffdup.size(3)), mode='nearest')

            pred1_loss1 = self.nllloss(pred1_diffup_logsoftmax, target_1.squeeze().long()).unsqueeze(dim=1)

            with torch.no_grad():
                pred1_detup_logsoftmax = self.logsoftmax(pred1_detup)
                pred1_lt_detup_logsoftmax = self.logsoftmax(pred1_lt_detup)
                pred1_lb_detup_logsoftmax = self.logsoftmax(pred1_lb_detup)
                pred1_rt_detup_logsoftmax = self.logsoftmax(pred1_rt_detup)
                pred1_rb_detup_logsoftmax = self.logsoftmax(pred1_rb_detup)

                pred1_loss2 = self.nllloss(pred1_detup_logsoftmax, target_1.squeeze().long()).unsqueeze(dim=1)
                pred1_loss3 = self.nllloss(pred1_lt_detup_logsoftmax, target_1.squeeze().long()).unsqueeze(dim=1)
                pred1_loss4 = self.nllloss(pred1_lb_detup_logsoftmax, target_1.squeeze().long()).unsqueeze(dim=1)
                pred1_loss5 = self.nllloss(pred1_rt_detup_logsoftmax, target_1.squeeze().long()).unsqueeze(dim=1)
                pred1_loss6 = self.nllloss(pred1_rb_detup_logsoftmax, target_1.squeeze().long()).unsqueeze(dim=1)

                coords_lt = grid.floor().float() - grid.float()
                coords_rb = grid.ceil().float() - grid.float()
                coords_lb = torch.cat((coords_rb[:, 0, :, :].unsqueeze(dim=1), coords_lt[:, 1, :, :].unsqueeze(dim=1)),
                                      1)  # coords_lt[..., 0] : row | coords_lt[..., 1] : col
                coords_rt = torch.cat((coords_lt[:, 0, :, :].unsqueeze(dim=1), coords_rb[:, 1, :, :].unsqueeze(dim=1)),
                                      1)

                gt_offsets = torch.zeros(offsets.shape).to(offsets.device)
                gt_offsets = gt_offsets + offsets
                min_error = pred1_loss1

                error_map = torch.lt(pred1_loss3, min_error).float()
                gt_offsets = gt_offsets + error_map * (coords_lt - gt_offsets)
                min_error = torch.min(pred1_loss3, min_error)

                error_map = torch.lt(pred1_loss4, min_error).float()
                gt_offsets = gt_offsets + error_map * (coords_lb - gt_offsets)
                min_error = torch.min(pred1_loss4, min_error)

                error_map = torch.lt(pred1_loss5, min_error).float()
                gt_offsets = gt_offsets + error_map * (coords_rt - gt_offsets)
                min_error = torch.min(pred1_loss5, min_error)

                error_map = torch.lt(pred1_loss6, min_error).float()
                gt_offsets = gt_offsets + error_map * (coords_rb - gt_offsets)
                min_error = torch.min(pred1_loss6, min_error)

                error_map_loss1 = torch.gt(pred1_loss1, min_error).float()
                error_map_loss1 = error_map_loss1.mul(self.offset_weight)
                error_map_loss1.add_(1.0)

            pred1_loss1.mul_(error_map_loss1.detach())

            offset_loss = self.smoothl1(gt_offsets.detach(), offsets)

            loss1 = torch.mean(pred1_loss1)
            loss2 = self.crossentropy(pred2, target)
            loss3 = self.bceloss(torch.sigmoid(se_pred), se_target)
            loss4 = offset_loss

            return self.loss_weight * (loss1 + self.aux_weight * loss2 + self.se_weight * loss3 + self.location_regression_weight * loss4)
        elif not self.se_loss:
            pred1_diffdup, pred1_detup, grid, pred1_lt_detup, pred1_lb_detup, pred1_rt_detup, pred1_rb_detup, pred2, offsets, target = tuple(
                inputs)
            se_target = self._get_batch_label_vector(target, nclass=self.nclass).type_as(pred1_diffdup)

            pred1_diffup_logsoftmax = self.logsoftmax(pred1_diffdup)

            target_1 = F.interpolate(target.unsqueeze(dim=1).float(),
                                     size=(pred1_diffdup.size(2), pred1_diffdup.size(3)), mode='nearest')

            pred1_loss1 = self.nllloss(pred1_diffup_logsoftmax, target_1.squeeze().long()).unsqueeze(dim=1)

            with torch.no_grad():
                pred1_detup_logsoftmax = self.logsoftmax(pred1_detup)
                pred1_lt_detup_logsoftmax = self.logsoftmax(pred1_lt_detup)
                pred1_lb_detup_logsoftmax = self.logsoftmax(pred1_lb_detup)
                pred1_rt_detup_logsoftmax = self.logsoftmax(pred1_rt_detup)
                pred1_rb_detup_logsoftmax = self.logsoftmax(pred1_rb_detup)

                pred1_loss2 = self.nllloss(pred1_detup_logsoftmax, target_1.squeeze().long()).unsqueeze(dim=1)
                pred1_loss3 = self.nllloss(pred1_lt_detup_logsoftmax, target_1.squeeze().long()).unsqueeze(dim=1)
                pred1_loss4 = self.nllloss(pred1_lb_detup_logsoftmax, target_1.squeeze().long()).unsqueeze(dim=1)
                pred1_loss5 = self.nllloss(pred1_rt_detup_logsoftmax, target_1.squeeze().long()).unsqueeze(dim=1)
                pred1_loss6 = self.nllloss(pred1_rb_detup_logsoftmax, target_1.squeeze().long()).unsqueeze(dim=1)

                coords_lt = grid.floor().float() - grid.float()
                coords_rb = grid.ceil().float() - grid.float()
                coords_lb = torch.cat((coords_rb[:, 0, :, :].unsqueeze(dim=1), coords_lt[:, 1, :, :].unsqueeze(dim=1)),
                                      1)  # coords_lt[..., 0] : row | coords_lt[..., 1] : col
                coords_rt = torch.cat((coords_lt[:, 0, :, :].unsqueeze(dim=1), coords_rb[:, 1, :, :].unsqueeze(dim=1)),
                                      1)

                gt_offsets = torch.zeros(offsets.shape).to(offsets.device)
                gt_offsets = gt_offsets + offsets
                min_error = pred1_loss1

                error_map = torch.lt(pred1_loss3, min_error).float()
                gt_offsets = gt_offsets + error_map * (coords_lt - gt_offsets)
                min_error = torch.min(pred1_loss3, min_error)

                error_map = torch.lt(pred1_loss4, min_error).float()
                gt_offsets = gt_offsets + error_map * (coords_lb - gt_offsets)
                min_error = torch.min(pred1_loss4, min_error)

                error_map = torch.lt(pred1_loss5, min_error).float()
                gt_offsets = gt_offsets + error_map * (coords_rt - gt_offsets)
                min_error = torch.min(pred1_loss5, min_error)

                error_map = torch.lt(pred1_loss6, min_error).float()
                gt_offsets = gt_offsets + error_map * (coords_rb - gt_offsets)
                min_error = torch.min(pred1_loss6, min_error)

                error_map_loss1 = torch.gt(pred1_loss1, min_error).float()
                error_map_loss1 = error_map_loss1.mul(self.offset_weight)
                error_map_loss1.add_(1.0)

            pred1_loss1.mul_(error_map_loss1.detach())

            offset_loss = self.smoothl1(gt_offsets.detach(), offsets)

            loss1 = torch.mean(pred1_loss1)
            loss2 = self.crossentropy(pred2, target)
            loss4 = offset_loss

            return self.loss_weight * (loss1 + self.aux_weight * loss2 + self.location_regression_weight * loss4)
        elif not self.aux:
            pred1_diffdup, se_pred, pred1_detup, grid, pred1_lt_detup, pred1_lb_detup, pred1_rt_detup, pred1_rb_detup, offsets, target = tuple(
                inputs)
            se_target = self._get_batch_label_vector(target, nclass=self.nclass).type_as(pred1_diffdup)

            pred1_diffup_logsoftmax = self.logsoftmax(pred1_diffdup)

            target_1 = F.interpolate(target.unsqueeze(dim=1).float(),
                                     size=(pred1_diffdup.size(2), pred1_diffdup.size(3)), mode='nearest')

            pred1_loss1 = self.nllloss(pred1_diffup_logsoftmax, target_1.squeeze().long()).unsqueeze(dim=1)

            with torch.no_grad():
                pred1_detup_logsoftmax = self.logsoftmax(pred1_detup)
                pred1_lt_detup_logsoftmax = self.logsoftmax(pred1_lt_detup)
                pred1_lb_detup_logsoftmax = self.logsoftmax(pred1_lb_detup)
                pred1_rt_detup_logsoftmax = self.logsoftmax(pred1_rt_detup)
                pred1_rb_detup_logsoftmax = self.logsoftmax(pred1_rb_detup)

                pred1_loss2 = self.nllloss(pred1_detup_logsoftmax, target_1.squeeze().long()).unsqueeze(dim=1)
                pred1_loss3 = self.nllloss(pred1_lt_detup_logsoftmax, target_1.squeeze().long()).unsqueeze(dim=1)
                pred1_loss4 = self.nllloss(pred1_lb_detup_logsoftmax, target_1.squeeze().long()).unsqueeze(dim=1)
                pred1_loss5 = self.nllloss(pred1_rt_detup_logsoftmax, target_1.squeeze().long()).unsqueeze(dim=1)
                pred1_loss6 = self.nllloss(pred1_rb_detup_logsoftmax, target_1.squeeze().long()).unsqueeze(dim=1)

                coords_lt = grid.floor().float() - grid.float()
                coords_rb = grid.ceil().float() - grid.float()
                coords_lb = torch.cat((coords_rb[:, 0, :, :].unsqueeze(dim=1), coords_lt[:, 1, :, :].unsqueeze(dim=1)),
                                      1)  # coords_lt[..., 0] : row | coords_lt[..., 1] : col
                coords_rt = torch.cat((coords_lt[:, 0, :, :].unsqueeze(dim=1), coords_rb[:, 1, :, :].unsqueeze(dim=1)),
                                      1)

                gt_offsets = torch.zeros(offsets.shape).to(offsets.device)
                gt_offsets = gt_offsets + offsets
                min_error = pred1_loss1

                error_map = torch.lt(pred1_loss3, min_error).float()
                gt_offsets = gt_offsets + error_map * (coords_lt - gt_offsets)
                min_error = torch.min(pred1_loss3, min_error)

                error_map = torch.lt(pred1_loss4, min_error).float()
                gt_offsets = gt_offsets + error_map * (coords_lb - gt_offsets)
                min_error = torch.min(pred1_loss4, min_error)

                error_map = torch.lt(pred1_loss5, min_error).float()
                gt_offsets = gt_offsets + error_map * (coords_rt - gt_offsets)
                min_error = torch.min(pred1_loss5, min_error)

                error_map = torch.lt(pred1_loss6, min_error).float()
                gt_offsets = gt_offsets + error_map * (coords_rb - gt_offsets)
                min_error = torch.min(pred1_loss6, min_error)

                error_map_loss1 = torch.gt(pred1_loss1, min_error).float()
                error_map_loss1 = error_map_loss1.mul(self.offset_weight)
                error_map_loss1.add_(1.0)

            pred1_loss1.mul_(error_map_loss1.detach())

            offset_loss = self.smoothl1(gt_offsets.detach(), offsets)

            loss1 = torch.mean(pred1_loss1)
            loss3 = self.bceloss(torch.sigmoid(se_pred), se_target)
            loss4 = offset_loss

            return self.loss_weight * (loss1 + self.se_weight * loss3 + self.location_regression_weight * loss4)
        else:
            pred1_diffdup, pred1_detup, grid, pred1_lt_detup, pred1_lb_detup, pred1_rt_detup, pred1_rb_detup, offsets, target = tuple(
                inputs)
            se_target = self._get_batch_label_vector(target, nclass=self.nclass).type_as(pred1_diffdup)

            pred1_diffup_logsoftmax = self.logsoftmax(pred1_diffdup)

            target_1 = F.interpolate(target.unsqueeze(dim=1).float(),
                                     size=(pred1_diffdup.size(2), pred1_diffdup.size(3)), mode='nearest')

            pred1_loss1 = self.nllloss(pred1_diffup_logsoftmax, target_1.squeeze().long()).unsqueeze(dim=1)

            with torch.no_grad():
                pred1_detup_logsoftmax = self.logsoftmax(pred1_detup)
                pred1_lt_detup_logsoftmax = self.logsoftmax(pred1_lt_detup)
                pred1_lb_detup_logsoftmax = self.logsoftmax(pred1_lb_detup)
                pred1_rt_detup_logsoftmax = self.logsoftmax(pred1_rt_detup)
                pred1_rb_detup_logsoftmax = self.logsoftmax(pred1_rb_detup)

                pred1_loss2 = self.nllloss(pred1_detup_logsoftmax, target_1.squeeze().long()).unsqueeze(dim=1)
                pred1_loss3 = self.nllloss(pred1_lt_detup_logsoftmax, target_1.squeeze().long()).unsqueeze(dim=1)
                pred1_loss4 = self.nllloss(pred1_lb_detup_logsoftmax, target_1.squeeze().long()).unsqueeze(dim=1)
                pred1_loss5 = self.nllloss(pred1_rt_detup_logsoftmax, target_1.squeeze().long()).unsqueeze(dim=1)
                pred1_loss6 = self.nllloss(pred1_rb_detup_logsoftmax, target_1.squeeze().long()).unsqueeze(dim=1)

                coords_lt = grid.floor().float() - grid.float()
                coords_rb = grid.ceil().float() - grid.float()
                coords_lb = torch.cat((coords_rb[:, 0, :, :].unsqueeze(dim=1), coords_lt[:, 1, :, :].unsqueeze(dim=1)),
                                      1)  # coords_lt[..., 0] : row | coords_lt[..., 1] : col
                coords_rt = torch.cat((coords_lt[:, 0, :, :].unsqueeze(dim=1), coords_rb[:, 1, :, :].unsqueeze(dim=1)),
                                      1)

                gt_offsets = torch.zeros(offsets.shape).to(offsets.device)
                gt_offsets = gt_offsets + offsets
                min_error = pred1_loss1

                error_map = torch.lt(pred1_loss3, min_error).float()
                gt_offsets = gt_offsets + error_map * (coords_lt - gt_offsets)
                min_error = torch.min(pred1_loss3, min_error)

                error_map = torch.lt(pred1_loss4, min_error).float()
                gt_offsets = gt_offsets + error_map * (coords_lb - gt_offsets)
                min_error = torch.min(pred1_loss4, min_error)

                error_map = torch.lt(pred1_loss5, min_error).float()
                gt_offsets = gt_offsets + error_map * (coords_rt - gt_offsets)
                min_error = torch.min(pred1_loss5, min_error)

                error_map = torch.lt(pred1_loss6, min_error).float()
                gt_offsets = gt_offsets + error_map * (coords_rb - gt_offsets)
                min_error = torch.min(pred1_loss6, min_error)

                error_map_loss1 = torch.gt(pred1_loss1, min_error).float()
                error_map_loss1 = error_map_loss1.mul(self.offset_weight)
                error_map_loss1.add_(1.0)

            pred1_loss1.mul_(error_map_loss1.detach())

            offset_loss = self.smoothl1(gt_offsets.detach(), offsets)

            loss1 = torch.mean(pred1_loss1)
            loss4 = offset_loss

            return self.loss_weight * (loss1 + self.location_regression_weight * loss4)

    @staticmethod
    def to_one_hot(labels, C=2):
        one_hot = torch.zeros(labels.size(0), C, labels.size(2), labels.size(3)).cuda().to(labels.device)
        target = one_hot.scatter_(1, labels.long(), 1.0)
        return target

    @staticmethod
    def _get_batch_label_vector(target, nclass):
        # target is a 3D Variable BxHxW, output is 2D BxnClass
        batch = target.size(0)
        tvect = Variable(torch.zeros(batch, nclass))
        for i in range(batch):
            hist = torch.histc(target[i].cpu().data.float(),
                               bins=nclass, min=0,
                               max=nclass - 1)
            vect = hist > 0
            tvect[i] = vect
        return tvect