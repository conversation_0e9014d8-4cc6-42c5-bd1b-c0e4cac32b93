import torch
import torch.nn as nn
import torch.nn.functional as F

from .utils import weight_reduce_loss
from econn.models.operators.sigmoid_focal_loss import sigmoid_focal_loss as _sigmoid_focal_loss


# This method is only for debugging
def py_sigmoid_focal_loss(pred,
                          target,
                          weight=None,
                          gamma=2.0,
                          alpha=0.25,
                          reduction='mean',
                          avg_factor=None):
    pred_sigmoid = pred.sigmoid()
    target = target.type_as(pred)
    pt = (1 - pred_sigmoid) * target + pred_sigmoid * (1 - target)
    focal_weight = (alpha * target + (1 - alpha) *
                    (1 - target)) * pt.pow(gamma)
    loss = F.binary_cross_entropy_with_logits(
        pred, target, reduction='none') * focal_weight
    loss = weight_reduce_loss(loss, weight, reduction, avg_factor)
    return loss


def sigmoid_focal_loss(pred,
                       target,
                       weight=None,
                       gamma=2.0,
                       alpha=0.25,
                       reduction='mean',
                       avg_factor=None):
    # Function.apply does not accept keyword arguments, so the decorator
    # "weighted_loss" is not applicable
    loss = _sigmoid_focal_loss(pred, target, gamma, alpha)
    # TODO: find a proper way to handle the shape of weight
    if weight is not None:
        weight = weight.view(-1, 1)
    loss = weight_reduce_loss(loss, weight, reduction, avg_factor)
    return loss


class FocalLoss(nn.Module):

    def __init__(self,
                 use_sigmoid=True,
                 gamma=2.0,
                 alpha=0.25,
                 reduction='mean',
                 loss_weight=1.0):
        super(FocalLoss, self).__init__()
        assert use_sigmoid is True, 'Only sigmoid focal loss supported now.'
        self.use_sigmoid = use_sigmoid
        self.gamma = gamma
        self.alpha = alpha
        self.reduction = reduction
        self.loss_weight = loss_weight

    def forward(self,
                pred,
                target,
                weight=None,
                avg_factor=None,
                reduction_override=None):
        assert reduction_override in (None, 'none', 'mean', 'sum')
        reduction = (
            reduction_override if reduction_override else self.reduction)
        if self.use_sigmoid:
            loss_cls = self.loss_weight * sigmoid_focal_loss(
                pred,
                target,
                weight,
                gamma=self.gamma,
                alpha=self.alpha,
                reduction=reduction,
                avg_factor=avg_factor)
        else:
            raise NotImplementedError
        return loss_cls


class SegFocalLoss(nn.Module):
    def __init__(self,
                 aux=False,
                 aux_weight = 0.4,
                 nclass = None,
                 use_sigmoid=True,
                 gamma=2.0,
                 alpha=0.25,
                 reduction='mean',
                 loss_weight=1.0):
        super(SegFocalLoss,self).__init__()
        self.aux = aux
        self.aux_weight = aux_weight
        self.nclass = nclass
        self.use_sigmoid = use_sigmoid
        self.gamma = gamma
        self.alpha = alpha
        self.reduction = reduction
        self.loss_weight = loss_weight
        self.reduction_override = reduction

    def forward(self,
                *inputs):
        logits_4D, labels_4D = tuple(inputs)
        inputs = tuple(list(logits_4D) + [labels_4D])
        if self.aux:
          loss = self._aux_forward(*inputs)
        else:
          loss = self.forward_sigmoid(logits_4D[0], labels_4D)
        # loss = self.forward_softmax_sigmoid(logits_4D, labels_4D)
        return loss

    def _aux_forward(self,
                     *inputs):
      *preds, target = tuple(inputs)
      loss = self.forward_sigmoid(preds[0], target)
      for i in range(1, len(preds)):
          aux_loss = self.forward_sigmoid(preds[i], target)
          loss += self.aux_weight * aux_loss
      return loss

    def forward_sigmoid(self,
                        pred,
                        target,
                        weight=None,
                        avg_factor=None,
                        reduction_override=None):

        target = F.one_hot(target.long(), self.nclass).reshape(-1, self.nclass)
        pred = pred.permute(0, 3, 1, 2).reshape(-1, self.nclass)
        # target = target.permute(0, 3, 1, 2).contiguous()

        assert reduction_override in (None, 'none', 'mean', 'sum')
        reduction = (
          reduction_override if reduction_override else self.reduction)
        if self.use_sigmoid:
            loss_cls = self.loss_weight * sigmoid_focal_loss(
              pred,
              target,
              weight,
              gamma=self.gamma,
              alpha=self.alpha,
              reduction=reduction,
              avg_factor=avg_factor)
        else:
          raise NotImplementedError
        return loss_cls

def _sigmoid_focal_loss_jit(
    inputs: torch.Tensor,
    targets: torch.Tensor,
    alpha: float = -1,
    gamma: float = 2,
    reduction: str = "none",
) -> torch.Tensor:
    """
    Loss used in RetinaNet for dense detection: https://arxiv.org/abs/1708.02002.
    Args:
        inputs: A float tensor of arbitrary shape.
                The predictions for each example.
        targets: A float tensor with the same shape as inputs. Stores the binary
                 classification label for each element in inputs
                (0 for the negative class and 1 for the positive class).
        alpha: (optional) Weighting factor in range (0,1) to balance
                positive vs negative examples. Default = -1 (no weighting).
        gamma: Exponent of the modulating factor (1 - p_t) to
               balance easy vs hard examples.
        reduction: 'none' | 'mean' | 'sum'
                 'none': No reduction will be applied to the output.
                 'mean': The output will be averaged.
                 'sum': The output will be summed.
    Returns:
        Loss tensor with the reduction option applied.
    """
    p = torch.sigmoid(inputs)
    ce_loss = F.binary_cross_entropy_with_logits(inputs, targets, reduction="none")
    p_t = p * targets + (1 - p) * (1 - targets)
    loss = ce_loss * ((1 - p_t) ** gamma)

    if alpha >= 0:
        alpha_t = alpha * targets + (1 - alpha) * (1 - targets)
        loss = alpha_t * loss

    if reduction == "mean":
        loss = loss.mean()
    elif reduction == "sum":
        loss = loss.sum()

    return loss


sigmoid_focal_loss_jit = torch.jit.script(
    _sigmoid_focal_loss_jit
)  # type: torch.jit.ScriptModule