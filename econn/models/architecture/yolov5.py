from econn.models.architecture.base_one_stage import BaseOneStage
import torch.nn as nn

class Yolov5(BaseOneStage):
    def __init__(self,
                 backbone_cfg,
                 task_head_cfg=None,
                 neck_cfg=None,
                  ):
        super(<PERSON>lov5, self).__init__(backbone_cfg,
                                   neck_cfg,
                                   task_head_cfg)
        self.initialize_weights()

    def initialize_weights(self):
        for m in self.modules():
            t = type(m)
            if t is nn.Conv2d:
                pass
            elif t is nn.BatchNorm2d:
                m.eps = 1e-3
                m.momentum = 0.03
            elif t in [nn.LeakyReLU, nn.ReLU]:
                m.inplace = True