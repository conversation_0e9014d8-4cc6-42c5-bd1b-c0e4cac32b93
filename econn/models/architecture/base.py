from abc import ABCMeta, abstractmethod
import torch.nn as nn


class BaseArchitecture(nn.Module, metaclass=ABCMeta):
    def __init__(self):
        super(BaseArchitecture, self).__init__()

    @abstractmethod
    def forward(self, x):
        pass

    @abstractmethod
    def forward_train(self, gt_meta):
        pass

    @abstractmethod
    def inference(self, meta, resize_keep_ratio):
        pass
