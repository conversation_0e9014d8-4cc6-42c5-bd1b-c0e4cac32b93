import torch
import torch.nn as nn
from econn.models.backbones.builder import build_backbone
from econn.models.necks.builder import build_neck
from econn.models.taskheads.builder import build_head
import time

class BaseEncoderDecoder(nn.Module):
    def __init__(self,
                 backbone_cfg,
                 neck_cfg=None,
                 task_head_cfg=None):
        super(BaseEncoderDecoder, self).__init__()
        self.backbone = build_backbone(backbone_cfg)
        if neck_cfg is not None:
            self.neck = build_neck(neck_cfg)
        if task_head_cfg is not None:
            self.task_head = build_head(task_head_cfg)
            if hasattr(self.task_head, 'encoder'):
                self.encoder = self.task_head.encoder  # encoder必须指向一个静态方法

    # def forward(self, x):
    #     x = self.backbone(x)
    #     if hasattr(self, 'neck'):
    #         x = self.neck(x)
    #     if hasattr(self, 'task_head'):
    #         x = self.task_head(x)
    #     return x
    def forward(self, x):
        x1 = self.backbone(x)
        if hasattr(self, 'neck'):
            x = self.neck(x)
        if hasattr(self, 'task_head'):
            x = self.task_head(x1)
        return x
    def inference(self, meta, val_keep_resize_ratio):
        with torch.no_grad():
            preds = self.forward(meta['img'])
            dets = self.task_head.decode(preds, meta, val_keep_resize_ratio)
        return dets

    def forward_train(self, gt_meta):
        preds = self.forward(gt_meta['img'])
        loss, loss_states = self.task_head.loss(preds, gt_meta)
        return preds, loss, loss_states



if __name__ == '__main__':
    from econn.utils.config import cfg, update_config
    import os

    update_config(cfg, r'/home/<USER>/ayang/1-work/eco_nn/econn/configs/bisenet_example.yml')
    print(cfg)
    segmodel = BaseEncoderDecoder(cfg.model.backbone, task_head_cfg=cfg.model.task_head)
    # print(segmodel)
    segmodel.eval()
    input = torch.randn(1, 3, 720, 1280)
    meta0={}
    meta0['img'] = input
    output = segmodel.inference(meta0)
    print(output[0][0].shape)
    # torch.onnx.export(detector, input, os.path.join(r"D:\tmp\test.onnx"), verbose=True)
