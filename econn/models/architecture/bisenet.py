from econn.models.architecture.base_encoder_decoder import BaseEncoderDecoder
import torch
import torch.nn as nn
import torch.nn.functional as F
from econn.ops import resize
class Bisenet(BaseEncoderDecoder):
    def __init__(self,
                 backbone_cfg,
                 neck_cfg,
                 task_head_cfg, ):
        super(Bisenet, self).__init__(backbone_cfg,
                                        neck_cfg,
                                        task_head_cfg)

    def forward(self, x_in):
        x = self.backbone(x_in)
        if hasattr(self, 'neck') and self.neck is not None:
            x = self.neck(x)
        if hasattr(self, 'task_head'):
            x = self.task_head([x_in, x])
            # x = self.task_head(x)
        return x
    
    def inference(self, meta, val_keep_resize_ratio):
        with torch.no_grad():
            preds = self.forward(meta['img'])
            if val_keep_resize_ratio:
                seg_logit = resize(preds[0],
                                    size=(meta['img_info']['height'], meta['img_info']['width']),
                                    mode='bilinear',
                                    align_corners=False,
                                    warning=False)
                output = F.softmax(seg_logit, dim=1)
                out = []
                out.append(output)
                dets = self.task_head.decode(out, meta, val_keep_resize_ratio)
            else:
                dets = self.task_head.decode(preds, meta, val_keep_resize_ratio)
        return dets