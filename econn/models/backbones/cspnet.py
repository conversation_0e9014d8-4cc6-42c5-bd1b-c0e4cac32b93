import math
import numpy as np
import torch
import torch.nn as nn

def make_divisible(x, divisor):
    # Returns x evenly divisible by divisor
    return math.ceil(x / divisor) * divisor

def autopad(k, p=None):  # kernel, padding
    # Pad to 'same'
    if p is None:
        p = k // 2 if isinstance(k, int) else [x // 2 for x in k]  # auto-pad
    return p

class Focus(nn.Module):
    # Focus wh information into c-space
    def __init__(self, c1, c2, k=1, s=1, p=None, g=1, act=True):  # ch_in, ch_out, kernel, stride, padding, groups
        super(Focus, self).__init__()
        self.conv = Conv(c1 * 4, c2, k, s, p, g, act)
        # self.contract = Contract(gain=2)

    def forward(self, x):  # x(b,c,w,h) -> y(b,4c,w/2,h/2)
        return self.conv(torch.cat([x[..., ::2, ::2], x[..., 1::2, ::2], x[..., ::2, 1::2], x[..., 1::2, 1::2]], 1))
        # return self.conv(self.contract(x))

class Conv(nn.Module):
    # Standard convolution
    def __init__(self, c1, c2, k=1, s=1, p=None, g=1, act=True):  # ch_in, ch_out, kernel, stride, padding, groups
        super(Conv, self).__init__()
        self.conv = nn.Conv2d(c1, c2, k, s, autopad(k, p), groups=g, bias=False)
        self.bn = nn.BatchNorm2d(c2)
        self.act = nn.ReLU() if act is True else (act if isinstance(act, nn.Module) else nn.Identity())

    def forward(self, x):
        return self.act(self.bn(self.conv(x)))

    def fuseforward(self, x):
        return self.act(self.conv(x))

class Bottleneck(nn.Module):
    # Standard bottleneck
    def __init__(self, c1, c2, shortcut=True, g=1, e=0.5):  # ch_in, ch_out, shortcut, groups, expansion
        super(Bottleneck, self).__init__()
        c_ = int(c2 * e)  # hidden channels
        self.cv1 = Conv(c1, c_, 1, 1)
        self.cv2 = Conv(c_, c2, 3, 1, g=g)
        self.add = shortcut and c1 == c2

    def forward(self, x):
        return x + self.cv2(self.cv1(x)) if self.add else self.cv2(self.cv1(x))

class C3(nn.Module):
    # CSP Bottleneck with 3 convolutions
    def __init__(self, c1, c2, n=1, shortcut=True, g=1, e=0.5):  # ch_in, ch_out, number, shortcut, groups, expansion
        super(C3, self).__init__()
        c_ = int(c2 * e)  # hidden channels
        self.cv1 = Conv(c1, c_, 1, 1)
        self.cv2 = Conv(c1, c_, 1, 1)
        self.cv3 = Conv(2 * c_, c2, 1)  # act=FReLU(c2)
        self.m = nn.Sequential(*[Bottleneck(c_, c_, shortcut, g, e=1.0) for _ in range(n)])
        # self.m = nn.Sequential(*[CrossConv(c_, c_, 3, 1, g, 1.0, shortcut) for _ in range(n)])

    def forward(self, x):
        return self.cv3(torch.cat((self.m(self.cv1(x)), self.cv2(x)), dim=1))

class SPP(nn.Module):
    # Spatial pyramid pooling layer used in YOLOv3-SPP
    def __init__(self, c1, c2, k=(5, 9, 13)):
        super(SPP, self).__init__()
        c_ = c1 // 2  # hidden channels
        self.cv1 = Conv(c1, c_, 1, 1)
        self.cv2 = Conv(c_ * (len(k) + 1), c2, 1, 1)
        self.m = nn.ModuleList([nn.MaxPool2d(kernel_size=x, stride=1, padding=x // 2) for x in k])

    def forward(self, x):
        x = self.cv1(x)
        return self.cv2(torch.cat([x] + [m(x) for m in self.m], 1))


class Concat(nn.Module):
    # Concatenate a list of tensors along dimension
    def __init__(self, dimension=1):
        super(Concat, self).__init__()
        self.d = dimension

    def forward(self, x):
        return torch.cat(x, self.d)


class CSPNet(nn.Module):
    def __init__(self,
                 depth_multiple=0.33,
                 width_multiple=0.50,
                 out_stages=[17, 20, 23]):
        super(CSPNet, self).__init__()
        self.depth_multiple = depth_multiple
        self.width_multiple = width_multiple
        self.out_stages = out_stages
        self.settings = \
            [[-1, 1, Focus, [64, 3]],  # 0-P1/2  # [from, number, module, args]
             [-1, 1, Conv, [128, 3, 2]],  # 1-P2/4
             [-1, 3, C3, [128]],
             [-1, 1, Conv, [256, 3, 2]],  # 3-P3/8
             [-1, 9, C3, [256]],
             [-1, 1, Conv, [512, 3, 2]],  # 5-P4/16
             [-1, 9, C3, [512]],
             [-1, 1, Conv, [1024, 3, 2]],  # 7-P5/32
             [-1, 1, SPP, [1024, [5, 9, 13]]],
             [-1, 3, C3, [1024, False]],  # 9
             [-1, 1, Conv, [512, 1, 1]],
             [-1, 1, nn.Upsample, [None, 2, 'nearest']],
             [[-1, 6], 1, Concat, [1]],  # cat backbone P4
             [-1, 3, C3, [512, False]],  # 13

             [-1, 1, Conv, [256, 1, 1]],
             [-1, 1, nn.Upsample, [None, 2, 'nearest']],
             [[-1, 4], 1, Concat, [1]],  # cat backbone P3
             [-1, 3, C3, [256, False]],  # 17 (P3/8-small)

             [-1, 1, Conv, [256, 3, 2]],
             [[-1, 14], 1, Concat, [1]],  # cat head P4
             [-1, 3, C3, [512, False]],  # 20 (P4/16-medium)

             [-1, 1, Conv, [512, 3, 2]],
             [[-1, 10], 1, Concat, [1]],  # cat head P5
             [-1, 3, C3, [1024, False]]]  # 23 (P5/32-large)

        self.model, self.save = self.parse_model()

    def forward(self,x):
        y, dt, output = [], [], []
        for m in self.model:
            if m.f != -1:
                x = y[m.f] if isinstance(m.f, int) else [x if j == -1 else y[j] for j in m.f]
            x = m(x)
            y.append(x if m.i in self.save else None)
            if m.i in self.out_stages:
                output.append(x)
        return tuple(output)

    def parse_model(self):
        gd, gw = self.depth_multiple, self.width_multiple
        ch = [3]
        layers, save, c2 = [], [], ch[-1]
        for i,(f,n,m,args) in enumerate(self.settings):
            m = eval(m) if isinstance(m, str) else m
            for j, a in enumerate(args):
                try:
                    args[j] = eval(a) if isinstance(a, str) else a  # eval strings
                except:
                    pass

            n = max(round(n * gd), 1) if n > 1 else n
            if m in [Conv, SPP, C3, Focus]:
                c1, c2 = ch[f], args[0]
                c2 = make_divisible(c2 * gw, 8)
                args = [c1, c2, *args[1:]]
                if m in [C3]:
                    args.insert(2, n)  # number of repeats
                    n = 1
            elif m is Concat:
                c2 = sum([ch[x] for x in f])
            else:
                c2 = ch[f]

            m_ = nn.Sequential(*[m(*args) for _ in range(n)]) if n > 1 else m(*args)
            m_.i, m_.f  = i, f
            save.extend(x % i for x in ([f] if isinstance(f, int) else f) if x != -1)  # append to savelist
            layers.append(m_)
            if i == 0:
                ch = []
            ch.append(c2)
        return nn.Sequential(*layers), sorted(save)

if __name__ == '__main__':
    model = CSPNet()
    input = torch.randn(1, 3, 320, 320)
    output = model(input)
    print(output[0].shape,output[1].shape,output[2].shape)



