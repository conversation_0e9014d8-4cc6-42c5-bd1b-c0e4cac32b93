import math
import numpy as np
import torch
import torch.nn as nn

def make_divisible(x, divisor):
    # Returns x evenly divisible by divisor
    return math.ceil(x / divisor) * divisor

def autopad(k, p=None):  # kernel, padding
    # Pad to 'same'
    if p is None:
        p = k // 2 if isinstance(k, int) else [x // 2 for x in k]  # auto-pad
    return p

class Focus(nn.Module):
    # Focus wh information into c-space
    def __init__(self, c1, c2, k=1, s=1, p=None, g=1, act=True):  # ch_in, ch_out, kernel, stride, padding, groups
        super(Focus, self).__init__()
        self.conv = Conv(c1 * 4, c2, k, s, p, g, act)
        # self.contract = Contract(gain=2)

    def forward(self, x):  # x(b,c,w,h) -> y(b,4c,w/2,h/2)
        return self.conv(torch.cat([x[..., ::2, ::2], x[..., 1::2, ::2], x[..., ::2, 1::2], x[..., 1::2, 1::2]], 1))
        # return self.conv(self.contract(x))

class Conv(nn.Module):
    # Standard convolution
    def __init__(self, c1, c2, k=1, s=1, p=None, g=1, act=True):  # ch_in, ch_out, kernel, stride, padding, groups
        super(Conv, self).__init__()
        self.conv = nn.Conv2d(c1, c2, k, s, autopad(k, p), groups=g, bias=False)
        self.bn = nn.BatchNorm2d(c2)
        self.act = nn.ReLU() if act is True else (act if isinstance(act, nn.Module) else nn.Identity())

    def forward(self, x):
        return self.act(self.bn(self.conv(x)))

    def fuseforward(self, x):
        return self.act(self.conv(x))

class Bottleneck(nn.Module):
    # Standard bottleneck
    def __init__(self, c1, c2, shortcut=True, g=1, e=0.5):  # ch_in, ch_out, shortcut, groups, expansion
        super(Bottleneck, self).__init__()
        c_ = int(c2 * e)  # hidden channels
        self.cv1 = Conv(c1, c_, 1, 1)
        self.cv2 = Conv(c_, c2, 3, 1, g=g)
        self.add = shortcut and c1 == c2

    def forward(self, x):
        return x + self.cv2(self.cv1(x)) if self.add else self.cv2(self.cv1(x))

class C3(nn.Module):
    # CSP Bottleneck with 3 convolutions
    def __init__(self, c1, c2, n=1, shortcut=True, g=1, e=0.5):  # ch_in, ch_out, number, shortcut, groups, expansion
        super(C3, self).__init__()
        c_ = int(c2 * e)  # hidden channels
        self.cv1 = Conv(c1, c_, 1, 1)
        self.cv2 = Conv(c1, c_, 1, 1)
        self.cv3 = Conv(2 * c_, c2, 1)  # act=FReLU(c2)
        self.m = nn.Sequential(*[Bottleneck(c_, c_, shortcut, g, e=1.0) for _ in range(n)])
        # self.m = nn.Sequential(*[CrossConv(c_, c_, 3, 1, g, 1.0, shortcut) for _ in range(n)])

    def forward(self, x):
        return self.cv3(torch.cat((self.m(self.cv1(x)), self.cv2(x)), dim=1))

class SPP(nn.Module):
    # Spatial pyramid pooling layer used in YOLOv3-SPP
    def __init__(self, c1, c2, k=(5, 9, 13)):
        super(SPP, self).__init__()
        c_ = c1 // 2  # hidden channels
        self.cv1 = Conv(c1, c_, 1, 1)
        self.cv2 = Conv(c_ * (len(k) + 1), c2, 1, 1)
        self.m = nn.ModuleList([nn.MaxPool2d(kernel_size=x, stride=1, padding=x // 2) for x in k])

    def forward(self, x):
        x = self.cv1(x)
        return self.cv2(torch.cat([x] + [m(x) for m in self.m], 1))

class CSPNet(nn.Module):
    def __init__(self,depth_multiple=0.33,width_multiple=0.50,out_stages=[4,6,9]):
        super(CSPNet, self).__init__()
        self.setting = [[1,Focus, [3,64,3]], #0-P1/2
                        [1,Conv, [64,128,3,2]], #1-P2/4
                        [3,C3,[128,128]], #2
                        [1,Conv, [128,256,3,2]], #3-P3/8
                        [9,C3,[256,256]], #4
                        [1,Conv, [256,512,3,2]], #5-P4/16
                        [9,C3,[512,512]], #6
                        [1,Conv, [512,1024,3,2]], #7-P5/32
                        [1,SPP, [1024,1024,[5,9,13]]],
                        [3,C3,[1024,1024,False]]]
        self.depth_multiple=depth_multiple
        self.width_multiple=width_multiple
        self.out_stages = out_stages
        self.stage = self.build_stage(self.setting)

    def build_stage(self,args_all):
        gd,gw = self.depth_multiple, self.width_multiple
        layers = []
        for n,m,args in args_all:
            m = eval(m) if isinstance(m, str) else m
            n = max(round(n * gd), 1) if n > 1 else n

            if m in [Conv, Focus, C3, SPP]:
                c1,c2 = args[0],args[1]
                if c1 != 3:
                    c1 = make_divisible(c1*gw, 8)
                c2 = make_divisible(c2*gw, 8)
                args = [c1, c2, *args[2:]]
                if m in [C3]:
                    args.insert(2,n)
                    n=1
            m_ = nn.Sequential(*[m(*args) for _ in range(n)]) if n > 1 else m(*args)
            layers.append(m_)
        return nn.Sequential(*layers)

    def forward(self,x):
        output = []
        for i,m in enumerate(self.stage):
            x = m(x)
            if i in self.out_stages:
                output.append(x)
        return tuple(output)

if __name__ == '__main__':
    model = CSPNet(out_stages=[9])
    print(model)
    input = torch.randn(1, 3, 320, 320)
    output = model(input)
    print(output[0].shape)

    from thop import profile

    dummy_input = torch.autograd.Variable(torch.randn(1, 3, 224, 224))
    flops, params = profile(model, inputs=(dummy_input,))
    print('flops:{}\nparams:{}'.format(flops, params))

