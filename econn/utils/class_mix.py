import torch
import numpy as np
import econn.utils.transforms as transforms
import random


def generate_class_mask(pred, classes):
    pred, classes = torch.broadcast_tensors(pred.unsqueeze(0), classes.unsqueeze(1).unsqueeze(2))
    N = pred.eq(classes).sum(0)
    return N


def gen_mix_mask(mask, ignore_label):
    batch_size = mask.shape[0]
    mix_mask = None
    for image_i in range(batch_size):
        classes = torch.unique(mask[image_i])
        classes = classes[classes != ignore_label]
        n_classes = classes.shape[0]
        classes = (classes[torch.Tensor(
            np.random.choice(n_classes, int((n_classes - n_classes % 2) / 2), replace=False)).long()]).cuda()
        if image_i == 0:
            # 第一张图像
            mix_mask = generate_class_mask(mask[image_i], classes).unsqueeze(0).cuda()
        else:
            mix_mask = torch.cat((mix_mask,
                                  generate_class_mask(mask[image_i], classes).unsqueeze(0).cuda()))
    return mix_mask


def strongTransform(parameters, data=None, target=None):
    assert ((data is not None) or (target is not None))
    data, target = transforms.mix(mask=parameters["Mix"], data=data, target=target)
    data, target = transforms.colorJitter(colorJitter=parameters["ColorJitter"],
                                          img_mean=torch.from_numpy(np.array([0, 0, 0])).cuda(), data=data,
                                          target=target)
    data, target = transforms.gaussian_blur(blur=parameters["GaussianBlur"], data=data, target=None)
    data, target = transforms.flip(flip=parameters["flip"], data=data, target=target)
    return data, target


def class_mix(pred, data, cfg):
    """
    do class mix as described in paper: ClassMix
    Parameters
    ----------
    pred: pred from student model
    data: unlabeled img
    cfg

    Returns
    -------

    """
    pred = torch.softmax(pred.detach(), dim=1)  # [B 19 W H]
    pred_max_probs, pred_argmax = torch.max(pred, dim=1)  # 第一个tensor最大值；第二个tensor是每行最大值的索引

    mix_mask = gen_mix_mask(pred_argmax, cfg.train.ignore_label)

    strong_parameters = {"Mix": mix_mask}
    if cfg.data.unlabeled_train.flip:
        strong_parameters["flip"] = random.randint(0, 1)
    else:
        strong_parameters["flip"] = 0
    if cfg.data.unlabeled_train.color_jitter:
        strong_parameters["ColorJitter"] = random.uniform(0, 1)
    else:
        strong_parameters["ColorJitter"] = 0
    if cfg.data.unlabeled_train.blur:
        strong_parameters["GaussianBlur"] = random.uniform(0, 1)
    else:
        strong_parameters["GaussianBlur"] = 0

    img_mixed, _ = strongTransform(strong_parameters, data=data)  #
    mask_mixed, _ = strongTransform(strong_parameters, data=pred)

    return {"img_mixed": img_mixed, "mask_mixed": mask_mixed}
