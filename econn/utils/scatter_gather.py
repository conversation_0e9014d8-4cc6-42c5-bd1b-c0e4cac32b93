import torch
from torch.autograd import Variable
from torch.nn.parallel._functions import Scatter, Gather


class List(list):
    def haha(self):
        print('haha')


def my_scatter(input, target_gpus, chunk_sizes):
    ret = []
    for idx, size in enumerate(chunk_sizes):
        ret.append(input[:size])
        del input[:size]
    return tuple(ret)


def scatter(inputs, target_gpus, dim=0, chunk_sizes=None):
    r"""
    Slices variables into approximately equal chunks and
    distributes them across given GPUs. Duplicates
    references to objects that are not variables. Does not
    support Tensors.
    """

    def scatter_map(obj):
        if isinstance(obj, Variable):
            return Scatter.apply(target_gpus, chunk_sizes, dim, obj)
        assert not torch.is_tensor(obj), "Tensors not supported in scatter."
        if isinstance(obj, list):
            return my_scatter(obj, target_gpus, chunk_sizes)
        if isinstance(obj, tuple):
            return list(zip(*map(scatter_map, obj)))
        # if isinstance(obj, list):
        #     return list(map(list, zip(*map(scatter_map, obj))))
        if isinstance(obj, dict):
            return list(map(type(obj), zip(*map(scatter_map, obj.items()))))
        return [obj for targets in target_gpus]

    return scatter_map(inputs)


def scatter_kwargs(inputs, kwargs, target_gpus, dim=0, chunk_sizes=None):
    r"""Scatter with support for kwargs dictionary"""
    inputs = scatter(inputs, target_gpus, dim, chunk_sizes) if inputs else []
    kwargs = scatter(kwargs, target_gpus, dim, chunk_sizes) if kwargs else []
    if len(inputs) < len(kwargs):
        inputs.extend([() for _ in range(len(kwargs) - len(inputs))])
    elif len(kwargs) < len(inputs):
        kwargs.extend([{} for _ in range(len(inputs) - len(kwargs))])
    inputs = tuple(inputs)
    kwargs = tuple(kwargs)
    return inputs, kwargs


def split_out(out_put, begin, end):
    if isinstance(out_put, torch.Tensor):
        return out_put[begin: end]
    res = []
    for out in out_put:
        res.append(split_out(out, begin, end))
    return res


if __name__ == '__main__':
    target_gpus = [0, 1]
    chunk_size = [2, 3]
    meta = {'img_name': List(['a', 'b', 'c', 'd', 'e'])}
    input = scatter(meta, target_gpus, chunk_sizes=chunk_size)
    a = 1
