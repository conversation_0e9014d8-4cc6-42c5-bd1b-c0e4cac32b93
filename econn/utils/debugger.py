"""
Create by <PERSON>qi.Lv
2020/3/18
"""
from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

import numpy as np
import cv2
import copy


# TODO: 改成visualizer
class Debugger(object):
    def __init__(self, class_names, theme='black', down_ratio=4):
        self.imgs = {}
        self.names = class_names
        self.theme = theme
        self.down_ratio = down_ratio
        colors = [(color_list[_]).astype(np.uint8) \
                  for _ in range(len(color_list))]
        self.colors = np.array(colors, dtype=np.uint8).reshape(len(colors), 1, 1, 3)
        if self.theme == 'white':
            self.colors = self.colors.reshape(-1)[::-1].reshape(len(colors), 1, 1, 3)
            self.colors = np.clip(self.colors, 0., 0.6 * 255).astype(np.uint8)

    def add_img(self, img, img_id='default', revert_color=False):
        if revert_color:
            img = 255 - img
        self.imgs[img_id] = img.copy()

    def add_blend_img(self, back, fore, img_id='blend', trans=0.7):
        if self.theme == 'white':
            fore = 255 - fore
        if fore.shape[0] != back.shape[0] or fore.shape[0] != back.shape[1]:
            fore = cv2.resize(fore, (back.shape[1], back.shape[0]))
        if len(fore.shape) == 2:
            fore = fore.reshape(fore.shape[0], fore.shape[1], 1)
        self.imgs[img_id] = (back * (1. - trans) + fore * trans)
        self.imgs[img_id][self.imgs[img_id] > 255] = 255
        self.imgs[img_id][self.imgs[img_id] < 0] = 0
        self.imgs[img_id] = self.imgs[img_id].astype(np.uint8).copy()

    def gen_colormap(self, img, output_res=None):
        img = img.copy()
        c, h, w = img.shape[0], img.shape[1], img.shape[2]
        if output_res is None:
            output_res = (h * self.down_ratio, w * self.down_ratio)
        img = img.transpose(1, 2, 0).reshape(h, w, c, 1).astype(np.float32)
        colors = np.array(
            self.colors, dtype=np.float32).reshape(-1, 3)[:c].reshape(1, 1, c, 3)
        if self.theme == 'white':
            colors = 255 - colors
        color_map = (img * colors).max(axis=2).astype(np.uint8)
        color_map = cv2.resize(color_map, (output_res[0], output_res[1]))
        return color_map

    def add_coco_bbox(self, bbox, cat, conf=1, show_txt=True, img_id='default', alpha=1):
        bbox = np.array(bbox, dtype=np.int32)
        # cat = (int(cat) + 1) % 80
        cat = int(cat)
        # print('cat', cat, self.names[cat])
        c = self.colors[cat][0][0].tolist()

        raw_img = copy.deepcopy(self.imgs[img_id])

        if self.theme == 'white':
            c = (255 - np.array(c)).tolist()
        txt = '{}:{:.3f}'.format(self.names[cat], conf)
        font = cv2.FONT_HERSHEY_SIMPLEX
        cat_size = cv2.getTextSize(txt, font, 0.5, 2)[0]
        cv2.rectangle(
            self.imgs[img_id], (bbox[0], bbox[1]), (bbox[2], bbox[3]), c, 1)

        if show_txt:
            cv2.rectangle(self.imgs[img_id],
                          (bbox[0], bbox[1] + cat_size[1] + 1),
                          (bbox[0] + cat_size[0] + cat_size[1], bbox[1] + 1), c, -1)
            cv2.putText(self.imgs[img_id], txt, (bbox[0], bbox[1] + cat_size[1]),
                        font, 0.5, (0, 0, 0), thickness=1, lineType=cv2.LINE_AA)

        self.imgs[img_id] = cv2.addWeighted(raw_img, 1 - alpha, self.imgs[img_id], alpha, 0)

    def add_coco_mask(self, mask, cat, conf=1, img_id='default', alpha=0.5):
        # TODO: 实现实例分割显示
        # cat = (int(cat) + 1) % 80
        cat = int(cat)
        # print('cat', cat, self.names[cat])
        c = self.colors[cat][0][0].tolist()

        raw_img = copy.deepcopy(self.imgs[img_id])

        if self.theme == 'white':
            c = (255 - np.array(c)).tolist()
        txt = '{}:{:.3f}'.format(self.names[cat], conf)
        font = cv2.FONT_HERSHEY_SIMPLEX
        cat_size = cv2.getTextSize(txt, font, 0.5, 2)[0]

        cv2.drawContours(self.imgs[img_id], [mask], -1, (255, 0, 0), -1)

    def add_coco_hp(self, points, img_id='default'):
        points = np.array(points, dtype=np.int32)
        raw_img = copy.deepcopy(self.imgs[img_id])
        for j in range(17):
            cv2.circle(self.imgs[img_id], (points[j, 0], points[j, 1]), 3, colors_hp[j], -1)
        for j, e in enumerate(edges):
            if points[e].min() > 0:
                cv2.line(self.imgs[img_id], (points[e[0], 0], points[e[0], 1]),
                         (points[e[1], 0], points[e[1], 1]), ec[j], 2, lineType=cv2.LINE_AA)

    def show_all_imgs(self, pause=False, time=0, expname='ctdet'):
        # if not self.ipynb:
        for i, v in self.imgs.items():
            cv2.namedWindow('{}{}'.format(i, expname), 1)
            cv2.imshow('{}{}'.format(i, expname), v)
        if cv2.waitKey(0 if pause else 1) == 27:
            import sys
            sys.exit(0)
    # else:
    #     self.ax = None
    #     nImgs = len(self.imgs)
    #     fig = self.plt.figure(figsize=(nImgs * 10, 10))
    #     nCols = nImgs
    #     nRows = nImgs // nCols
    #     for i, (k, v) in enumerate(self.imgs.items()):
    #         fig.add_subplot(1, nImgs, i + 1)
    #         if len(v.shape) == 3:
    #             self.plt.imshow(cv2.cvtColor(v, cv2.COLOR_BGR2RGB))
    #         else:
    #             self.plt.imshow(v)
    #     self.plt.show()


color_list = np.array(
    [
        1.000, 1.000, 1.000,
        0.850, 0.325, 0.098,
        0.929, 0.694, 0.125,
        0.494, 0.184, 0.556,
        0.466, 0.674, 0.188,
        0.301, 0.745, 0.933,
        0.935, 0.078, 0.184,
        0.300, 0.300, 0.300,
        0.600, 0.600, 0.600,
        1.000, 0.000, 0.000,
        1.000, 0.500, 0.000,
        0.749, 0.749, 0.000,
        0.000, 1.000, 0.000,
        0.000, 0.000, 1.000,
        0.667, 0.000, 1.000,
        0.333, 0.333, 0.000,
        0.333, 0.667, 0.000,
        0.333, 1.000, 0.000,
        0.667, 0.333, 0.000,
        0.667, 0.667, 0.000,
        0.667, 1.000, 0.000,
        1.000, 0.333, 0.000,
        1.000, 0.667, 0.000,
        1.000, 1.000, 0.000,
        0.000, 0.333, 0.500,
        0.000, 0.667, 0.500,
        0.000, 1.000, 0.500,
        0.333, 0.000, 0.500,
        0.333, 0.333, 0.500,
        0.333, 0.667, 0.500,
        0.333, 1.000, 0.500,
        0.667, 0.000, 0.500,
        0.667, 0.333, 0.500,
        0.667, 0.667, 0.500,
        0.667, 1.000, 0.500,
        1.000, 0.000, 0.500,
        1.000, 0.333, 0.500,
        1.000, 0.667, 0.500,
        1.000, 1.000, 0.500,
        0.000, 0.333, 1.000,
        0.000, 0.667, 1.000,
        0.000, 1.000, 1.000,
        0.333, 0.000, 1.000,
        0.333, 0.333, 1.000,
        0.333, 0.667, 1.000,
        0.333, 1.000, 1.000,
        0.667, 0.000, 1.000,
        0.667, 0.333, 1.000,
        0.667, 0.667, 1.000,
        0.667, 1.000, 1.000,
        1.000, 0.000, 1.000,
        1.000, 0.333, 1.000,
        1.000, 0.667, 1.000,
        0.167, 0.000, 0.000,
        0.333, 0.000, 0.000,
        0.500, 0.000, 0.000,
        0.667, 0.000, 0.000,
        0.833, 0.000, 0.000,
        1.000, 0.000, 0.000,
        0.000, 0.167, 0.000,
        0.000, 0.333, 0.000,
        0.000, 0.500, 0.000,
        0.000, 0.667, 0.000,
        0.000, 0.833, 0.000,
        0.000, 1.000, 0.000,
        0.000, 0.000, 0.167,
        0.000, 0.000, 0.333,
        0.000, 0.000, 0.500,
        0.000, 0.000, 0.667,
        0.000, 0.000, 0.833,
        0.000, 0.000, 1.000,
        0.000, 0.000, 0.000,
        0.143, 0.143, 0.143,
        0.286, 0.286, 0.286,
        0.429, 0.429, 0.429,
        0.571, 0.571, 0.571,
        0.714, 0.714, 0.714,
        0.857, 0.857, 0.857,
        0.000, 0.447, 0.741,
        0.50, 0.5, 0
    ]
).astype(np.float32)
color_list = color_list.reshape((-1, 3)) * 255

colors_hp = [(255, 0, 255), (255, 0, 0), (0, 0, 255),
             (255, 0, 0), (0, 0, 255), (255, 0, 0), (0, 0, 255),
             (255, 0, 0), (0, 0, 255), (255, 0, 0), (0, 0, 255),
             (255, 0, 0), (0, 0, 255), (255, 0, 0), (0, 0, 255),
             (255, 0, 0), (0, 0, 255)]
edges = [[0, 1], [0, 2], [1, 3], [2, 4],
         [3, 5], [4, 6], [5, 6],
         [5, 7], [7, 9], [6, 8], [8, 10],
         [5, 11], [6, 12], [11, 12],
         [11, 13], [13, 15], [12, 14], [14, 16]]
ec = [(255, 0, 0), (0, 0, 255), (255, 0, 0), (0, 0, 255),
      (255, 0, 0), (0, 0, 255), (255, 0, 255),
      (255, 0, 0), (255, 0, 0), (0, 0, 255), (0, 0, 255),
      (255, 0, 0), (0, 0, 255), (255, 0, 255),
      (255, 0, 0), (255, 0, 0), (0, 0, 255), (0, 0, 255)]
