"""
Create by Chengqi.Lv
2020/4/1
"""
import torch
import os
import logging


class TensorboardLogger:
    def __init__(self,
                 log_dir,
                 rank
                 ):
        self.rank = rank
        if torch.__version__ >= '1.1' and '.' in torch.__version__:
            try:
                from torch.utils.tensorboard import SummaryWriter
            except ImportError:
                raise ImportError(
                    'Please run "pip install future tensorboard" to install '
                    'the dependencies to use torch.utils.tensorboard '
                    '(applicable to PyTorch 1.1 or higher)')
        else:
            try:
                from tensorboardX import SummaryWriter
            except ImportError:
                raise ImportError('Please install tensorboardX to use '
                                  'TensorboardLoggerHook.')
        if self.rank < 1:
            self.log_dir = os.path.join(log_dir, 'log')
            logging.info('Using Tensorboard, log will be save to {}'.format(self.log_dir))
            self.writer = SummaryWriter(log_dir=self.log_dir)
        else:
            pass

    def scalar_summary(self, tag, phase, value, step):
        if self.rank < 1:
            self.writer.add_scalars(tag, {phase: value}, step)
        else:
            pass

    def add_model_graph(self, model, dummy_input):
        if self.rank < 1:
            self.writer.add_graph(model, input_to_model=dummy_input)
        else:
            pass

