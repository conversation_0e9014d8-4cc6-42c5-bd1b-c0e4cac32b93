"""
Create by Chengqi.Lv
2020/3/13
"""
from __future__ import absolute_import
from __future__ import division
from __future__ import print_function
from .config import CfgNode as CN

_C = CN(new_allowed=True)
_C.save_dir = './'
# common params for NETWORK
_C.model = CN()
_C.model.architecture = 'CenterNet'
_C.model.backbone = CN(new_allowed=True)
_C.model.neck = CN(new_allowed=True)
_C.model.task_head = CN(new_allowed=True)
# _C.model.task_head.task1_seg = CN(new_allowed=True)

# DATASET related params
_C.data = CN(new_allowed=True)

# train
_C.train = CN(new_allowed=True)

# logger
_C.log = CN()
_C.log.interval = 50

# testing
_C.test = CN()
# size of images for each device


def update_config(cfg, args_cfg):
    cfg.defrost()
    cfg.merge_from_file(args_cfg)
    cfg.freeze()


if __name__ == '__main__':
    import sys

    with open(sys.argv[1], 'w') as f:
        print(_C, file=f)
