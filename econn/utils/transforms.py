"""
Create by <PERSON><PERSON>.Lv
2020/3/18
"""
import numpy as np
import cv2
import torch


def bbox_flip(bboxes, img_shape, direction='horizontal'):
    """Flip bboxes horizontally or vertically.

    Args:
        bboxes(ndarray): shape (..., 4*k)
        img_shape(tuple): (height, width)
    """
    assert bboxes.shape[-1] % 4 == 0
    flipped = bboxes.copy()
    if direction == 'horizontal':
        w = img_shape[1]
        flipped[..., 0::4] = w - bboxes[..., 2::4] - 1
        flipped[..., 2::4] = w - bboxes[..., 0::4] - 1
    else:
        h = img_shape[0]
        flipped[..., 1::4] = h - bboxes[..., 3::4] - 1
        flipped[..., 3::4] = h - bboxes[..., 1::4] - 1
    return flipped


def keypoints_flip(keypoints, img_shape, direction='horizontal'):
    flip_idx = [[1, 2], [3, 4], [5, 6], [7, 8], [9, 10],
                [11, 12], [13, 14], [15, 16]]
    assert keypoints.shape[-1] % 17 == 0
    keypoints_fliped = keypoints.reshape(-1, 17, 3)
    if direction == 'horizontal':
        w = img_shape[1]
        keypoints_fliped[..., 0] = w - keypoints_fliped[..., 0] - 1
    else:
        h = img_shape[0]
        keypoints_fliped[..., 1] = h - keypoints_fliped[..., 1] - 1
    for e in flip_idx:
        keypoints_fliped[:, e[0]], keypoints_fliped[:, e[1]] = keypoints_fliped[:, e[1]].copy(), keypoints_fliped[:,
                                                                                                 e[0]].copy()
    return keypoints_fliped.reshape(-1, 51)


def bbox2distance(points, bbox, max_dis=None, dt=0.1):
    """Decode bounding box based to distances.
    Args:
        points (Tensor): Shape (n, 2), [x, y].
        bbox (Tensor): Shape (n, 4), "xyxy" format
        max_shape (tuple): Shape of the image.
    Returns:
        Tensor: Decoded distances.
    """
    l = points[:, 0] - bbox[:, 0]
    t = points[:, 1] - bbox[:, 1]
    r = bbox[:, 2] - points[:, 0]
    b = bbox[:, 3] - points[:, 1]
    if max_dis is not None:
        l = l.clamp(min=0, max=max_dis - dt)
        t = t.clamp(min=0, max=max_dis - dt)
        r = r.clamp(min=0, max=max_dis - dt)
        b = b.clamp(min=0, max=max_dis - dt)
    return torch.stack([l, t, r, b], -1)


def distance2bbox(points, distance, max_shape=None):
    """Decode distance prediction to bounding box.

    Args:
        points (Tensor): Shape (n, 2), [x, y].
        distance (Tensor): Distance from the given point to 4
            boundaries (left, top, right, bottom).
        max_shape (tuple): Shape of the image.

    Returns:
        Tensor: Decoded bboxes.
    """
    x1 = points[:, 0] - distance[:, 0]
    y1 = points[:, 1] - distance[:, 1]
    x2 = points[:, 0] + distance[:, 2]
    y2 = points[:, 1] + distance[:, 3]
    if max_shape is not None:
        x1 = x1.clamp(min=0, max=max_shape[1] - 1)
        y1 = y1.clamp(min=0, max=max_shape[0] - 1)
        x2 = x2.clamp(min=0, max=max_shape[1] - 1)
        y2 = y2.clamp(min=0, max=max_shape[0] - 1)
    return torch.stack([x1, y1, x2, y2], -1)


# TODO: 完全重写这个变换函数，把多种变换拆分开来
def get_affine_transform(center,
                         scale,
                         rot,
                         output_size,
                         shift=np.array([0, 0], dtype=np.float32),
                         inv=0,
                         resize_keep_ratio=False):

    if not isinstance(scale, np.ndarray) and not isinstance(scale, list):
        scale = np.array([scale, scale], dtype=np.float32)

    scale_tmp = scale
    src_w = scale_tmp[0]
    src_h = scale_tmp[1]
    dst_w = output_size[0]
    dst_h = output_size[1]

    rot_rad = np.pi * rot / 180
    if resize_keep_ratio:
        src_dir = get_dir([0, src_h * -0.5], rot_rad)
        dst_dir_h = dst_h if src_w / src_h < dst_w / dst_h else dst_w / src_w * src_h  # 这么算终于解决了resize问题了吗？？？？
        dst_dir = np.array([0, dst_dir_h * -0.5], np.float32)
    else:
        src_dir = get_dir([0, src_h * -0.5], rot_rad)
        dst_dir = np.array([0, dst_h * -0.5], np.float32)
    src = np.zeros((3, 2), dtype=np.float32)
    dst = np.zeros((3, 2), dtype=np.float32)
    src[0, :] = center + scale_tmp * shift
    src[1, :] = center + src_dir + scale_tmp * shift
    dst[0, :] = [dst_w * 0.5, dst_h * 0.5]
    dst[1, :] = np.array([dst_w * 0.5, dst_h * 0.5], np.float32) + dst_dir

    if resize_keep_ratio:
        src[2:, :] = get_3rd_point_ver2(src[0, :], src[1, :], scale[1], scale[0])
        dst[2:, :] = get_3rd_point_ver2(dst[0, :], dst[1, :], scale[1], scale[0])
    else:
        src[2:, :] = get_3rd_point_ver2(src[0, :], src[1, :], scale[1], scale[0])
        dst[2:, :] = get_3rd_point_ver2(dst[0, :], dst[1, :], dst_h, dst_w)

    if inv:
        trans = cv2.getAffineTransform(np.float32(dst), np.float32(src))
    else:
        trans = cv2.getAffineTransform(np.float32(src), np.float32(dst))

    return trans


def affine_transform(pt, t):
    new_pt = np.array([pt[0], pt[1], 1.], dtype=np.float32).T
    new_pt = np.dot(t, new_pt)  # 旧的点点乘仿射变换矩阵，得到输出图像上的点
    return new_pt[:2]


def get_3rd_point(a, b):
    direct = a - b
    return b + np.array([-direct[1], direct[0]], dtype=np.float32)


def get_3rd_point_ver2(a, b, h, w):
    direct = a - b
    return b + np.array([-direct[1] / h * w, direct[0]], dtype=np.float32)


def get_dir(src_point, rot_rad):
    sn, cs = np.sin(rot_rad), np.cos(rot_rad)

    src_result = [0, 0]
    src_result[0] = src_point[0] * cs - src_point[1] * sn
    src_result[1] = src_point[0] * sn + src_point[1] * cs

    return src_result
