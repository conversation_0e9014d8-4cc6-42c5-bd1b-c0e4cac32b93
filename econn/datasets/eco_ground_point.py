'''
Create by shen huixiang
2020/8/1
'''

from sys import path
from numpy.core.function_base import linspace

from shapely.geometry import point
from econn.augmentations.yolo_mosaic import load_image
import json
import torch
import numpy as np
from .base_dataset import BaseDataset
import os
import cv2
import warnings
import logging

class LinePoint(BaseDataset):
    class_names = ["linePoint"]
    def __init__(self,
                 use_mosaic=False,
                 **kwargs):
        #self.img_path=img_path
        self.use_mosaic = use_mosaic
        super(LinePoint, self).__init__(**kwargs)
        #self.img_info=self.load_img_info(ann_path=ann_pth)

    def load_img_info(self, ann_path):
        with open(ann_path,"r") as fr:
            img_info=json.load(fr)
        return img_info

    def get_per_img_ann(self, idx):
        #img_id=self.img_info.keys()[idx]
        def generate_point(points):
            "两个point"
            max_len=int(max(abs(points[1][1]-points[0][1]),abs(points[1][0]-points[0][0])))
            x_range=linspace(points[0][0],points[1][0],max_len)
            y_range=linspace(points[0][1],points[1][1],max_len)
            point2point=[[int(x),int(y)]for x,y in zip(x_range,y_range)]
            return point2point
            

        def line_to_all_ponit(line):
            line_points=[]
            for x in range(len(line)-1):
                point0=line[x]
                point1=line[x+1]
                point2point=generate_point([point0,point1])
                line_points.extend(point2point)
            return line_points
        lines=self.img_info[str(idx)]["lines"]
        all_points=[]

        for line in lines:
            line_points=line_to_all_ponit(line)
            all_points.extend(line_points)
        ann = dict(lines=lines,lines_points=all_points)
        return ann

    def get_train_data(self, idx):
        img_path = os.path.join(self.img_path, self.img_info[str(idx)]['img_id'])
        #print(img_path)
        img = cv2.imread(img_path)
        assert img is not None, 'Image Not Found ' + img_path
        ann = self.get_per_img_ann(idx)
        img_info=dict(file_name=self.img_info[str(idx)]['img_id'],height=self.img_info[str(idx)]['imageHeight'],width=self.img_info[str(idx)]['imageWidth'],id=idx)
        meta = dict(img=img,
                    gt_lines=ann["lines"],
                    lines_points=ann['lines_points'],
                    gt_bboxes=np.array([]),
                    gt_labels=np.array([]))
        if self.aug is not None:
            meta = self.aug(meta)
        # #-------Debug-----------------
        #show_img = meta['img'].copy()
        # for bbox in meta['gt_bboxes']:
        #     cv2.rectangle(show_img, (int(bbox[0]),int(bbox[1])),(int(bbox[2]),int(bbox[3])),(0,255,0))
        # cv2.imshow('raw', show_img)
        # cv2.waitKey(0)
        meta['img'] = ((meta['img'].astype(np.float32) / 255.) - self.mean) / self.std
        meta['img'] = torch.from_numpy(meta['img'].transpose(2, 0, 1))
        meta['img_info'] = img_info

        if self.encoder is not None:
            meta = self.encoder(meta)
        if self.with_mask:
            meta['gt_masks'] = np.array(meta['gt_masks'])
        return meta

    def get_test_data(self, idx):
        return self.get_train_data(idx)
    
if __name__ =="__main__":
    data=LinePoint(img_path="data/data_ann/wire/images/train",ann_pth="data/data_ann/wire/train.json")
    data.get_train_data(0)