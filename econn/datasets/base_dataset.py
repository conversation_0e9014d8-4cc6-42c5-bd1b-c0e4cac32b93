"""
Create by Chengqi.Lv
2020/3/13
"""
import os
import numpy as np
import torch
from torch.utils.data import Dataset


class BaseDataset(Dataset):
    """

    """
    def __init__(self,
                 ann_path,
                 img_path,
                 input_size,
                 img_norm,
                 with_crowd,
                 with_mask,
                 with_keypoints,
                 aug,
                 resize_keep_ratio,
                 skip_img_without_anno=True,
                 mode='train',
                 encoder=None
                 ):
        assert mode in ['train', 'val']
        self.ann_path = ann_path
        # image data path
        self.img_path = img_path
        # input image size [(width1, height1), ...]
        self.img_size = input_size if isinstance(input_size, list) else [input_size]
        # normalization configs
        self.img_norm = img_norm
        self.mean = np.array(self.img_norm.mean,
                    dtype=np.float32).reshape(1, 1, 3)
        self.std = np.array(self.img_norm.std,
                             dtype=np.float32).reshape(1, 1, 3)
        self.mode = mode
        self.with_crowd = with_crowd
        self.with_mask = with_mask
        self.with_keypoints = with_keypoints
        self.aug = aug
        self.resize_keep_ratio = resize_keep_ratio
        self.skip_img_without_anno = skip_img_without_anno
        self.encoder = encoder
        # annotation
        self.img_info = self.load_img_info(ann_path)

    def __len__(self):
        return len(self.img_info)

    def __getitem__(self, idx):
        if self.mode == 'test':
            return self.get_test_data(idx)
        while True:
            data = self.get_train_data(idx)
            if data is None:
                idx = self.get_another_id(idx)
                continue
            return data

    def load_img_info(self, ann_path):
        raise NotImplementedError

    def get_train_data(self, idx):
        raise NotImplementedError

    def get_test_data(self, idx):
        raise NotImplementedError

    def get_another_id(self, idx):
        return np.random.random_integers(0, len(self.img_info)-1)




