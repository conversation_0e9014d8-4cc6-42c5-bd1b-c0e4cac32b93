"""
create by yang<PERSON><PERSON><PERSON>
date:11/5/20
"""
from pycocotools.coco import COCO
import torch
import numpy as np
from .base_dataset import BaseDataset
import os
import cv2
import warnings
import logging
from ..augmentations.yolo_mosaic import load_mosaic
import random
from PIL import Image,ImageFilter, ImageOps, ImageEnhance
import matplotlib.pyplot as plt
# from albumentations import (Blur,Flip,ShiftScaleRotate,GridDistortion,ElasticTransform,HorizontalFlip,CenterCrop,
#                             HueSaturationValue,Transpose,RandomBrightnessContrast,CLAHE,RandomCrop,Cutout,CoarseDropout,
#                             CoarseDropout,Normalize,ToFloat,OneOf,Compose,Resize,RandomRain,RandomFog,Lambda
#                             ,ChannelDropout,ISONoise,VerticalFlip,RandomGamma,RandomRotate90)
class ECOSemanticIndoorDataset(BaseDataset):
    def __init__(self, base_size,
                       valid_ids,
                       # class_names,
                       **kwargs):
        super(ECOSemanticIndoorDataset,self).__init__(**kwargs)
        self.basesize = base_size
        self._valid_ids = valid_ids
        # self.class_names = self.read_class_file(self.ann_path)
        self.key = np.zeros(100, np.uint8)
        id_count = 0
        for id in list(self._valid_ids):
            self.key[id] = id_count
            id_count+=1
        self._mapping = np.array(range(0, len(self.key) - 1)).astype('int32')
        self.img_info = self.load_img_info(self.ann_path)
        # self.aug_file_list = open(self.ann_path + '/trainvallist/' + 'rugtrainlist.txt', 'r').readlines()
        assert len(self.img_info) >0, "img size 0" 
        # self.coco = COCO(os.path.join(self.ann_path, '../../',self.mode + '2017_semantic.json'))

    def _class_to_index(self, mask):
        # assert the value
        values = np.unique(mask)
        for value in values:
            assert (value in self._mapping), "wrong mask value {}".format(value)
        index = np.digitize(mask.ravel(), self._mapping, right=True)
        return self.key[index].reshape(mask.shape)

    def load_img_info(self, ann_dir):
        print(ann_dir,11111111111111111111111111111)
        assert os.path.exists(ann_dir), "Please setup the dataset anno path"
        if self.mode in ['train','val']:
            file_list = open(ann_dir + '/trainvallist/' + self.mode + 'list.txt','r').readlines()
        else:
            file_list = open(ann_dir + '/trainvallist/' + self.mode + 'list.txt', 'r').readlines()
        img_info_list = []
        assert len(file_list) > 0
        fileindex = 0
        for file in file_list:
            filename = file.rstrip('.\n').split('.')[0]
            ann_path = os.path.join(self.img_path, 'Segmentations/mask_png' ,filename + '.png')
            im_path = os.path.join(self.img_path, 'JPEGImages', filename +'.jpg')
            if os.path.exists(im_path) and os.path.exists(ann_path):
                img_info = dict(
                    filename = filename,
                    id =fileindex,
                    im_path = im_path,
                    ann_path = ann_path,
                )
                img_info_list.append(img_info)
            fileindex+=1
        random.shuffle(img_info_list)
        return img_info_list

    def get_train_data(self, idx):
        img = Image.open(self.img_info[idx]['im_path'])
        # img =  cv2.imread(self.img_info[idx]['im_path'])
        # origin_img.show()
        # img = np.fromfile(self.img_info[idx]['im_path'], np.float32)
        mask = Image.open(self.img_info[idx]['ann_path'])
        # print(self.img_info[idx]['ann_path'],111111111111)
        # mask.show()

        if self.mode == 'train':
            # img, mask = self.warp_transfrom(img, mask, self.img_info[idx]['filename'], copypaste_ratio=0.3)
            img, mask = self.sync_transform(img, mask)
            #TODO:add albutmentations数据增广的方案
            # img = CLAHE(clip_limit=4.0, tile_grid_size=(32, 32), p=0.5)(image=img)['image']
        elif self.mode == 'val':
            img, mask = self.val_transform(img, mask)
        #TODO:add test
        elif self.mode == 'test':
            img = img.resize(self.img_size, Image.BILINEAR)
            mask = mask.resize(self.img_size, Image.NEAREST)
            img, mask = self._img_transform(img), self._mask_transform(mask)
        

        #TODO: Crop image and mask from 360 to 320
        # img = img[:320,:,:]
        # mask = mask[:320,:]


        meta=dict(
            img = img,
            gt_masks = mask,
            img_info = self.img_info[idx]
        )
        #TODO:如果没有数据增广，请注释下面两行
        # if self.aug is not None and self.mode == 'train':
        #     meta = self.aug(meta)
        
        # plt.figure()
        # plt.imshow(img)
        # plt.savefig('color.png')

        meta['img'] = ((meta['img'].astype(np.float32) / 255.) - self.mean) / self.std
        meta['img'] = torch.from_numpy(meta['img'].transpose(2, 0, 1))

        return meta

    def get_test_data(self, idx):
        return self.get_train_data(idx)


    def val_transform(self, img, mask):
        # random mirror
        if random.random() < 0.5:
            img = img.transpose(Image.FLIP_LEFT_RIGHT)
            mask = mask.transpose(Image.FLIP_LEFT_RIGHT)
        #TODO
        crop_size_w = self.img_size[0]
        crop_size_h = self.img_size[1]
        # random scale (short edge)
        short_size = random.randint(int(self.basesize * 0.5), int(self.basesize * 2.0))
        w, h = img.size
        if h > w:
            ow = short_size
            oh = int(1.0 * h * ow / w)
        else:
            oh = short_size
            ow = int(1.0 * w * oh / h)
        # img.show()
        # self.show_corlor_mask(mask)

        img = img.resize((ow, oh), Image.BILINEAR)
        mask = mask.resize((ow, oh), Image.NEAREST)
        # pad crop
        if short_size < crop_size_h:
            padh = crop_size_h - oh if oh < crop_size_h else 0
            padw = crop_size_w - ow if ow < crop_size_w else 0
            img = ImageOps.expand(img, border=(0, 0, padw, padh), fill=0)
            mask = ImageOps.expand(mask, border=(0, 0, padw, padh), fill=0)
            # img.show()
            # mask.show()
        # random crop crop_size
        w, h = img.size
        x1 = random.randint(0, w - crop_size_w)
        y1 = random.randint(0, h - crop_size_h)
        img = img.crop((x1, y1, x1 + crop_size_w, y1 + crop_size_h))
        mask = mask.crop((x1, y1, x1 + crop_size_w, y1 + crop_size_h))
        # ttt = np.unique(np.array(mask))
        # img.show()
        # self.show_corlor_mask(mask)

        # gaussian blur as in PSP
        if random.random() < 0.5:
            img = img.filter(ImageFilter.GaussianBlur(radius=random.random()))
        # final transform
        img, mask = self._img_transform(img), self._mask_transform(mask)
        return img, mask

    def sync_transform(self, img, mask):
        # random mirror
        if random.random() < 0.5:
            img = img.transpose(Image.FLIP_LEFT_RIGHT)
            mask = mask.transpose(Image.FLIP_LEFT_RIGHT)
        # img = img.resize((self.img_size[0], self.img_size[1]), Image.BILINEAR)
        # mask = mask.resize((self.img_size[0], self.img_size[1]), Image.NEAREST)
        #TODO
        crop_size_w = self.img_size[0]
        crop_size_h = self.img_size[1]
        # random scale (short edge)
        short_size = random.randint(int(self.basesize * 0.5), int(self.basesize * 2.0))
        w, h = img.size
        if h > w:
            ow = short_size
            oh = int(1.0 * h * ow / w)
        else:
            oh = short_size
            ow = int(1.0 * w * oh / h)
        # img.show()
        # self.show_corlor_mask(mask)
        img = img.resize((ow, oh), Image.BILINEAR)
        mask = mask.resize((ow, oh), Image.NEAREST)
        # pad crop
        if short_size < crop_size_h:
            padh = crop_size_h - oh if oh < crop_size_h else 0
            padw = crop_size_w - ow if ow < crop_size_w else 0
            img = ImageOps.expand(img, border=(0, 0, padw, padh), fill=0)
            mask = ImageOps.expand(mask, border=(0, 0, padw, padh), fill=0)
            # img.show()
            # mask.show()
        # random crop crop_size
        w, h = img.size
        x1 = random.randint(0, w - crop_size_w)
        y1 = random.randint(0, h - crop_size_h)
        img = img.crop((x1, y1, x1 + crop_size_w, y1 + crop_size_h))
        mask = mask.crop((x1, y1, x1 + crop_size_w, y1 + crop_size_h))
        # img.show()
        # self.show_corlor_mask(mask)
        if random.random() < 0.5:
            # shift = random.randint(-10, 10)
            factor = 0.8+0.4*float(random.random())
            img = ImageEnhance.Brightness(img)
            img = img.enhance(factor=factor)
        # gaussian blur as in PSP
        if random.random() < 0.5:
            img = img.filter(ImageFilter.GaussianBlur(radius=random.random()))
        # final transform
        img, mask = self._img_transform(img), self._mask_transform(mask)

        # plt.figure()
        # plt.subplot(1,2,1)
        # plt.imshow(img.copy())

        # x = random.randint(-2, 2)
        # y = random.randint(-2, 2)
        # H = np.float32([[1,0,x],[0,1,y]])
        # inputs1 = cv2.warpAffine(img.copy(), H, (img.shape[1],img.shape[0]))
        # # labels1 = cv2.warpAffine(mask.astype(np.float32),H,(img.shape[1],img.shape[0])).astype(np.uint8) 

        # plt.subplot(1,2,2)
        # plt.imshow(inputs1)
        # plt.savefig("warpAffine.jpg")
        return img, mask

    def _img_transform(self,img):
        return np.array(img)

    def _mask_transform(self, mask):
        ttt = np.unique(np.array(mask))
        mask = self._class_to_index(np.array(mask))

        # tm = torch.from_numpy(mask).long()
        # tmm = torch.nn.functional.one_hot(tm)
        #
        # tm = tmm.cpu().numpy()
        # plt.imshow(tm[:,:,0])
        # plt.show()
        # plt.imshow(tm[:,:,1])
        # plt.show()

        ttts = np.unique(mask)
        # return torch.LongTensor(np.array(mask).astype('int32'))
        return torch.FloatTensor(np.array(mask).astype('int32'))

    def warp_transfrom(self, img, mask, filename, copypaste_ratio):
        if np.random.random() < copypaste_ratio:
            dst_filename = random.choice(self.aug_file_list)

            wrapped_img = Image.open(os.path.join(self.img_path, 'JPEGImages_wrapped', filename + '.jpg'))
            wrapped_mask = Image.open(os.path.join(self.img_path, 'Segmentations_wrapped/mask_png', filename + '.png'))
            dst_img = Image.open(os.path.join(self.img_path, 'JPEGImages_copypaste', dst_filename.strip() + '.jpg'))

            wrapped_img = np.array(wrapped_img)
            wrapped_mask = np.array(wrapped_mask)
            new_img = np.array(dst_img)

            new_img[wrapped_mask == 2] = wrapped_img[wrapped_mask == 2]

            return Image.fromarray(new_img.astype('uint8')).convert('RGB'), \
                   Image.fromarray(wrapped_mask)
        else:
            return img, mask


class ECOSemanticIndoorUnlabeledDataset(BaseDataset):
    def __init__(self, base_size,
                 flip,
                 color_jitter,
                 blur,
                 **kwargs):
        super(ECOSemanticIndoorUnlabeledDataset, self).__init__(**kwargs)
        self.basesize = base_size
        self.flip = flip
        self.color_jitter = color_jitter
        self.blur = blur
        self.key = np.zeros(100, np.uint8)
        self._mapping = np.array(range(0, len(self.key) - 1)).astype('int32')
        self.img_info = self.load_img_info(self.img_path)
        assert len(self.img_info) > 0, "img size 0"

    def load_img_info(self, img_dir):
        if not os.path.exists(img_dir):
            print("warning: mask sure {} is unlabeled image path and exist".format(img_dir))
            return None
        file_list = open(img_dir + '/trainvallist_keliwu_bak/' + self.mode + 'list.txt', 'r').readlines()
        img_info_list = []
        assert len(file_list) > 0
        fileindex = 0
        for file in file_list:
            filename = file.rstrip('.\n').split('.')[0]
            im_path = os.path.join(self.img_path, 'JPEGImages', filename + '.jpg')
            if os.path.exists(im_path):
                img_info = dict(
                    filename=filename,
                    id=fileindex,
                    im_path=im_path
                )
                img_info_list.append(img_info)
            fileindex += 1
        random.shuffle(img_info_list)
        return img_info_list

    def get_train_data(self, idx):
        img = Image.open(self.img_info[idx]['im_path'])

        # TODO: 谷歌复制粘贴数据增广
        # img, mask = self.warp_transfrom(img, mask, self.img_info[idx]['filename'], copypaste_ratio=0.3)

        img = self.sync_transform(img)
        # TODO:add albutmentations数据增广的方案
        # img = CLAHE(clip_limit=4.0, tile_grid_size=(32, 32), p=0.5)(image=img)['image']

        # TODO: Crop image and mask from 360 to 320
        # img = img[:320,:,:]
        # mask = mask[:320,:]

        meta = dict(
            img=img,
            img_info=self.img_info[idx]
        )
        # TODO:如果没有数据增广，请注释下面两行
        # if self.aug is not None and self.mode == 'train':
        #     meta = self.aug(meta)

        # plt.figure()
        # plt.imshow(img)
        # plt.savefig('color.png')

        meta['img'] = ((meta['img'].astype(np.float32) / 255.) - self.mean) / self.std
        meta['img'] = torch.from_numpy(meta['img'].transpose(2, 0, 1))

        return meta

    def sync_transform(self, img):
        # random mirror
        if random.random() < 0.5:
            img = img.transpose(Image.FLIP_LEFT_RIGHT)
        # img = img.resize((self.img_size[0], self.img_size[1]), Image.BILINEAR)
        # mask = mask.resize((self.img_size[0], self.img_size[1]), Image.NEAREST)
        # TODO
        crop_size_w = self.img_size[0]
        crop_size_h = self.img_size[1]
        # random scale (short edge)
        short_size = random.randint(int(self.basesize * 0.5), int(self.basesize * 2.0))
        w, h = img.size
        if h > w:
            ow = short_size
            oh = int(1.0 * h * ow / w)
        else:
            oh = short_size
            ow = int(1.0 * w * oh / h)
        # img.show()
        # self.show_corlor_mask(mask)
        img = img.resize((ow, oh), Image.BILINEAR)
        # pad crop
        if short_size < crop_size_h:
            padh = crop_size_h - oh if oh < crop_size_h else 0
            padw = crop_size_w - ow if ow < crop_size_w else 0
            img = ImageOps.expand(img, border=(0, 0, padw, padh), fill=0)
            # img.show()
            # mask.show()
        # random crop crop_size
        w, h = img.size
        x1 = random.randint(0, w - crop_size_w)
        y1 = random.randint(0, h - crop_size_h)
        img = img.crop((x1, y1, x1 + crop_size_w, y1 + crop_size_h))
        # img.show()
        # self.show_corlor_mask(mask)
        if random.random() < 0.5:
            # shift = random.randint(-10, 10)
            factor = 0.8 + 0.4 * float(random.random())
            img = ImageEnhance.Brightness(img)
            img = img.enhance(factor=factor)
        # gaussian blur as in PSP
        if random.random() < 0.5:
            img = img.filter(ImageFilter.GaussianBlur(radius=random.random()))
        # final transform
        img = self._img_transform(img)

        return img

    def _img_transform(self, img):
        return np.array(img)

    def warp_transfrom(self, img, mask, filename, copypaste_ratio):
        if np.random.random() < copypaste_ratio:
            dst_filename = random.choice(self.aug_file_list)

            wrapped_img = Image.open(os.path.join(self.img_path, 'JPEGImages_wrapped', filename + '.jpg'))
            wrapped_mask = Image.open(os.path.join(self.img_path, 'Segmentations_wrapped/mask_png', filename + '.png'))
            dst_img = Image.open(os.path.join(self.img_path, 'JPEGImages_copypaste', dst_filename.strip() + '.jpg'))

            wrapped_img = np.array(wrapped_img)
            wrapped_mask = np.array(wrapped_mask)
            new_img = np.array(dst_img)

            new_img[wrapped_mask == 2] = wrapped_img[wrapped_mask == 2]

            return Image.fromarray(new_img.astype('uint8')).convert('RGB'), \
                   Image.fromarray(wrapped_mask)
        else:
            return img, mask
