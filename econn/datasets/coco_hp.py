"""
Create by Chengqi.Lv
2020/3/26
"""

from pycocotools.coco import COCO
from .coco import CocoDataset
import warnings
import logging


class COCOHPDataset(CocoDataset):
    class_names = ['person']

    _valid_ids = [1]

    def load_img_info(self, ann_path):
        logging.info('==> initializing {} data.'.format(self.mode))
        self.coco = COCO(ann_path)
        self.cat_ids = self.coco.getCatIds(catIds=self._valid_ids)
        self.cat2label = {
            cat_id: i
            for i, cat_id in enumerate(self.cat_ids)
        }
        if self.mode == 'train' and self.skip_img_without_anno:
            self.img_ids = self.coco.getImgIds(catIds=self._valid_ids)
        else:
            self.img_ids = self.coco.getImgIds()
        logging.info('loaded {} {} annotations from {}'.format(len(self.img_ids), self.mode, ann_path))
        img_info = []
        for i in self.img_ids:
            info = self.coco.loadImgs([i])[0]
            img_info.append(info)
        return img_info
