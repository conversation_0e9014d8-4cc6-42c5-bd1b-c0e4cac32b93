"""
Create by Chengqi.Lv
2020/3/17
"""

from pycocotools.coco import COCO
import torch
import numpy as np
from .coco import CocoDataset
import os
import cv2
import warnings
import logging
from ..augmentations.yolo_mosaic import load_mosaic


class KeliWuDataset(CocoDataset):
    class_names = ['<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON>u<PERSON><PERSON>','<PERSON><PERSON><PERSON>']

    _valid_ids = [
                    1, 2, 3, 4, 5, 6]

