"""
Create by Chengqi.Lv
2020/8/12
"""

from .coco import CocoDataset


class ECOInstanceIndoorDataset(CocoDataset):
    class_names = ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>',
                   '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'LiShiKongTiao', 'PuTongChuangHu',
                   'Putongdimian', 'PuTongMen', '<PERSON><PERSON>', '<PERSON>', 'ShaFa', 'TuiLaMen',
                   '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'ZhuoZ<PERSON>']

    _valid_ids = [
        1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]
