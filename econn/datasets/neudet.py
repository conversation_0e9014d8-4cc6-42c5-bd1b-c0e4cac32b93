"""
Create by Chengqi.Lv
2020/3/17
"""

from pycocotools.coco import COCO
import torch
import numpy as np
from .coco import CocoDataset
import os
import cv2
import warnings
import logging
from ..augmentations.yolo_mosaic import load_mosaic


class NEUDETDataset(CocoDataset):
    class_names = ['crazing','inclusion','patches','pitted_surface','rolled-in_scale','scratches']#['face','body']#['Kou<PERSON>iang','<PERSON>S<PERSON>','<PERSON>ua<PERSON>iKe','<PERSON>uaZiKes','GuWu','DaMi']

    _valid_ids = [
                    1, 2, 3, 4, 5, 6]

