"""
Create by Chengqi.Lv
2020/3/17
"""
import copy
from .coco import CocoDataset
from .coco_hp import COCOHPDataset
from .eco_indoor import ECOindoorDataset
from .eco_ground import ECOgroundDataset
from .eco_instance_indoor import ECOInstanceIndoorDataset
from .eco_semantic_indoor import ECOSemanticIndoorDataset
from .eco_semantic_indoor import ECOSemanticIndoorUnlabeledDataset
from econn.augmentations.builder import Augmentation
from .eco_ground_point import LinePoint
from .dataset_warppers import ClassBalancedDataset


def build_dataset(dataset_cfg, mode='train', encoder=None):
    aug = Augmentation(dataset_cfg)
    if 'augmentation' in dataset_cfg:
        dataset_cfg.pop('augmentation')
    warp_mode = None
    if 'warp_mode' in dataset_cfg:
        warp_mode = dataset_cfg.pop('warp_mode')
    dataset_name = dataset_cfg.pop('dataset_name')

    if dataset_name == 'coco':
        dataset = CocoDataset(aug=aug,
                              mode=mode,
                              encoder=encoder,
                              **dataset_cfg)
    elif dataset_name == 'coco_hp':
        dataset = COCOHPDataset(aug=aug,
                                mode=mode,
                                encoder=encoder,
                                **dataset_cfg)
    elif dataset_name == 'eco_indoor':
        dataset = ECOindoorDataset(aug=aug,
                                   mode=mode,
                                   encoder=encoder,
                                   **dataset_cfg)
    elif dataset_name == 'eco_ground':
        dataset = ECOgroundDataset(aug=aug,
                                   mode=mode,
                                   encoder=encoder,
                                   **dataset_cfg)
    elif dataset_name == 'eco_instance_indoor':
        dataset = ECOInstanceIndoorDataset(aug=aug,
                                           mode=mode,
                                           encoder=encoder,
                                           **dataset_cfg)
    elif dataset_name == 'eco_semantic_indoor':
        dataset = ECOSemanticIndoorDataset(
                                           aug = aug,
                                           mode=mode,
                                           encoder=encoder,
                                           **dataset_cfg
                                           )
    elif dataset_name=="LinePoint":
        dataset = LinePoint(aug=aug,
                              mode=mode,
                              encoder=encoder,
                              **dataset_cfg)
    else:
        raise NotImplementedError

    if warp_mode:
        if warp_mode.name == 'class_balance':
            dataset = ClassBalancedDataset(dataset, warp_mode.oversample_thr, warp_mode.filter_empty_gt)
        else:
            raise NotImplementedError

    return dataset
