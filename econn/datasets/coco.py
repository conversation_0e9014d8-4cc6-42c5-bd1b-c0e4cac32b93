"""
Create by Chengqi.Lv
2020/3/17
"""

from pycocotools.coco import COCO
import torch
import numpy as np
from .base_dataset import BaseDataset
import os
import cv2
import copy
import warnings
import logging
from ..augmentations.yolo_mosaic import load_mosaic


class CocoDataset(BaseDataset):
    class_names = ['person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus',
                        'train', 'truck', 'boat', 'traffic_light', 'fire_hydrant',
                        'stop_sign', 'parking_meter', 'bench', 'bird', 'cat', 'dog',
                        'horse', 'sheep', 'cow', 'elephant', 'bear', 'zebra', 'giraffe',
                        'backpack', 'umbrella', 'handbag', 'tie', 'suitcase', 'frisbee',
                        'skis', 'snowboard', 'sports_ball', 'kite', 'baseball_bat',
                        'baseball_glove', 'skateboard', 'surfboard', 'tennis_racket',
                        'bottle', 'wine_glass', 'cup', 'fork', 'knife', 'spoon', 'bowl',
                        'banana', 'apple', 'sandwich', 'orange', 'broccoli', 'carrot',
                        'hot_dog', 'pizza', 'donut', 'cake', 'chair', 'couch',
                        'potted_plant', 'bed', 'dining_table', 'toilet', 'tv', 'laptop',
                        'mouse', 'remote', 'keyboard', 'cell_phone', 'microwave',
                        'oven', 'toaster', 'sink', 'refrigerator', 'book', 'clock',
                        'vase', 'scissors', 'teddy_bear', 'hair_drier', 'toothbrush']

    _valid_ids = [
                    1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13,
                    14, 15, 16, 17, 18, 19, 20, 21, 22, 23,
                    24, 25, 27, 28, 31, 32, 33, 34, 35, 36,
                    37, 38, 39, 40, 41, 42, 43, 44, 46, 47,
                    48, 49, 50, 51, 52, 53, 54, 55, 56, 57,
                    58, 59, 60, 61, 62, 63, 64, 65, 67, 70,
                    72, 73, 74, 75, 76, 77, 78, 79, 80, 81,
                    82, 84, 85, 86, 87, 88, 89, 90]

    def __init__(self,
                 use_mosaic=False,
                 **kwargs):
        self.use_mosaic = use_mosaic
        super(CocoDataset, self).__init__(**kwargs)

    def load_img_info(self, ann_path):
        logging.info('==> initializing {} data.'.format(self.mode))
        self.coco = COCO(ann_path)
        self.cat_ids = self.coco.getCatIds(catIds=self._valid_ids)
        self.cat2label = {
            cat_id: i
            for i, cat_id in enumerate(self.cat_ids)
        }
        self.img_ids = self.coco.getImgIds()
        logging.info('loaded {} {} annotations from {}'.format(len(self.img_ids), self.mode, ann_path))
        img_info = []
        for i in self.img_ids:
            info = self.coco.loadImgs([i])[0]
            img_info.append(info)
        return img_info

    def get_cat_ids(self, idx):
        img_id = self.img_info[idx]['id']
        ann_ids = self.coco.getAnnIds(imgIds=[img_id])
        anns = self.coco.loadAnns(ann_ids)
        return [ann['category_id'] for ann in anns]

    def get_per_img_ann(self, idx):
        img_id = self.img_info[idx]['id']
        ann_ids = self.coco.getAnnIds(imgIds=[img_id])
        anns = self.coco.loadAnns(ann_ids)

        gt_bboxes = []
        gt_labels = []
        gt_bboxes_ignore = []
        # Two formats are provided.
        # 1. mask: a binary map of the same size of the image.
        # 2. polys: each mask consists of one or several polys, each poly is a
        # list of float.
        if self.with_mask:
            gt_masks = []
            # gt_mask_polys = []
            # gt_poly_lens = []
        if self.with_keypoints:
            gt_keypoints = []
        for i, ann in enumerate(anns):
            if ann.get('ignore', False):
                continue
            x1, y1, w, h = ann['bbox']
            if ann['area'] <= 0 or w < 1 or h < 1:
                continue
            bbox = [x1, y1, x1 + w, y1 + h]
            if ann['iscrowd']:
                gt_bboxes_ignore.append(bbox)
            else:
                gt_bboxes.append(bbox)
                gt_labels.append(self.cat2label[ann['category_id']])
                if self.with_mask:
                    gt_masks.append(self.coco.annToMask(ann))
                    # mask_polys = [
                    #     p for p in ann['segmentation'] if len(p) >= 6
                    # ]  # valid polygons have >= 3 points (6 coordinates)
                    # poly_lens = [len(p) for p in mask_polys]
                    # gt_mask_polys.append(mask_polys)
                    # gt_poly_lens.extend(poly_lens)
                if self.with_keypoints:
                    gt_keypoints.append(ann['keypoints'])
        if gt_bboxes:
            gt_bboxes = np.array(gt_bboxes, dtype=np.float32)
            gt_labels = np.array(gt_labels, dtype=np.int64)
        else:
            gt_bboxes = np.zeros((0, 4), dtype=np.float32)
            gt_labels = np.array([], dtype=np.int64)

        if gt_bboxes_ignore:
            gt_bboxes_ignore = np.array(gt_bboxes_ignore, dtype=np.float32)
        else:
            gt_bboxes_ignore = np.zeros((0, 4), dtype=np.float32)

        ann = dict(
            bboxes=gt_bboxes, labels=gt_labels, bboxes_ignore=gt_bboxes_ignore)

        if self.with_mask:
            ann['masks'] = gt_masks
            # poly format is not used in the current implementation
            # ann['mask_polys'] = gt_mask_polys
            # ann['poly_lens'] = gt_poly_lens
        if self.with_keypoints:
            if gt_keypoints:
                ann['keypoints'] = np.array(gt_keypoints, dtype=np.float32)
            else:
                ann['keypoints'] = np.zeros((0, 51), dtype=np.float32)
        return ann

    def get_train_data(self, idx):
        img_path = os.path.join(self.img_path, self.img_info[idx]['file_name'])
        # print(img_path)

        if self.use_mosaic and self.mode == 'train':
            img, ann = load_mosaic(self, idx)
        else:
            img = cv2.imread(img_path)
            ann = self.get_per_img_ann(idx)

        # skip the image if there is no valid gt bbox
        if len(ann['bboxes']) == 0 and self.skip_img_without_anno:
            warnings.warn('Skip the image {} that has no gt bbox'.format(img_path))
            return None

        meta = dict(img=img,
                    gt_bboxes=ann['bboxes'],
                    gt_labels=ann['labels'])
        if self.with_mask:
            meta['gt_masks'] = ann['masks']
        if self.with_keypoints:
            meta['gt_keypoints'] =ann['keypoints']
        if self.aug is not None and not self.use_mosaic:
            meta = self.aug(meta)
        # #-------Debug-----------------
        # show_img = meta['img'].copy()
        # for bbox in meta['gt_bboxes']:
        #     cv2.rectangle(show_img, (int(bbox[0]),int(bbox[1])),(int(bbox[2]),int(bbox[3])),(0,255,0))
        # cv2.imshow('raw', show_img)
        # cv2.waitKey(0)
        meta['inp_img'] = copy.deepcopy(meta['img'])
        meta['img'] = ((meta['img'].astype(np.float32) / 255.) - self.mean) / self.std
        meta['img'] = torch.from_numpy(meta['img'].transpose(2, 0, 1))
        meta['img_info'] = self.img_info[idx]

        if self.encoder is not None:
            meta = self.encoder(meta)
        if self.with_mask:
            meta['gt_masks'] = np.array(meta['gt_masks'])
        return meta

    def get_test_data(self, idx):
        return self.get_train_data(idx)
