import math
import torch.optim.lr_scheduler as lr_scheduler


def one_cycle(y1=1.0, y2=0.2, steps=100):
    # lambda function for sinusoidal ramp from y1 to y2
    return lambda x: ((1 - math.cos(x * math.pi / steps)) / 2) * (y2 - y1) + y1

def multi_step(factor=None,milestones=[80,110]):
    if factor == None:
        factor = 0.1
    return lambda x: factor ** (milestones.index(x) + 1) if x in milestones else 1.

# def Poly(power, milestones):

class Scheduler:
    def __init__(self,cfg):
        self.name = cfg.train.lr_schedule.name
        if self.name == 'multi_step':
            self.milestones = cfg.train.lr_schedule.milestones
            self.lambda_lr = multi_step(cfg.train.lr_schedule.get('factor'), cfg.train.lr_schedule.milestones)
        elif self.name == 'one_cycle':
            self.max_factor = cfg.train.lr_schedule.max_factor
            self.min_factor = cfg.train.lr_schedule.min_factor
            self.lambda_lr = one_cycle(self.max_factor,
                                       self.min_factor,
                                       cfg.train.num_epochs)
        else:
            raise NotImplementedError

    def __call__(self, epoch):
        decay_factor = self.lambda_lr(epoch)

        return decay_factor

