"""
Create by Chengqi.Lv
2020/3/18
"""

from functools import partial
from .augmentations import random_flip, resize_and_ioa_random_crop, resize
from .iaa_augmentations import ColorAug
import copy


def build(func, cfg):
    # TODO: 修改成通用的BUILDER
    if isinstance(cfg, dict):
        return partial(func, **cfg)
    elif cfg:
        return func
    else:
        raise AssertionError


class Augmentation(object):
    def __init__(self, config):
        self.aug_list = []

        if 'augmentation' in config:
            if 'random_flip' in config.augmentation:
                self.aug_list.append(partial(random_flip,
                                             with_mask=config.with_mask,
                                             with_keypoints=config.with_keypoints,
                                             **config.augmentation.random_flip))
            if 'resize' in config.augmentation:
                self.aug_list.append(partial(resize,
                                             shape=config.input_size,
                                             with_mask=config.with_mask,
                                             with_keypoints=config.with_keypoints,
                                             resize_keep_ratio=config.resize_keep_ratio))
            if 'ioa_random_crop' in config.augmentation:
                if config.augmentation.ioa_random_crop:
                    self.aug_list.append(partial(resize_and_ioa_random_crop,
                                                 shape=config.input_size,
                                                 with_mask=config.with_mask,
                                                 with_keypoints=config.with_keypoints,
                                                 resize_keep_ratio=config.resize_keep_ratio,
                                                 **config.augmentation.ioa_random_crop))
                else:
                    self.aug_list.append(partial(resize_and_ioa_random_crop,
                                                 shape=config.input_size,
                                                 with_mask=config.with_mask,
                                                 with_keypoints=config.with_keypoints,
                                                 resize_keep_ratio=config.resize_keep_ratio))
            if 'color' in config.augmentation:
                if config.augmentation.color is not None:
                    self.aug_list.append(ColorAug(config.augmentation.color))
            # TODO: 颜色数据增强，考虑用DALI实现？

        if len(self.aug_list) == 0:
            self.aug_list.append(partial(resize,
                                         shape=config.input_size,
                                         with_mask=config.with_mask,
                                         with_keypoints=config.with_keypoints,
                                         resize_keep_ratio=config.resize_keep_ratio))

    def __call__(self, meta):
        aug_meta = meta
        for aug in self.aug_list:
            aug_meta = aug(aug_meta)
        return aug_meta
