"""
Create by Chengqi.Lv
2020/4/7
"""

from imgaug import augmenters as iaa
from functools import partial

sometimes = lambda aug: iaa.Sometimes(0.5, aug)

func_dict = {
    'brightness': iaa.MultiplyBrightness,
    'saturation': iaa.MultiplySaturation,
    'color_temperature': iaa.ChangeColorTemperature,
    'noise': partial(iaa.MultiplyElementwise, per_channel=0.5),
    'color_shift': partial(iaa.Multiply, per_channel=0.5),
}


class ColorAug:
    def __init__(self, color_cfg):
        seq = []
        for k, v in color_cfg.items():
            assert isinstance(v, list)  # color augmentation setting should be a list in the YAML file.
            seq.append(sometimes(func_dict[k](v)))
        self.sequence = iaa.Sequential(seq)

    def __call__(self, meta):
        meta['img'] = self.sequence.augment_image(meta['img'])
        return meta
