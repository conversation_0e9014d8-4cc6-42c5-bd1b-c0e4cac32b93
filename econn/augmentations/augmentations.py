"""
Create by <PERSON>qi.Lv
2020/3/18
"""
import copy, random
import numpy as np
from econn.utils.transforms import bbox_flip, keypoints_flip, get_affine_transform, affine_transform
import cv2


def _get_border(border, size):
    i = 1
    while size - border // i <= border // i:
        i *= 2
    return border // i


def _random_center(img, s, c, random_stretch):
    ss = copy.deepcopy(s)
    cc = copy.deepcopy(c)
    if random_stretch:
        i = np.random.choice([0, 1])
        ss[i] = ss[i] * np.random.choice(np.arange(0.6, 1.4, 0.05))

    w_border = _get_border(128, img.shape[1])
    h_border = _get_border(128, img.shape[0])
    cc[0] = np.random.randint(low=w_border, high=img.shape[1] - w_border)
    cc[1] = np.random.randint(low=h_border, high=img.shape[0] - h_border)

    ss = ss * np.random.choice(np.arange(0.6, 1.4, 0.1))

    return ss, cc


def _random_crop_ioa(img,
                     s,
                     c,
                     input_w,
                     input_h,
                     origin_bboxes,
                     random_stretch=False,
                     resize_keep_ratio=True,
                     ioa_thres=0.7):
    times = 0
    while True:
        # randomly choose a mode
        mode = (ioa_thres, None)
        if times > 20:
            ss, cc = _random_center(img, s, c, random_stretch)
            return ss, cc
            # print('cannot crop')

        min_ioa, max_ioa = mode
        if min_ioa is None:
            min_ioa = float('-inf')
        for _ in range(20):
            bboxes = copy.deepcopy(origin_bboxes)
            ss, cc = _random_center(img, s, c, random_stretch)
            # ioa
            trans_input = get_affine_transform(
                cc, ss, 0, [input_w, input_h], resize_keep_ratio=resize_keep_ratio)
            ioas = []
            for bbox in bboxes:
                bbox[:2] = affine_transform(bbox[:2], trans_input)  # 把bbox两个点转换到输出大小的坐标
                bbox[2:] = affine_transform(bbox[2:], trans_input)
                ori_h, ori_w = bbox[3] - bbox[1], bbox[2] - bbox[0]
                bbox[[0, 2]] = np.clip(bbox[[0, 2]], 0, input_w - 1)  # 裁剪，把值限制在0～output_w - 1之间，防止越界
                bbox[[1, 3]] = np.clip(bbox[[1, 3]], 0, input_h - 1)
                cut_h, cut_w = bbox[3] - bbox[1], bbox[2] - bbox[0]  # 输出大小下的高宽
                ioa = (cut_h * cut_w) / (ori_h * ori_w)
                ioas.append(ioa)
            if len(ioas) == 0:
                continue
            if min(ioas) > min_ioa:  # 必须全部满足
                return ss, cc
            # for iioa in ioas:  # 只要有一个满足就行
            #     if iioa > min_ioa:
            #         return ss, cc
            else:
                # print('ioa={} failed'.format(min_ioa))
                continue
        times += 1


def resize_and_ioa_random_crop(meta, shape, with_mask, with_keypoints, random_stretch=False, resize_keep_ratio=True):
    img = meta['img']
    bboxes = meta['gt_bboxes']
    labels = meta['gt_labels']
    if with_mask:
        masks = meta['gt_masks']
    if with_keypoints:
        keypoints = meta['gt_keypoints']
    height, width = img.shape[0], img.shape[1]
    c = np.array([img.shape[1] / 2., img.shape[0] / 2.], dtype=np.float32)
    s = np.array([img.shape[1], img.shape[0]], dtype=np.float32)
    input_w, input_h = shape
    ss, cc = _random_crop_ioa(img, s, c, input_w, input_h, bboxes, random_stretch, resize_keep_ratio)
    trans_affine = get_affine_transform(
        cc, ss, 0, [input_w, input_h], resize_keep_ratio=resize_keep_ratio)

    trans_img = cv2.warpAffine(img, trans_affine, (input_w, input_h), flags=cv2.INTER_LINEAR)
    for bbox in bboxes:  # TODO: 优化仿射变换速度
        bbox[:2] = affine_transform(bbox[:2], trans_affine)
        bbox[2:] = affine_transform(bbox[2:], trans_affine)
        bbox[[0, 2]] = np.clip(bbox[[0, 2]], 0, input_w - 1)
        bbox[[1, 3]] = np.clip(bbox[[1, 3]], 0, input_h - 1)
    trans_meta = dict(img=trans_img,
                      gt_bboxes=bboxes,
                      gt_labels=labels)

    if with_mask:
        trans_masks = []
        for mask in masks:
            trans_mask = cv2.warpAffine(mask, trans_affine, (input_w, input_h), flags=cv2.INTER_LINEAR)
            trans_masks.append(trans_mask)
        trans_meta['gt_masks'] = trans_masks
    if with_keypoints:
        for keypoint in keypoints:
            keypoint.shape = 17, 3
            for j in range(17):
                if keypoint[j, 2] > 0:
                    keypoint[j, :2] = affine_transform(keypoint[j, :2], trans_affine)
            keypoint[:, 0] = np.clip(keypoint[:, 0], 0, input_w - 1)
            keypoint[:, 1] = np.clip(keypoint[:, 1], 0, input_h - 1)
            keypoint.shape = 51, 1
        trans_meta['gt_keypoints'] = keypoints

    if "lines_points" in meta.keys():
        lines_points=meta["lines_points"]
        lines_points=np.array([affine_transform(point, trans_affine) for point in lines_points])
        lines_points=np.delete(lines_points,np.where(lines_points[:,0]<0),axis=0)
        lines_points=np.delete(lines_points,np.where(lines_points[:,0]>input_w-1),axis=0)
        lines_points=np.delete(lines_points,np.where(lines_points[:,1]<0),axis=0)
        lines_points=np.delete(lines_points,np.where(lines_points[:,1]>input_h-1),axis=0)

        # lines_points[:,0]=np.clip(lines_points[:,0],0,input_w-1)
        # lines_points[:,1]=np.clip(lines_points[:,1],0,input_h - 1)

        
        trans_meta["lines_points"]=lines_points
        # for point in trans_meta["lines_points"]:
	    #     cv2.circle(trans_img, tuple(point), 1, (0, 0, 255), 4)
        # cv2.imwrite("debug.png", trans_img)
    return trans_meta


def random_flip(meta, with_mask, with_keypoints, flip_ratio=0.5):
    if np.random.random() < flip_ratio:
        img = meta['img']
        height, width = img.shape[0], img.shape[1]
        bboxes = meta['gt_bboxes']
        labels = meta['gt_labels']
        img = img[:, ::-1, :]
        bboxes = bbox_flip(bboxes, img_shape=(height, width))
        flip_meta = dict(img=img,
                         gt_bboxes=bboxes,
                         gt_labels=labels)
        flip_masks = []
        if with_mask:
            masks = meta['gt_masks']
            for mask in masks:
                flip_mask = mask[:, ::-1]
                flip_masks.append(flip_mask)
            flip_meta['gt_masks'] = flip_masks
        if with_keypoints:
            keypoints = meta['gt_keypoints']
            keypoints = keypoints_flip(keypoints, img_shape=(height, width))
            flip_meta['gt_keypoints'] = keypoints

        if "lines_points" in meta.keys():
            lines_points=np.array(meta["lines_points"])
            lines_points[:,0]=width-lines_points[:,0]-1
            #meta["lines_points"]=lines_points
            flip_meta["lines_points"]=lines_points
            ##-----------------debug------------
            # for point in lines_points:
            #     cv2.circle(img, tuple(point), 1, (0, 0, 255), 4)
            # cv2.imwrite("debug.png", img)

        return flip_meta
    else:
        return meta


def resize(meta, shape, with_mask, with_keypoints, resize_keep_ratio=True):
    img = meta['img']
    bboxes = meta['gt_bboxes']
    labels = meta['gt_labels']
    if with_mask:
        masks = meta['gt_masks']
    if with_keypoints:
        keypoints = meta['gt_keypoints']
    height, width = img.shape[0], img.shape[1]
    c = np.array([img.shape[1] / 2., img.shape[0] / 2.], dtype=np.float32)
    s = np.array([img.shape[1], img.shape[0]], dtype=np.float32)
    input_w, input_h = shape
    trans_affine = get_affine_transform(
        c, s, 0, [input_w, input_h], resize_keep_ratio=resize_keep_ratio)

    trans_img = cv2.warpAffine(img, trans_affine, (input_w, input_h), flags=cv2.INTER_LINEAR)
    for bbox in bboxes:  # TODO: 优化仿射变换速度
        bbox[:2] = affine_transform(bbox[:2], trans_affine)
        bbox[2:] = affine_transform(bbox[2:], trans_affine)
        bbox[[0, 2]] = np.clip(bbox[[0, 2]], 0, input_w - 1)
        bbox[[1, 3]] = np.clip(bbox[[1, 3]], 0, input_h - 1)
    trans_meta = dict(img=trans_img,
                      gt_bboxes=bboxes,
                      gt_labels=labels)

    if with_mask:
        trans_masks = []
        for mask in masks:
            trans_mask = cv2.warpAffine(mask, trans_affine, (input_w, input_h), flags=cv2.INTER_LINEAR)
            trans_masks.append(trans_mask)
        trans_meta['gt_masks'] = trans_masks
    if with_keypoints:
        for keypoint in keypoints:
            keypoint.shape = 17, 3
            for j in range(17):
                if keypoint[j, 2] > 0:
                    keypoint[j, :2] = affine_transform(keypoint[j, :2], trans_affine)
            keypoint[:, 0] = np.clip(keypoint[:, 0], 0, input_w - 1)
            keypoint[:, 1] = np.clip(keypoint[:, 1], 0, input_h - 1)
            keypoint.shape = 51, 1
        trans_meta['gt_keypoints'] = keypoints
    
    if "lines_points" in meta.keys():
        lines_points=meta["lines_points"]
        #
        #lines_points=np.array[lines_points]
        trans_meta["lines_points"]=np.array([affine_transform(point, trans_affine) for point in lines_points])
        assert trans_meta["lines_points"][:,0].max()<input_w and trans_meta["lines_points"][:,1].max()<input_h
        # for point in trans_meta["lines_points"]:
	    #     cv2.circle(trans_img, tuple(point), 1, (0, 0, 255), 4)
        # cv2.imwrite("debug.png", trans_img)

    return trans_meta
