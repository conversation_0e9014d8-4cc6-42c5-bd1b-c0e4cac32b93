"""
create by yang<PERSON>odong
date:11/13/20
"""
from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

import time
import logging
import shutil
import os
import torch
import torch.nn as nn
from econn.optimizer.apollo import Apollo
from econn.optimizer.adabelief import <PERSON><PERSON><PERSON>ef
from econn.utils.data_parallel import DataParallel, _DataParallel
from econn.utils.logger.average import AverageMeter, MovingAverage
from econn.utils.class_mix import class_mix
from .base_seg_trainer import BaseTrainer, ModleWithLoss
from econn.models.architecture.builder import build_model
import random
import numpy as np

from econn.models.loss.unlabeled_loss import CrossEntropy2d
from econn.models.loss.unlabeled_loss import CrossEntropyLoss2dPixelWiseWeighted
from econn.models.loss.unlabeled_loss import MSELoss2d


class UnlabeledLoss(torch.nn.Module):
    def __init__(self, consistency_weight,
                 consistency_loss='CE',
                 pixel_weight='threshold_uniform',
                 ignore_label=None):
        super(UnlabeledLoss, self).__init__()
        self.consistency_weight = consistency_weight
        self.consistency_loss = consistency_loss
        self.pixel_weight = pixel_weight
        gpus = 1
        if consistency_loss == 'CE':
            if len(gpus) > 1:
                self.unlabeled_loss = torch.nn.DataParallel(CrossEntropyLoss2dPixelWiseWeighted(ignore_index=ignore_label),
                                                       device_ids=gpus).cuda()
            else:
                self.unlabeled_loss = CrossEntropyLoss2dPixelWiseWeighted().cuda()
        elif consistency_loss == 'MSE':
            if len(gpus) > 1:
                self.unlabeled_loss = torch.nn.DataParallel(MSELoss2d(), device_ids=gpus).cuda()
            else:
                self.unlabeled_loss = MSELoss2d().cuda()

    def forward(self, preds, target):
        loss_states = {}
        max_probs, pseudo_label = torch.max(preds, dim=1)

        if self.pixel_weight == "threshold_uniform":
            unlabeled_weight = torch.sum(max_probs.ge(0.968).long() == 1).item() / np.size(np.array(pseudo_label.cpu()))
            pixelWiseWeight = unlabeled_weight * torch.ones(max_probs.shape).cuda()
        elif pixel_weight == "threshold":
            pixelWiseWeight = max_probs.ge(0.968).long().cuda()
        # elif self.pixel_weight == 'sigmoid':
        #     max_iter = 10000
        #     pixelWiseWeight = sigmoid_ramp_up(i_iter, max_iter) * torch.ones(max_probs.shape).cuda()
        elif self.pixel_weight == False:
            pixelWiseWeight = torch.ones(max_probs.shape).cuda()

        if self.consistency_loss == 'CE':
            unlabeled_loss = self.consistency_weight * self.unlabeled_loss(preds, pseudo_label, pixelWiseWeight)
        elif self.consistency_loss == 'MSE':
            unlabeled_weight = torch.sum(max_probs.ge(0.968).long() == 1).item() / np.size(np.array(pseudo_label.cpu()))
            unlabeled_loss = self.consistency_weight * unlabeled_weight * self.unlabeled_loss(preds, target)

        return unlabeled_loss


class SegTrainer(BaseTrainer):
    def __init__(self, cfg,
                 model,
                 evaluator,
                 logger,
                 rank=-1):
        super(SegTrainer, self).__init__(cfg,model,
                 evaluator,
                 logger,
                 rank)#TODO
        self.init_lr = self.cfg.train.optimizer.lr
        self.lr_schedule = self.cfg.train.lr_schedule
        self.use_amp = self.cfg.train.use_amp

    def update_lr(self, new_lr):
        self.optimizer.param_groups[0]['lr'] = new_lr
        if os.path.exists(self.cfg.model.backbone.load_from):
            for i in range(1, len(self.optimizer.param_groups)):
                self.optimizer.param_groups[i]['lr'] = new_lr * 10

    def build_optimizer(self):
        optimizer_cfg = self.cfg.train.optimizer
        param_list = list()
        if os.path.exists(self.cfg.model.backbone.load_from):
            for module in optimizer_cfg.lr_separate:
                param_list.append(dict(params = getattr(self.model_with_loss.model, module).parameters(), lr = optimizer_cfg.lr_separate[module]))
        else:
            param_list.append(dict(params = self.model_with_loss.parameters(), lr = optimizer_cfg.lr))

        assert len(param_list) > 0, "model parameters length should > 0!"

        if optimizer_cfg.name == 'SGD':
            self.optimizer = torch.optim.SGD(param_list, optimizer_cfg.lr, optimizer_cfg.momentum)
        elif optimizer_cfg.name == 'Adam':
            self.optimizer = torch.optim.Adam(param_list, optimizer_cfg.lr)
        elif optimizer_cfg.name == 'AMSGrad':
            self.optimizer = torch.optim.Adam(param_list, optimizer_cfg.lr, amsgrad=True)
        elif optimizer_cfg.name == 'Apollo':
            self.optimizer = Apollo(param_list, optimizer_cfg.lr, warmup=0)
        elif optimizer_cfg.name == 'AdaBelief':
            self.optimizer = AdaBelief(param_list, optimizer_cfg.lr, optimizer_cfg.betas, optimizer_cfg.eps)
        else:
            raise NotImplementedError

    def get_warmup_lr(self, cur_iters):
        if self.cfg.train.warmup.name == 'constant':
            warmup_lr = self.cfg.train.optimizer.lr * self.cfg.train.warmup.ratio
        elif self.cfg.train.warmup.name == 'linear':
            k = (1 - cur_iters / self.cfg.train.warmup.steps) * (1 - self.cfg.train.warmup.ratio)
            warmup_lr = self.cfg.train.optimizer.lr * (1 - k)
        elif self.cfg.train.warmup.name == 'exp':
            k = self.cfg.train.warmup.ratio ** (1 - cur_iters / self.cfg.train.warmup.steps)
            warmup_lr = self.cfg.train.optimizer.lr * k
        else:
            raise Exception('Unsupported warm up type!')
        return warmup_lr

    def warm_up(self, data_loader):
        model = self.model_with_loss
        model.train()
        moving_avg_loss_stats = {}
        num_iters = self.cfg.train.warmup.steps
        cur_iter = 0
        while cur_iter < num_iters:
            for iter_id, batch in enumerate(data_loader):
                cur_iter += 1
                if cur_iter >= num_iters:
                    break

                lr = self.get_warmup_lr(cur_iter)

                self.update_lr(lr)

                for k in batch:
                    if k == 'img':
                        batch[k] = batch[k].to(device=torch.device('cuda'), non_blocking=True)

                output, loss, loss_stats = self.run_one_batch(model, batch)

                for k in loss_stats:
                    if k not in moving_avg_loss_stats:
                        moving_avg_loss_stats[k] = MovingAverage(loss_stats[k].mean().item(),
                                                                 window_size=self.cfg.log.interval)
                    else:
                        moving_avg_loss_stats[k].push(loss_stats[k].mean().item())

                if iter_id % self.cfg.log.interval == 0:
                    log_str = '{}-{}||({}/{})steps:| '.format(self.cfg.model.task_head.name, 'warmup',
                                                              cur_iter, num_iters)
                    for l in moving_avg_loss_stats:
                        log_str += '{}: {:.4f}| '.format(l, moving_avg_loss_stats[l].avg())

                    logging.info(log_str)
                del output, loss, loss_stats

    def run_epoch(self, phase, epoch, data_loader):
        model = self.model_with_loss
        if phase == 'train':
            model.train()
            
        else:
            if len(self.cfg.train.gpu_ids) > 1:
                model = self.model_with_loss.module
            model.eval()
            torch.cuda.empty_cache()
        results = {}
        avg_loss_stats = {}
        moving_avg_loss_stats = {}

        # 使用分布式的sampler，dataset shuffle是根据epoch生成随机种子的，必须在每个epoch前手动设置epoch号
        if phase == 'train' and self.rank > -1:
            logging.info("distributed sampler set epoch at {}".format(epoch))
            data_loader.sampler.set_epoch(epoch)

        num_iters = len(data_loader)  # if opt.num_iters < 0 else opt.num_iters
        for iter_id, batch in enumerate(data_loader):
            if iter_id >= num_iters:
                break

            for k in batch:
                if k == 'img':
                    batch[k] = batch[k].to(device=torch.device('cuda'), non_blocking=True)

            output, loss, loss_stats = self.run_one_batch(model, batch, phase=phase)
            
            if phase == 'val':
                dets = model.model.task_head.decode(output, batch, self.cfg.data.val.resize_keep_ratio)

                for i in range(len(dets[0])):
                    results[batch['img_info']['id'].cpu().numpy()[i]] = dets[0][i]  # TODO: 实例分割的json格式？

                self.evaluator.update(dets[1],batch['gt_masks'].byte().cpu().numpy())


            for k in loss_stats:
                if k not in avg_loss_stats:
                    avg_loss_stats[k] = AverageMeter(loss_stats[k].mean().item())
                    moving_avg_loss_stats[k] = MovingAverage(loss_stats[k].mean().item(),
                                                             window_size=self.cfg.log.interval)
                else:
                    avg_loss_stats[k].update(loss_stats[k].mean().item())
                    moving_avg_loss_stats[k].push(loss_stats[k].mean().item())

            if iter_id % self.cfg.log.interval == 0:
                log_str = '{}-{}|{}/{} epochs|({}/{}){}steps:| '.format(self.cfg.model.task_head.name, phase,
                                                                        epoch, self.cfg.train.num_epochs,
                                                                        iter_id, num_iters, self._iter)
                for l in avg_loss_stats:
                    log_str += '{}: {:.4f}| '.format(l, moving_avg_loss_stats[l].avg())
                    if phase == 'train' and self.rank < 1:
                        self.logger.scalar_summary('Train_loss_by_step/{}'.format(l), phase,
                                                   moving_avg_loss_stats[l].avg(), self._iter)
                logging.info(log_str)
            if phase == 'train':
                # TODO: this change 
                total_iters = num_iters * self.cfg.train.num_epochs #train.batchsize
                assert  self._iter <= total_iters , 'wrong iters'
                lr = self.init_lr * ((1 - float(self._iter) / total_iters) ** self.lr_schedule.power)
                # logging.info('Drop LR to {}'.format(lr))
                self.update_lr(new_lr= lr)
                self._iter += 1

            del output, loss, loss_stats
        epoch_loss_dict = {k: v.avg for k, v in avg_loss_stats.items()}
        return results, epoch_loss_dict

    def run(self, train_loader, val_loader):
        start_epoch = self.epoch
        best_mAP = -10

        # TODO: add test mode
        # with torch.no_grad():
        #     results, val_loss_dict = self.val(self.epoch, val_loader)
        #     self.evaluator.run_eval(results, self.cfg.save_dir, 1)

        if self.cfg.train.warmup.steps > 0 and start_epoch == 1:
            logging.info('Start warming up...')
            self.warm_up(train_loader)

            self.update_lr(new_lr= self.cfg.train.optimizer.lr)

        for epoch in range(start_epoch, self.cfg.train.num_epochs + 1):
            #######################################################################################
            results, train_loss_dict = self.train(epoch, train_loader)#############################
            #######################################################################################
            # if self.rank < 1:
            #     self.save_model(os.path.join(self.cfg.save_dir, 'model_last.pth'), epoch, with_optimizer=True)
            #     for k, v in train_loss_dict.items():
            #         self.logger.scalar_summary('Train_loss_by_epoch/{}'.format(k), 'train', v, epoch)

            if self.cfg.train.val_intervals > 0:# and epoch % self.cfg.train.val_intervals == 0:
                with torch.no_grad():
                    results, val_loss_dict = self.val(self.epoch, val_loader)
                if self.rank < 1:
                    for k, v in val_loss_dict.items():
                        self.logger.scalar_summary('Train_loss_by_epoch/{}'.format(k), 'val', v, epoch)
                mAP = self.evaluator.run_eval(results, self.cfg.save_dir, epoch, rank=self.rank)
                if mAP > best_mAP and self.rank < 1:
                    best_mAP = mAP
                    best_save_path = os.path.join(self.cfg.save_dir, 'model_best_score')
                    if not os.path.exists(best_save_path):
                        os.makedirs(best_save_path)
                    self.save_model(os.path.join(best_save_path, 'model_best_score.pth'), epoch)
                    best_cocomap_txt_path = os.path.join(best_save_path, "best_map.txt")
                    shutil.copyfile(os.path.join(self.cfg.save_dir, 'eval_results.txt'), best_cocomap_txt_path)
                    shutil.copy(os.path.join(self.cfg.save_dir, 'results{}.json'.format(self.rank)), best_save_path)
                    with open(best_cocomap_txt_path, "a") as f:
                        f.write("\nEpoch:{}".format(epoch))
            
            # # TODO: the following is milestone change of lr
            # if epoch in self.cfg.train.lr_schedule.milestones:
            #     if self.rank < 1:
            #         self.save_model(os.path.join(self.cfg.save_dir, 'model_{}.pth'.format(epoch)), epoch,
            #                         with_optimizer=True)
            #     lr = self.cfg.train.optimizer.lr * (0.1 ** (self.cfg.train.lr_schedule.milestones.index(epoch) + 1))
            #     logging.info('Drop LR to {}'.format(lr))
            #     for param_group in self.optimizer.param_groups:
            #         param_group['lr'] = lr

            self.epoch += 1


class UnlabelLoss(nn.Module):
    def __init__(self, unlabel_loss_cfg):
        super(UnlabelLoss, self).__init__()
        self.consistency_loss = unlabel_loss_cfg.consistency_loss
        self.pixel_weight = unlabel_loss_cfg.pixel_weight
        self.consistency_weight = unlabel_loss_cfg.consistency_weight
        
        if self.consistency_loss == 'CE':
            self.unlabel_loss = CrossEntropyLoss2dPixelWiseWeighted().cuda()
        elif self.consistency_loss == 'MSE':
            self.unlabel_loss = MSELoss2d().cuda()

    def forward(self, pred_mixed, mask_mixed, max_probs, pseudo_label):
        if  self.pixel_weight == "threshold_uniform":
            unlabeled_weight = torch.sum(max_probs.ge(0.968).long() == 1).item() / np.size(
                np.array(pseudo_label.cpu()))
            pixelWiseWeight = unlabeled_weight * torch.ones(max_probs.shape).cuda()
        elif  self.pixel_weight == "threshold":
            pixelWiseWeight = max_probs.ge(0.968).long().cuda()
        elif  self.pixel_weight == False:
            pixelWiseWeight = torch.ones(max_probs.shape).cuda()

        if self.consistency_loss == 'CE':
            # images_remain 输入到mea_model, 根据预测结果，对images_remain与预测结果进行mix，
            # 得到images_remain_mixed与mask pseudo_label
            # images_remain_mixed再输入给model预测得到logits_u_s
            # 所以unlabeled_loss的设计目的是为了最小化ema_model与model的预测.
            unlabel_loss = self.consistency_weight * self.unlabel_loss(pred_mixed, pseudo_label, pixelWiseWeight)
            # plt.imshow(pseudo_label[0].squeeze().detach().cpu())
            # plt.show()
        elif self.consistency_loss == 'MSE':
            unlabeled_weight = torch.sum(max_probs.ge(0.968).long() == 1).item() / np.size(
                np.array(pseudo_label.cpu()))
            # softmax_u_w_mixed = torch.cat((softmax_u_w_mixed[1].unsqueeze(0),softmax_u_w_mixed[0].unsqueeze(0)))
            unlabel_loss = self.consistency_weight * unlabeled_weight * self.unlabel_loss(pred_mixed, mask_mixed)  
        return unlabel_loss


class SegSelfTrainer(BaseTrainer):
    def __init__(self, cfg,
                 model,
                 ema_model,
                 evaluator,
                 logger,
                 rank=-1):
        super(SegSelfTrainer, self).__init__(cfg, model,
                                         evaluator,
                                         logger,
                                         rank)  # TODO
        self.init_lr = self.cfg.train.optimizer.lr
        self.lr_schedule = self.cfg.train.lr_schedule
        self.use_amp = self.cfg.train.use_amp
        self.ema_model = ema_model
        self.unlabel_loss = UnlabelLoss(self.cfg.train.train_unlabeled.loss)

    def init_ema_model(self):
        # 此操作控制ema_model的参数无法进行反向传播，即固定mea_model参数不变
        for param in self.ema_model.parameters():
            param.detach_()

        mp = list(self.model_with_loss.model.parameters())
        mcp = list(self.ema_model.parameters())
        n = len(mcp)
        for i in range(0, n):
            mcp[i].data[:] = mp[i].data[:].clone()
        # if len(self.cfg.train.gpu_ids) > 1:
            # if self.cfg.train.use_sync_batchnorm:
            #     ema_model = convert_model(ema_model)
            #     ema_model = DataParallelWithCallback(ema_model, device_ids=gpus)
            # else:
            # ema_model = torch.nn.DataParallel(ema_model, device_ids=gpus)
        return

    def update_lr(self, new_lr):
        self.optimizer.param_groups[0]['lr'] = new_lr
        if os.path.exists(self.cfg.model.backbone.load_from):
            for i in range(1, len(self.optimizer.param_groups)):
                self.optimizer.param_groups[i]['lr'] = new_lr * 10

    def build_optimizer(self):
        optimizer_cfg = self.cfg.train.optimizer
        param_list = list()
        if os.path.exists(self.cfg.model.backbone.load_from):
            for module in optimizer_cfg.lr_separate:
                param_list.append(dict(params=getattr(self.model_with_loss.model, module).parameters(),
                                       lr=optimizer_cfg.lr_separate[module]))
        else:
            param_list.append(dict(params=self.model_with_loss.parameters(), lr=optimizer_cfg.lr))

        assert len(param_list) > 0, "model parameters length should > 0!"

        if optimizer_cfg.name == 'SGD':
            self.optimizer = torch.optim.SGD(param_list, optimizer_cfg.lr, optimizer_cfg.momentum)
        elif optimizer_cfg.name == 'Adam':
            self.optimizer = torch.optim.Adam(param_list, optimizer_cfg.lr)
        elif optimizer_cfg.name == 'AMSGrad':
            self.optimizer = torch.optim.Adam(param_list, optimizer_cfg.lr, amsgrad=True)
        elif optimizer_cfg.name == 'Apollo':
            self.optimizer = Apollo(param_list, optimizer_cfg.lr, warmup=0)
        elif optimizer_cfg.name == 'AdaBelief':
            self.optimizer = AdaBelief(param_list, optimizer_cfg.lr, optimizer_cfg.betas, optimizer_cfg.eps)
        else:
            raise NotImplementedError

    def get_warmup_lr(self, cur_iters):
        if self.cfg.train.warmup.name == 'constant':
            warmup_lr = self.cfg.train.optimizer.lr * self.cfg.train.warmup.ratio
        elif self.cfg.train.warmup.name == 'linear':
            k = (1 - cur_iters / self.cfg.train.warmup.steps) * (1 - self.cfg.train.warmup.ratio)
            warmup_lr = self.cfg.train.optimizer.lr * (1 - k)
        elif self.cfg.train.warmup.name == 'exp':
            k = self.cfg.train.warmup.ratio ** (1 - cur_iters / self.cfg.train.warmup.steps)
            warmup_lr = self.cfg.train.optimizer.lr * k
        else:
            raise Exception('Unsupported warm up type!')
        return warmup_lr

    def warm_up(self, data_loader):
        model = self.model_with_loss
        model.train()
        moving_avg_loss_stats = {}
        num_iters = self.cfg.train.warmup.steps
        cur_iter = 0
        while cur_iter < num_iters:
            for iter_id, batch in enumerate(data_loader):
                cur_iter += 1
                if cur_iter >= num_iters:
                    break

                lr = self.get_warmup_lr(cur_iter)

                self.update_lr(lr)

                for k in batch:
                    if k == 'img':
                        batch[k] = batch[k].to(device=torch.device('cuda'), non_blocking=True)

                output, loss, loss_stats = self.run_one_batch(model, batch)

                for k in loss_stats:
                    if k not in moving_avg_loss_stats:
                        moving_avg_loss_stats[k] = MovingAverage(loss_stats[k].mean().item(),
                                                                 window_size=self.cfg.log.interval)
                    else:
                        moving_avg_loss_stats[k].push(loss_stats[k].mean().item())

                if iter_id % self.cfg.log.interval == 0:
                    log_str = '{}-{}||({}/{})steps:| '.format(self.cfg.model.task_head.name, 'warmup',
                                                              cur_iter, num_iters)
                    for l in moving_avg_loss_stats:
                        log_str += '{}: {:.4f}| '.format(l, moving_avg_loss_stats[l].avg())

                    logging.info(log_str)
                del output, loss, loss_stats

    def run_epoch(self, phase, epoch, train_loader, unlabel_train_loader=None):
        model = self.model_with_loss
        ema_model = self.ema_model
        if phase == 'train':
            model.train()
            ema_model.train()
            ema_model.cuda()
        else:
            if len(self.cfg.train.gpu_ids) > 1:
                model = self.model_with_loss.module
            model.eval()
            torch.cuda.empty_cache()
        results = {}
        avg_loss_stats = {}
        moving_avg_loss_stats = {}

        # 使用分布式的sampler，dataset shuffle是根据epoch生成随机种子的，必须在每个epoch前手动设置epoch号
        if phase == 'train' and self.rank > -1:
            logging.info("distributed sampler set epoch at {}".format(epoch))
            train_loader.sampler.set_epoch(epoch)

        num_iters = len(train_loader)  # if opt.num_iters < 0 else opt.num_iters

        unlabel_train_loader_iter = iter(unlabel_train_loader)

        for iter_id, label_batch in enumerate(train_loader):
            
            try:
                unlabel_batch = next(unlabel_train_loader_iter)
            except:
                unlabel_train_loader_iter = iter(unlabel_train_loader)
                unlabel_batch = next(unlabel_train_loader_iter)

            for k in label_batch:
                if k == 'img':
                    label_batch[k] = label_batch[k].to(device=torch.device('cuda'), non_blocking=True)
            for k in unlabel_batch:
                if k == 'img':
                    unlabel_batch[k] = unlabel_batch[k].to(device=torch.device('cuda'), non_blocking=True)

            output, loss, loss_stats = self.run_one_batch(model, ema_model, label_batch, unlabel_batch)

            if phase == 'val':
                dets = model.model.task_head.decode(output, label_batch, self.cfg.data.val.resize_keep_ratio)

                for i in range(len(dets[0])):
                    results[label_batch['img_info']['id'].cpu().numpy()[i]] = dets[0][i]  # TODO: 实例分割的json格式？

                self.evaluator.update(dets[1], label_batch['gt_masks'].byte().cpu().numpy())

            for k in loss_stats:
                if k not in avg_loss_stats:
                    avg_loss_stats[k] = AverageMeter(loss_stats[k].mean().item())
                    moving_avg_loss_stats[k] = MovingAverage(loss_stats[k].mean().item(),
                                                             window_size=self.cfg.log.interval)
                else:
                    avg_loss_stats[k].update(loss_stats[k].mean().item())
                    moving_avg_loss_stats[k].push(loss_stats[k].mean().item())

            if iter_id % self.cfg.log.interval == 0:
                log_str = '{}-{}|{}/{} epochs|({}/{}){}steps:| '.format(self.cfg.model.task_head.name, phase,
                                                                        epoch, self.cfg.train.num_epochs,
                                                                        iter_id, num_iters, self._iter)
                for l in avg_loss_stats:
                    log_str += '{}: {:.4f}| '.format(l, moving_avg_loss_stats[l].avg())
                    if phase == 'train' and self.rank < 1:
                        self.logger.scalar_summary('Train_loss_by_step/{}'.format(l), phase,
                                                   moving_avg_loss_stats[l].avg(), self._iter)
                logging.info(log_str)

            if phase == 'train':
                # TODO: this change
                total_iters = num_iters * self.cfg.train.num_epochs  # train.batchsize
                assert self._iter <= total_iters, 'wrong iters'
                lr = self.init_lr * ((1 - float(self._iter) / total_iters) ** self.lr_schedule.power)
                # logging.info('Drop LR to {}'.format(lr))
                self.update_lr(new_lr=lr)
                self._iter += 1

            del output, loss, loss_stats
        epoch_loss_dict = {k: v.avg for k, v in avg_loss_stats.items()}
        return results, epoch_loss_dict

    # def run_one_batch(self, model, bat, unlabel_batch, phase='train'):
    #     output, loss, loss_stats = model(label_batch)
    #     # loss = loss.mean()
    #     if phase == 'train':
    #         self.optimizer.zero_grad()
    #         loss.backward()
    #         self.optimizer.step()
    #     return output, loss, loss_stats

    def run_val_epoch(self, phase, epoch, data_loader):
        model = self.model_with_loss
        if phase == 'train':
            model.train()
            
        else:
            if len(self.cfg.train.gpu_ids) > 1:
                model = self.model_with_loss.module
            model.eval()
            torch.cuda.empty_cache()
        results = {}
        avg_loss_stats = {}
        moving_avg_loss_stats = {}

        # 使用分布式的sampler，dataset shuffle是根据epoch生成随机种子的，必须在每个epoch前手动设置epoch号
        if phase == 'train' and self.rank > -1:
            logging.info("distributed sampler set epoch at {}".format(epoch))
            data_loader.sampler.set_epoch(epoch)

        num_iters = len(data_loader)  # if opt.num_iters < 0 else opt.num_iters
        for iter_id, batch in enumerate(data_loader):
            if iter_id >= num_iters:
                break

            for k in batch:
                if k == 'img':
                    batch[k] = batch[k].to(device=torch.device('cuda'), non_blocking=True)

            output, loss, loss_stats = self.run_one_val_batch(model, batch, phase=phase)
            
            if phase == 'val':
                dets = model.model.task_head.decode(output, batch, self.cfg.data.val.resize_keep_ratio)

                for i in range(len(dets[0])):
                    results[batch['img_info']['id'].cpu().numpy()[i]] = dets[0][i]  # TODO: 实例分割的json格式？

                self.evaluator.update(dets[1],batch['gt_masks'].byte().cpu().numpy())


            for k in loss_stats:
                if k not in avg_loss_stats:
                    avg_loss_stats[k] = AverageMeter(loss_stats[k].mean().item())
                    moving_avg_loss_stats[k] = MovingAverage(loss_stats[k].mean().item(),
                                                             window_size=self.cfg.log.interval)
                else:
                    avg_loss_stats[k].update(loss_stats[k].mean().item())
                    moving_avg_loss_stats[k].push(loss_stats[k].mean().item())

            if iter_id % self.cfg.log.interval == 0:
                log_str = '{}-{}|{}/{} epochs|({}/{}){}steps:| '.format(self.cfg.model.task_head.name, phase,
                                                                        epoch, self.cfg.train.num_epochs,
                                                                        iter_id, num_iters, self._iter)
                for l in avg_loss_stats:
                    log_str += '{}: {:.4f}| '.format(l, moving_avg_loss_stats[l].avg())
                    if phase == 'train' and self.rank < 1:
                        self.logger.scalar_summary('Train_loss_by_step/{}'.format(l), phase,
                                                   moving_avg_loss_stats[l].avg(), self._iter)
                logging.info(log_str)
            if phase == 'train':
                # TODO: this change 
                total_iters = num_iters * self.cfg.train.num_epochs #train.batchsize
                assert  self._iter <= total_iters , 'wrong iters'
                lr = self.init_lr * ((1 - float(self._iter) / total_iters) ** self.lr_schedule.power)
                # logging.info('Drop LR to {}'.format(lr))
                self.update_lr(new_lr= lr)
                self._iter += 1

            del output, loss, loss_stats
        epoch_loss_dict = {k: v.avg for k, v in avg_loss_stats.items()}
        return results, epoch_loss_dict


    def run(self, train_loader, val_loader, unlabeled_train_loader):
        start_epoch = self.epoch
        best_mAP = -10

        # TODO: add test mode
        # with torch.no_grad():
        #     results, val_loss_dict = self.val(self.epoch, val_loader)
        #     self.evaluator.run_eval(results, self.cfg.save_dir, 1)

        if self.cfg.train.warmup.steps > 0 and start_epoch == 1:
            logging.info('Start warming up...')
            self.warm_up(train_loader)

            self.update_lr(new_lr=self.cfg.train.optimizer.lr)

        for epoch in range(start_epoch, self.cfg.train.num_epochs + 1):
            #######################################################################################
            results, train_loss_dict = self.train(epoch, train_loader, unlabeled_train_loader)  #############################
            #######################################################################################
            if self.rank < 1:
                self.save_model(os.path.join(self.cfg.save_dir, 'model_last.pth'), epoch, with_optimizer=True)
                for k, v in train_loss_dict.items():
                    self.logger.scalar_summary('Train_loss_by_epoch/{}'.format(k), 'train', v, epoch)

            if self.cfg.train.val_intervals > 0 and epoch % self.cfg.train.val_intervals == 0:
                with torch.no_grad():
                    results, val_loss_dict = self.val(self.epoch, val_loader)
                if self.rank < 1:
                    for k, v in val_loss_dict.items():
                        self.logger.scalar_summary('Train_loss_by_epoch/{}'.format(k), 'val', v, epoch)
                mAP = self.evaluator.run_eval(results, self.cfg.save_dir, epoch, rank=self.rank)
                if mAP > best_mAP and self.rank < 1:
                    best_mAP = mAP
                    best_save_path = os.path.join(self.cfg.save_dir, 'model_best_score')
                    if not os.path.exists(best_save_path):
                        os.makedirs(best_save_path)
                    self.save_model(os.path.join(best_save_path, 'model_best_score.pth'), epoch)
                    best_cocomap_txt_path = os.path.join(best_save_path, "best_map.txt")
                    shutil.copyfile(os.path.join(self.cfg.save_dir, 'eval_results.txt'), best_cocomap_txt_path)
                    shutil.copy(os.path.join(self.cfg.save_dir, 'results{}.json'.format(self.rank)), best_save_path)
                    with open(best_cocomap_txt_path, "a") as f:
                        f.write("\nEpoch:{}".format(epoch))

            # # TODO: the following is milestone change of lr
            # if epoch in self.cfg.train.lr_schedule.milestones:
            #     if self.rank < 1:
            #         self.save_model(os.path.join(self.cfg.save_dir, 'model_{}.pth'.format(epoch)), epoch,
            #                         with_optimizer=True)
            #     lr = self.cfg.train.optimizer.lr * (0.1 ** (self.cfg.train.lr_schedule.milestones.index(epoch) + 1))
            #     logging.info('Drop LR to {}'.format(lr))
            #     for param_group in self.optimizer.param_groups:
            #         param_group['lr'] = lr

            self.epoch += 1

    def update_ema_variables(self, ema_model, model, alpha_teacher, iteration):
        # Use the "true" average until the exponential average is more correct
        alpha_teacher = min(1 - 1 / (iteration + 1), alpha_teacher)
        print(alpha_teacher)

        for ema_param, param in zip(ema_model.parameters(), model.parameters()):
            ema_param.data[:] = alpha_teacher * ema_param[:].data[:] + (1 - alpha_teacher) * param[:].data[:]
        return ema_model

    def run_one_batch(self, model, ema_model, label_batch, unlabel_batch, phase='train'):
        # calculate label loss
        label_output, label_loss, loss_stats = model(label_batch)
        # get unlabel output from ema_model
        unlabel_output = ema_model(unlabel_batch['img'])
        # class mix
        batch_mixed = class_mix(unlabel_output[0], unlabel_batch["img"], self.cfg)
        # pred output from input made by class mix
        pred_mixed = model.model(batch_mixed['img_mixed'])[0]
        max_probs, pseudo_label = torch.max(batch_mixed['mask_mixed'], dim=1)

        unlabel_loss = self.unlabel_loss(pred_mixed, batch_mixed['mask_mixed'], max_probs, pseudo_label)
        loss_stats['unlabel_loss'] = unlabel_loss
        loss = label_loss + unlabel_loss

        if phase == 'train':
            self.optimizer.zero_grad()
            loss.backward()
            self.optimizer.step()

        return label_output, loss, loss_stats

    def run_one_val_batch(self, model, batch, phase='train'):

        output, loss, loss_stats = model(batch)
        # loss = loss.mean()
        if phase == 'train':
            self.optimizer.zero_grad()
            loss.backward()
            self.optimizer.step()
        return output, loss, loss_stats

    def train(self, epoch, train_loader, unlabel_train_loader):
        return self.run_epoch('train', epoch, train_loader, unlabel_train_loader)

    def val(self, epoch, data_loader):
        return self.run_val_epoch('val', epoch, data_loader)

    def load_model(self, config):
        load_path = None
        resume = False
        if 'resume' in config.train:
            resume = True
            if config.train.resume is not None:
                load_path = config.train.resume
            else:
                load_path = os.path.join(config.save_dir, 'model_last.pth')
        if 'load_model' in config.train:
            load_path = config.train.load_model

        start_epoch = 0
        checkpoint = torch.load(load_path, map_location=lambda storage, loc: storage)
        logging.info('loaded {}, epoch {}'.format(load_path, checkpoint['epoch']))
        state_dict_ = checkpoint['state_dict']
        # state_dict_ = checkpoint#this is for repvgg model
        state_dict = {}

        # TODO: 分布式的模型怎么加载回来???????
        # convert data_parallal to model
        for k in state_dict_:
            if k.startswith('module') and not k.startswith('module_list'):
                state_dict[k[7:]] = state_dict_[k]
            else:
                state_dict[k] = state_dict_[k]
        model_state_dict = self.model_with_loss.model.state_dict()

        # check loaded parameters and created model parameters
        for k in state_dict:
            if k in model_state_dict:
                if state_dict[k].shape != model_state_dict[k].shape:
                    logging.warning('Skip loading parameter {}, required shape{}, ' \
                          'loaded shape{}.'.format(
                        k, model_state_dict[k].shape, state_dict[k].shape))
                    state_dict[k] = model_state_dict[k]
            else:
                logging.warning('Drop parameter {}.'.format(k))
        for k in model_state_dict:
            if not (k in state_dict):
                logging.warning('No param {}.'.format(k))
                state_dict[k] = model_state_dict[k]
        self.model_with_loss.model.load_state_dict(state_dict, strict=False)

        # resume optimizer parameters
        if resume:
            if 'optimizer' in checkpoint:
                self.optimizer.load_state_dict(checkpoint['optimizer'])
                self.epoch = checkpoint['epoch'] + 1
                logging.info('resumed at epoch: {}'.format(self.epoch))
                if 'iter' in checkpoint:
                    self._iter = checkpoint['iter']
                    logging.info('checkpoint total steps: {}'.format(self._iter))
                start_lr = config.train.optimizer.lr
                if config.train.lr_schedule.milestones is not None:
                    for step in config.train.lr_schedule.milestones:
                        if self.epoch >= step:
                            start_lr *= 0.818
                for param_group in self.optimizer.param_groups:
                    param_group['lr'] = start_lr
                logging.info('Resumed optimizer with start lr {}'.format(start_lr))
            else:
                logging.warning('No optimizer parameters in checkpoint.')
# if __name__ == '__main__':
