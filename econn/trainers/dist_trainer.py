"""
Create by Chengqi.Lv
2020/9/4
"""
import logging
import shutil
import os
import torch
import torch.distributed as dist
from .base_trainer import BaseTrainer
from econn.utils.distributed_data_parallel import MyDDP


def average_gradients(model):
    """ Gradient averaging. """
    size = float(dist.get_world_size())
    for param in model.parameters():
        dist.all_reduce(param.grad.data, op=dist.ReduceOp.SUM)
        param.grad.data /= size


class DistTrainer(BaseTrainer):

    def set_device(self, gpu, batchsize, device):
        logging.info('Dist training device:{}'.format(gpu))
        self.rank = gpu
        self.model_with_loss = MyDDP(batchsize, module=self.model_with_loss.cuda(), device_ids=[gpu], output_device=gpu)

    def run_one_batch(self, model, batch, phase='train'):
        output, loss, loss_stats = model(batch)
        loss = loss.mean()
        if phase == 'train':
            self.optimizer.zero_grad()
            loss.backward()
            average_gradients(model)
            self.optimizer.step()
        return output, loss, loss_stats

    def load_model(self, config):
        load_path = None
        resume = False
        if 'resume' in config.train:
            resume = True
            if config.train.resume is not None:
                load_path = config.train.resume
            else:
                load_path = os.path.join(config.save_dir, 'model_last.pth')
        if 'load_model' in config.train:
            load_path = config.train.load_model

        start_epoch = 0
        checkpoint = torch.load(load_path, map_location=lambda storage, loc: storage)
        logging.info('loaded {}, epoch {}'.format(load_path, checkpoint['epoch']))
        state_dict_ = checkpoint['state_dict']
        state_dict = {}

        # convert data_parallal to model
        for k in state_dict_:
            if k.startswith('module') and not k.startswith('module_list'):
                state_dict[k[7:]] = state_dict_[k]
            else:
                state_dict[k] = state_dict_[k]
        model_state_dict = self.model_with_loss.module.model.state_dict()

        # check loaded parameters and created model parameters
        for k in state_dict:
            if k in model_state_dict:
                if state_dict[k].shape != model_state_dict[k].shape:
                    logging.warning('Skip loading parameter {}, required shape{}, ' \
                          'loaded shape{}.'.format(
                        k, model_state_dict[k].shape, state_dict[k].shape))
                    state_dict[k] = model_state_dict[k]
            else:
                logging.warning('Drop parameter {}.'.format(k))
        for k in model_state_dict:
            if not (k in state_dict):
                logging.warning('No param {}.'.format(k))
                state_dict[k] = model_state_dict[k]
        self.model_with_loss.module.model.load_state_dict(state_dict, strict=False)

        # resume optimizer parameters
        if resume:
            if 'optimizer' in checkpoint:
                self.optimizer.load_state_dict(checkpoint['optimizer'])
                self.epoch = checkpoint['epoch'] + 1
                logging.info('resumed at epoch: {}'.format(self.epoch))
                if 'iter' in checkpoint:
                    self._iter = checkpoint['iter']
                    logging.info('checkpoint total steps: {}'.format(self._iter))
                start_lr = config.train.optimizer.lr
                if config.train.lr_schedule.name == 'multi_step':
                    if config.train.lr_schedule.milestones is not None:
                        for step in config.train.lr_schedule.milestones:
                            if self.epoch >= step:
                                start_lr *= 0.1
                elif config.train.lr_schedule.name == 'one_cycle': ####### Here has Bug to fix!
                    start_lr = self.scheduler(self.epoch)
                for param_group in self.optimizer.param_groups:
                    param_group['lr'] = start_lr
                logging.info('Resumed optimizer with start lr {}'.format(start_lr))
            else:
                logging.warning('No optimizer parameters in checkpoint.')

    def save_model(self, path, epoch, with_optimizer=True):
        if isinstance(self.model_with_loss, MyDDP):
            state_dict = self.model_with_loss.module.model.state_dict()
        else:
            state_dict = self.model_with_loss.model.state_dict()
        data = {'epoch': epoch,
                'state_dict': state_dict,
                'iter': self._iter}
        if with_optimizer:
            data['optimizer'] = self.optimizer.state_dict()
        torch.save(data, path)

