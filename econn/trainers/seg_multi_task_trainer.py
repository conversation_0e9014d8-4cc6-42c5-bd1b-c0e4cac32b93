"""
Create by Chengqi.Lv
2020/12/16
"""
import logging
import shutil
import os
import torch
from functools import partial
from .base_seg_trainer import BaseTrainer
from econn.utils.logger.average import AverageMeter, MovingAverage
from econn.optimizer.apollo import Apollo
from econn.optimizer.adabelief import Ada<PERSON><PERSON>ef
try:
    import torch.cuda.amp as amp
    scaler = amp.GradScaler()
except Exception as e:
    print(e)

class IterLoader:

    def __init__(self, dataloader):
        self._dataloader = dataloader
        self.iter_loader = iter(self._dataloader)
        self._epoch = 0

    @property
    def epoch(self):
        return self._epoch

    def __next__(self):
        try:
            data = next(self.iter_loader)
        except StopIteration:
            self._epoch += 1
            if hasattr(self._dataloader.sampler, 'set_epoch'):
                self._dataloader.sampler.set_epoch(self._epoch)
                logging.info("{} distributed sampler set epoch at {}".format(self.__class__.__name__, self._epoch))
            self.iter_loader = iter(self._dataloader)
            data = next(self.iter_loader)

        return data

    def __len__(self):
        return len(self._dataloader)


class MultiTaskModleWithLoss(torch.nn.Module):
    def __init__(self, model, weight_decay):
        super(MultiTaskModleWithLoss, self).__init__()
        self.model = model
        self.weight_decay = weight_decay

    def forward(self, gt_meta):
        preds, loss, loss_states = self.model.forward_train(gt_meta)
        l2regularization_loss = 0.0
        for name, param in self.model.named_parameters():  # TODO: 根据Task算正则
            if 'bias' not in name:
                l2regularization_loss += torch.sum(torch.pow(param, 2))
        l2regularization_loss = 0.5 * self.weight_decay * l2regularization_loss
        loss_states["l2reg_loss"] = l2regularization_loss
        loss += l2regularization_loss

        return preds, loss, loss_states


class MultiTaskTrainer(BaseTrainer):
    def __init__(self,
                 cfg,
                 model,
                 evaluator,
                 logger,
                 rank=-1):
        super(MultiTaskTrainer, self).__init__(cfg, model, evaluator, logger, rank)
        self.cfg = cfg
        self.model_with_loss = MultiTaskModleWithLoss(model, cfg.train.optimizer.weight_decay)
        self.evaluator = evaluator
        self.rank = rank
        self.logger = logger
        self._iter = 1
        self.epoch = 1
        self.init_lr = self.cfg.train.optimizer.lr
        self.lr_schedule = self.cfg.train.lr_schedule
        self.use_amp = cfg.train.use_amp

    def update_lr(self, new_lr):
        self.optimizer.param_groups[0]['lr'] = new_lr
        if os.path.exists(self.cfg.model.backbone.load_from):
            for i in range(1, len(self.optimizer.param_groups)):
                self.optimizer.param_groups[i]['lr'] = new_lr# * 10

    def build_optimizer(self):
        optimizer_cfg = self.cfg.train.optimizer
        param_list = list()
        if os.path.exists(self.cfg.model.backbone.load_from):
            for module in optimizer_cfg.lr_separate:
                param_list.append(dict(params=getattr(self.model_with_loss.model, module).parameters(),
                                       lr=optimizer_cfg.lr_separate[module]))
        else:
            param_list.append(dict(params=self.model_with_loss.parameters(), lr=optimizer_cfg.lr))

        assert len(param_list) > 0, "model parameters length should > 0!"

        if optimizer_cfg.name == 'SGD':
            self.optimizer = torch.optim.SGD(param_list, optimizer_cfg.lr, optimizer_cfg.momentum)
        elif optimizer_cfg.name == 'Adam':
            self.optimizer = torch.optim.Adam(param_list, optimizer_cfg.lr)
        elif optimizer_cfg.name == 'AMSGrad':
            self.optimizer = torch.optim.Adam(param_list, optimizer_cfg.lr, amsgrad=True)
        elif optimizer_cfg.name == 'Apollo':
            self.optimizer = Apollo(param_list, optimizer_cfg.lr, warmup=0)
        elif optimizer_cfg.name == 'AdaBelief':
            self.optimizer = AdaBelief(param_list, optimizer_cfg.lr, optimizer_cfg.betas, optimizer_cfg.eps)
        else:
            raise NotImplementedError

    def warm_up(self, data_loaders):
        model = self.model_with_loss
        model.train()
        moving_avg_loss_stats = {}
        num_iters = self.cfg.train.warmup.steps
        cur_iter = 0
        while cur_iter < num_iters:
            lr = self.get_warmup_lr(cur_iter)
            self.update_lr(new_lr=lr)
            multi_task_batch = {}
            for task, dataloader in data_loaders.items():
                batch = next(dataloader)
                batch['img'] = batch['img'].to(device=torch.device('cuda'), non_blocking=True)
                multi_task_batch[task] = batch
            multi_task_batch['imgs'] = torch.cat([batch['img'] for key, batch in multi_task_batch.items()], dim=0)
            output, loss, loss_stats = self.run_one_batch(model, multi_task_batch)
            for k in loss_stats:
                if k not in moving_avg_loss_stats:
                    moving_avg_loss_stats[k] = MovingAverage(loss_stats[k].mean().item(),
                                                             window_size=self.cfg.log.interval)
                else:
                    moving_avg_loss_stats[k].push(loss_stats[k].mean().item())
            del output, loss, loss_stats

            if cur_iter % self.cfg.log.interval == 0:
                log_str = '{}-{}||({}/{})steps:| '.format(self.cfg.model.architecture, 'warmup',
                                                          cur_iter, num_iters)
                for l in moving_avg_loss_stats:
                    log_str += '{}: {:.4f}| '.format(l, moving_avg_loss_stats[l].avg())

                logging.info(log_str)

            cur_iter += 1
            if cur_iter >= num_iters:
                break
    def run_one_batch(self, model, batch, phase='train'):

        if self.use_amp:
            with amp.autocast():
                output, loss, loss_stats = model(batch)
                loss = loss.mean()
            if phase == 'train':
                self.optimizer.zero_grad()
                scaler.scale(loss).backward()
                scaler.step(self.optimizer)
                scaler.update()
        else:

            output, loss, loss_stats = model(batch)
            loss = loss.mean()
            if phase == 'train':
                self.optimizer.zero_grad()
                loss.backward()
                self.optimizer.step()
        return output, loss, loss_stats

    def run_epoch(self, phase, epoch, data_loaders):
        model = self.model_with_loss
        if phase == 'train':
            model.train()
        else:
            if len(self.cfg.train.gpu_ids) > 1:
                model = self.model_with_loss.module
            model.eval()
            torch.cuda.empty_cache()
        results = {k:{} for k in data_loaders}
        avg_loss_stats = {}
        moving_avg_loss_stats = {}

        iter_id = 0
        num_iters = max([len(dataloader) for _, dataloader in data_loaders.items()])
        while True:
            if iter_id >= num_iters:
            # if iter_id >= 10:
                break
            multi_task_batch = {}
            for task, dataloader in data_loaders.items():
                batch = next(dataloader)
                batch['img'] = batch['img'].to(device=torch.device('cuda'))
                multi_task_batch[task] = batch
            multi_task_batch['imgs'] = torch.cat([batch['img'] for key, batch in multi_task_batch.items()], dim=0)
            output, loss, loss_stats = self.run_one_batch(model, multi_task_batch, phase=phase)
            if phase == 'val':
                for idx, task in enumerate(data_loaders):
                    output_per_task = output[task]
                    dets = model.model.task_heads[task].decode(output_per_task, multi_task_batch[task],
                                                               self.cfg.data[task].val.resize_keep_ratio)
                    results[task][multi_task_batch[task]['img_info']['id'].cpu().numpy()[0]] = dets[0]

            for k in loss_stats:
                if k not in avg_loss_stats:
                    avg_loss_stats[k] = AverageMeter(loss_stats[k].mean().item())
                    moving_avg_loss_stats[k] = MovingAverage(loss_stats[k].mean().item(),
                                                             window_size=self.cfg.log.interval)
                else:
                    avg_loss_stats[k].update(loss_stats[k].mean().item())
                    moving_avg_loss_stats[k].push(loss_stats[k].mean().item())
            del output, loss, loss_stats

            if iter_id % self.cfg.log.interval == 0:
                log_str = '{}-{}|{}/{} epochs|({}/{}){}steps:| '.format(self.cfg.model.architecture, phase,
                                                                        epoch, self.cfg.train.num_epochs,
                                                                        iter_id, num_iters, self._iter)
                for l in avg_loss_stats:
                    log_str += '{}: {:.4f}| '.format(l, moving_avg_loss_stats[l].avg())
                    if phase == 'train' and self.rank < 1:
                        self.logger.scalar_summary('Train_loss_by_step/{}'.format(l), phase,
                                                   moving_avg_loss_stats[l].avg(), self._iter)
                logging.info(log_str)
            if phase == 'train':
                total_iters = num_iters * self.cfg.train.num_epochs  # train.batchsize
                assert self._iter <= total_iters, 'wrong iters'
                lr = self.init_lr * ((1 - float(self._iter) / total_iters) ** self.lr_schedule.power)
                # logging.info('Drop LR to {}'.format(lr))
                # if not os.path.isfile(self.cfg.model.backbone.load_from):
                self.update_lr(lr)

                self._iter += 1
            iter_id += 1
        epoch_loss_dict = {k: v.avg for k, v in avg_loss_stats.items()}
        return results, epoch_loss_dict

    def train(self, epoch, data_loader):
        return self.run_epoch('train', epoch, data_loader)

    def val(self, epoch, data_loader):
        return self.run_epoch('val', epoch, data_loader)

    def run(self, train_loader, val_loader):
        train_loaders = {k: IterLoader(v) for k, v in train_loader.items()}
        val_loaders = {k: IterLoader(v) for k, v in val_loader.items()}
        start_epoch = self.epoch
        best_mAP = {k: -10 for k in val_loader}

        # TODO: add test mode
        # with torch.no_grad():
        #     results, val_loss_dict = self.val(self.epoch, val_loader)
        #     self.evaluator.run_eval(results, self.cfg.save_dir, 1)

        if self.cfg.train.warmup.steps > 0 and start_epoch == 1:
            logging.info('Start warming up...')
            self.warm_up(train_loaders)

            self.update_lr(new_lr= self.cfg.train.optimizer.lr)

        for epoch in range(start_epoch, self.cfg.train.num_epochs + 1):
            results, train_loss_dict = self.train(epoch, train_loaders)
            if self.rank < 1:
                self.save_model(os.path.join(self.cfg.save_dir, 'model_last.pth'), epoch, with_optimizer=True)
                for k, v in train_loss_dict.items():
                    self.logger.scalar_summary('Train_loss_by_epoch/{}'.format(k), 'train', v, epoch)

            if self.cfg.train.val_intervals > 0 and epoch % self.cfg.train.val_intervals == 0:
                with torch.no_grad():
                    results, val_loss_dict = self.val(self.epoch, val_loaders)
                if self.rank < 1:
                    for k, v in val_loss_dict.items():
                        self.logger.scalar_summary('Train_loss_by_epoch/{}'.format(k), 'val', v, epoch)

                for task, evaluator in self.evaluator.items():
                    mAP = evaluator.run_eval(results[task], self.cfg.save_dir, epoch, rank=self.rank, task_name=task)
                    if mAP > best_mAP[task] and self.rank < 1:
                        best_mAP[task] = mAP
                        best_save_path = os.path.join(self.cfg.save_dir, task + '_model_best_score')
                        if not os.path.exists(best_save_path):
                            os.makedirs(best_save_path)
                        self.save_model(os.path.join(best_save_path, 'model_best_score.pth'), epoch)
                        best_cocomap_txt_path = os.path.join(best_save_path, "best_map.txt")
                        shutil.copyfile(os.path.join(self.cfg.save_dir, task+'eval_results.txt'), best_cocomap_txt_path)
                        shutil.copy(os.path.join(self.cfg.save_dir, task+'results{}.json'.format(self.rank)), best_save_path)
                        with open(best_cocomap_txt_path, "a") as f:
                            f.write("\nEpoch:{}".format(epoch))

            # if epoch in self.cfg.train.lr_schedule.milestones:
            #     if self.rank < 1:
            #         self.save_model(os.path.join(self.cfg.save_dir, 'model_{}.pth'.format(epoch)), epoch,
            #                         with_optimizer=True)
            #     lr = self.cfg.train.optimizer.lr * (0.1 ** (self.cfg.train.lr_schedule.milestones.index(epoch) + 1))
            #     logging.info('Drop LR to {}'.format(lr))
            #     for param_group in self.optimizer.param_groups:
            #         param_group['lr'] = lr

            self.epoch += 1

