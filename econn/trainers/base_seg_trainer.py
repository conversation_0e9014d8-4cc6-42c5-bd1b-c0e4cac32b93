"""
Create by Chengqi.Lv
2020/3/24
"""

from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

import time
import logging
import shutil
import os
import torch
from econn.optimizer.apollo import Apollo
from econn.optimizer.adabelief import AdaBelief
from econn.utils.data_parallel import DataParallel, _DataParallel
from econn.utils.logger.average import AverageMeter, MovingAverage


class ModleWithLoss(torch.nn.Module):
    def __init__(self, model, weight_decay):
        super(ModleWith<PERSON>oss, self).__init__()
        self.model = model
        self.weight_decay = weight_decay

    def forward(self, gt_meta):
        preds, loss, loss_states = self.model.forward_train(gt_meta)
        l2regularization_loss = 0.0
        for name, param in self.model.named_parameters():
            if 'bias' not in name:
                l2regularization_loss += torch.sum(torch.pow(param, 2))
        l2regularization_loss = 0.5 * self.weight_decay * l2regularization_loss
        loss_states["l2reg_loss"] = l2regularization_loss
        loss += l2regularization_loss

        return preds, loss, loss_states


class BaseTrainer(object):
    def __init__(self,
                 cfg,
                 model,
                 evaluator,
                 logger,
                 rank=-1):
        self.cfg = cfg
        self.model_with_loss = ModleWithLoss(model, cfg.train.optimizer.weight_decay)
        self.evaluator = evaluator
        self.rank = rank
        self.logger = logger
        self._iter = 1
        self.epoch = 1
        self.use_amp = self.cfg.train.use_amp
    def build_optimizer(self):
        optimizer_cfg = self.cfg.train.optimizer
        if optimizer_cfg.name == 'SGD':
            self.optimizer = torch.optim.SGD(self.model_with_loss.parameters(), optimizer_cfg.lr, optimizer_cfg.momentum)
        elif optimizer_cfg.name == 'Adam':
            self.optimizer = torch.optim.Adam(self.model_with_loss.parameters(), optimizer_cfg.lr)
        elif optimizer_cfg.name == 'AdamW':
            self.optimizer = torch.optim.AdamW(self.model_with_loss.parameters(), optimizer_cfg.lr, weight_decay=0.0001)
        elif optimizer_cfg.name == 'AMSGrad':
            self.optimizer = torch.optim.Adam(self.model_with_loss.parameters(), optimizer_cfg.lr, amsgrad=True)
        elif optimizer_cfg.name == 'Apollo':
            self.optimizer = Apollo(self.model_with_loss.parameters(), optimizer_cfg.lr, warmup=0)
        elif optimizer_cfg.name == 'AdaBelief':
            self.optimizer = AdaBelief(self.model_with_loss.parameters(), optimizer_cfg.lr, optimizer_cfg.betas, optimizer_cfg.eps)
        else:
            raise NotImplementedError

    def get_warmup_lr(self, cur_iters):
        if self.cfg.train.warmup.name == 'constant':
            warmup_lr = self.cfg.train.optimizer.lr * self.cfg.train.warmup.ratio
        elif self.cfg.train.warmup.name == 'linear':
            k = (1 - cur_iters / self.cfg.train.warmup.steps) * (1 - self.cfg.train.warmup.ratio)
            warmup_lr = self.cfg.train.optimizer.lr * (1 - k)
        elif self.cfg.train.warmup.name == 'exp':
            k = self.cfg.train.warmup.ratio ** (1 - cur_iters / self.cfg.train.warmup.steps)
            warmup_lr = self.cfg.train.optimizer.lr * k
        else:
            raise Exception('Unsupported warm up type!')
        return warmup_lr

    def set_device(self, gpus, batchsize, device):
        master_batch_size = batchsize // len(gpus)
        rest_batch_size = (batchsize - master_batch_size)
        chunk_sizes = [master_batch_size]
        for i in range(len(gpus) - 1):
            slave_chunk_size = rest_batch_size // (len(gpus) - 1)
            if i < rest_batch_size % (len(gpus) - 1):
                slave_chunk_size += 1
            chunk_sizes.append(slave_chunk_size)
        logging.info('training chunk_sizes:{}'.format(chunk_sizes))

        if len(gpus) > 1:
            self.model_with_loss = DataParallel(
                self.model_with_loss, device_ids=gpus,
                chunk_sizes=chunk_sizes).to(device)
        else:
            self.model_with_loss = self.model_with_loss.to(device)

        # if self.optimizer is not None:
        #     for state in self.optimizer.state.values():
        #         for k, v in state.items():
        #             if isinstance(v, torch.Tensor):
        #                 state[k] = v.to(device=device, non_blocking=True)

    def load_model(self, config):
        load_path = None
        resume = False
        if 'resume' in config.train:
            resume = True
            if config.train.resume is not None:
                load_path = config.train.resume
            else:
                load_path = os.path.join(config.save_dir, 'model_last.pth')
        if 'load_model' in config.train:
            load_path = config.train.load_model

        start_epoch = 0
        checkpoint = torch.load(load_path, map_location=lambda storage, loc: storage)
        logging.info('loaded {}, epoch {}'.format(load_path, checkpoint['epoch']))
        state_dict_ = checkpoint['state_dict']
        # state_dict_ = checkpoint#this is for repvgg model
        state_dict = {}

        # TODO: 分布式的模型怎么加载回来???????
        # convert data_parallal to model
        for k in state_dict_:
            if k.startswith('module') and not k.startswith('module_list'):
                state_dict[k[7:]] = state_dict_[k]
            else:
                state_dict[k] = state_dict_[k]
        model_state_dict = self.model_with_loss.model.state_dict()

        # check loaded parameters and created model parameters
        for k in state_dict:
            if k in model_state_dict:
                if state_dict[k].shape != model_state_dict[k].shape:
                    logging.warning('Skip loading parameter {}, required shape{}, ' \
                          'loaded shape{}.'.format(
                        k, model_state_dict[k].shape, state_dict[k].shape))
                    state_dict[k] = model_state_dict[k]
            else:
                logging.warning('Drop parameter {}.'.format(k))
        for k in model_state_dict:
            if not (k in state_dict):
                logging.warning('No param {}.'.format(k))
                state_dict[k] = model_state_dict[k]
        self.model_with_loss.model.load_state_dict(state_dict, strict=False)

        # resume optimizer parameters
        if resume:
            if 'optimizer' in checkpoint:
                self.optimizer.load_state_dict(checkpoint['optimizer'])
                self.epoch = checkpoint['epoch'] + 1
                logging.info('resumed at epoch: {}'.format(self.epoch))
                if 'iter' in checkpoint:
                    self._iter = checkpoint['iter']
                    logging.info('checkpoint total steps: {}'.format(self._iter))
                start_lr = config.train.optimizer.lr
                if config.train.lr_schedule.milestones is not None:
                    for step in config.train.lr_schedule.milestones:
                        if self.epoch >= step:
                            start_lr *= 0.818
                for param_group in self.optimizer.param_groups:
                    param_group['lr'] = start_lr
                logging.info('Resumed optimizer with start lr {}'.format(start_lr))
            else:
                logging.warning('No optimizer parameters in checkpoint.')

    def save_model(self, path, epoch, with_optimizer=True):
        if isinstance(self.model_with_loss, torch.nn.DataParallel) or \
                isinstance(self.model_with_loss, _DataParallel):
            state_dict = self.model_with_loss.module.model.state_dict()
        else:
            state_dict = self.model_with_loss.model.state_dict()
        data = {'epoch': epoch,
                'state_dict': state_dict,
                'iter': self._iter}
        if with_optimizer:
            data['optimizer'] = self.optimizer.state_dict()
        torch.save(data, path)

    def run_one_batch(self, model, batch, phase='train'):

        output, loss, loss_stats = model(batch)
        # loss = loss.mean()
        if phase == 'train':
            self.optimizer.zero_grad()
            loss.backward()
            self.optimizer.step()
        return output, loss, loss_stats

    def warm_up(self, data_loader):
        model = self.model_with_loss
        model.train()
        moving_avg_loss_stats = {}
        num_iters = self.cfg.train.warmup.steps
        cur_iter = 0
        while cur_iter < num_iters:
            for iter_id, batch in enumerate(data_loader):
                cur_iter += 1
                if cur_iter >= num_iters:
                    break

                lr = self.get_warmup_lr(cur_iter)
                for param_group in self.optimizer.param_groups:
                    param_group['lr'] = lr

                for k in batch:
                    if k == 'img':
                        batch[k] = batch[k].to(device=torch.device('cuda'), non_blocking=True)

                output, loss, loss_stats = self.run_one_batch(model, batch)

                for k in loss_stats:
                    if k not in moving_avg_loss_stats:
                        moving_avg_loss_stats[k] = MovingAverage(loss_stats[k].mean().item(),
                                                                 window_size=self.cfg.log.interval)
                    else:
                        moving_avg_loss_stats[k].push(loss_stats[k].mean().item())

                if iter_id % self.cfg.log.interval == 0:
                    log_str = '{}-{}||({}/{})steps:| '.format(self.cfg.model.task_head.name, 'warmup',
                                                              cur_iter, num_iters)
                    for l in moving_avg_loss_stats:
                        log_str += '{}: {:.4f}| '.format(l, moving_avg_loss_stats[l].avg())

                    logging.info(log_str)
                del output, loss, loss_stats

    def run_epoch(self, phase, epoch, data_loader):
        model = self.model_with_loss
        if phase == 'train':
            # pass
            model.train()
        else:
            if len(self.cfg.train.gpu_ids) > 1:
                model = self.model_with_loss.module
            model.eval()
            torch.cuda.empty_cache()
        results = {}
        avg_loss_stats = {}
        moving_avg_loss_stats = {}

        # 使用分布式的sampler，dataset shuffle是根据epoch生成随机种子的，必须在每个epoch前手动设置epoch号
        if phase == 'train' and self.rank > -1:
            logging.info("distributed sampler set epoch at {}".format(epoch))
            data_loader.sampler.set_epoch(epoch)

        num_iters = len(data_loader)  # if opt.num_iters < 0 else opt.num_iters
        for iter_id, batch in enumerate(data_loader):
            if phase == 'train':
                break
            if iter_id >= num_iters:
                break

            for k in batch:
                if k == 'img':
                    batch[k] = batch[k].to(device=torch.device('cuda'), non_blocking=True)

            output, loss, loss_stats = self.run_one_batch(model, batch, phase=phase)

            if phase == 'val':
                dets = model.model.task_head.decode(output, batch, self.cfg.data.val.resize_keep_ratio)
                results[batch['img_info']['id'].cpu().numpy()[0]] = dets[0]  # TODO: 实例分割的json格式？

            for k in loss_stats:
                if k not in avg_loss_stats:
                    avg_loss_stats[k] = AverageMeter(loss_stats[k].mean().item())
                    moving_avg_loss_stats[k] = MovingAverage(loss_stats[k].mean().item(),
                                                             window_size=self.cfg.log.interval)
                else:
                    avg_loss_stats[k].update(loss_stats[k].mean().item())
                    moving_avg_loss_stats[k].push(loss_stats[k].mean().item())

            if iter_id % self.cfg.log.interval == 0:
                log_str = '{}-{}|{}/{} epochs|({}/{}){}steps:| '.format(self.cfg.model.task_head.name, phase,
                                                                        epoch, self.cfg.train.num_epochs,
                                                                        iter_id, num_iters, self._iter)
                for l in avg_loss_stats:
                    log_str += '{}: {:.4f}| '.format(l, moving_avg_loss_stats[l].avg())
                    if phase == 'train' and self.rank < 1:
                        self.logger.scalar_summary('Train_loss_by_step/{}'.format(l), phase,
                                                   moving_avg_loss_stats[l].avg(), self._iter)
                logging.info(log_str)
            if phase == 'train':
                self._iter += 1
            del output, loss, loss_stats
        epoch_loss_dict = {k: v.avg for k, v in avg_loss_stats.items()}
        return results, epoch_loss_dict

    def train(self, epoch, data_loader):
        return self.run_epoch('train', epoch, data_loader)

    def val(self, epoch, data_loader):
        return self.run_epoch('val', epoch, data_loader)

    def test(self, epoch, data_loader):
        return self.run_epoch('test', epoch, data_loader)

    def run(self, train_loader, val_loader):
        start_epoch = self.epoch
        best_mAP = -10

        # TODO: add test mode
        # with torch.no_grad():
        #     results, val_loss_dict = self.val(self.epoch, val_loader)
        #     self.evaluator.run_eval(results, self.cfg.save_dir, 1)

        if self.cfg.train.warmup.steps > 0 and start_epoch == 1:
            logging.info('Start warming up...')
            self.warm_up(train_loader)
            for param_group in self.optimizer.param_groups:
                param_group['lr'] = self.cfg.train.optimizer.lr

        for epoch in range(start_epoch, self.cfg.train.num_epochs + 1):
            results, train_loss_dict = self.train(epoch, train_loader)
            if self.rank < 1:
                self.save_model(os.path.join(self.cfg.save_dir, 'model_last.pth'), epoch, with_optimizer=True)
                for k, v in train_loss_dict.items():
                    self.logger.scalar_summary('Train_loss_by_epoch/{}'.format(k), 'train', v, epoch)

            if self.cfg.train.val_intervals > 0 and epoch % self.cfg.train.val_intervals == 0:
                with torch.no_grad():
                    results, val_loss_dict = self.val(self.epoch, val_loader)
                if self.rank < 1:
                    for k, v in val_loss_dict.items():
                        self.logger.scalar_summary('Train_loss_by_epoch/{}'.format(k), 'val', v, epoch)
                mAP = self.evaluator.run_eval(results, self.cfg.save_dir, epoch, rank=self.rank)
                if mAP > best_mAP and self.rank < 1:
                    best_mAP = mAP
                    best_save_path = os.path.join(self.cfg.save_dir, 'model_best_score')
                    if not os.path.exists(best_save_path):
                        os.makedirs(best_save_path)
                    self.save_model(os.path.join(best_save_path, 'model_best_score.pth'), epoch)
                    best_cocomap_txt_path = os.path.join(best_save_path, "best_map.txt")
                    shutil.copyfile(os.path.join(self.cfg.save_dir, 'eval_results.txt'), best_cocomap_txt_path)
                    shutil.copy(os.path.join(self.cfg.save_dir, 'results{}.json'.format(self.rank)), best_save_path)
                    with open(best_cocomap_txt_path, "a") as f:
                        f.write("\nEpoch:{}".format(epoch))

            if epoch in self.cfg.train.lr_schedule.milestones:
                if self.rank < 1:
                    self.save_model(os.path.join(self.cfg.save_dir, 'model_{}.pth'.format(epoch)), epoch,
                                    with_optimizer=True)
                lr = self.cfg.train.optimizer.lr * (0.1 ** (self.cfg.train.lr_schedule.milestones.index(epoch) + 1))
                # logging.info('Drop LR to {}'.format(lr))
                for param_group in self.optimizer.param_groups:
                    param_group['lr'] = lr

            self.epoch += 1

# if __name__ == '__main__':
