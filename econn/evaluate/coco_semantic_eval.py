"""
create by yang<PERSON><PERSON><PERSON>
date:11/5/20
"""

import cv2
import numpy as np
import json
import os
import copy
import logging
from tqdm import tqdm
# import pycocotools.coco as coco
# from pycocotools.cocostuffeval import COCOStuffeval
# import pycocotools.mask as mask_util
import numpy as np
import torch
import time
# from pycocotools import mask
from PIL import Image, ImagePalette # For indexed images
import matplotlib # For Matlab's color maps


def torch_nanmean(x):
    num = torch.where(torch.isnan(x), torch.full_like(x, 0), torch.full_like(x, 1)).sum()
    value = torch.where(torch.isnan(x), torch.full_like(x, 0), x).sum()
    return value / num

class MyEncoder(json.JSONEncoder):

    def default(self, obj):
        """
        只要检查到了是bytes类型的数据就把它转为str类型
        :param obj:
        :return:
        """
        if isinstance(obj, bytes):
            return str(obj, encoding='utf-8')
        return json.JSONEncoder.default(self, obj)

class CocoStuffEvaluator:
    # TODO:参考detectron，将bbox和分割的eval合并
    def __init__(self,  dataset, logger, ignore_index=None):
        # self.class_names = dataset.class_names
        self._valid_ids = dataset._valid_ids
        self.dataset = dataset
        self._valid_ids = dataset._valid_ids
        self.logger = logger
        self.nclass = len(self._valid_ids)
        self.confusion_matrix = np.zeros((self.nclass, self.nclass))
        self.confusion_matrix_gpu = torch.zeros((self.nclass, self.nclass), device='cuda')
        self.ignore_index = ignore_index
    def _fast_hist(self, label_true, label_pred, n_class):
        mask = (label_true >= 0) & (label_true < n_class)

        if self.ignore_index is not None:
            mask = mask & (label_true != self.ignore_index)

        hist = np.bincount(
            n_class * label_true[mask].astype(int) +
            label_pred[mask], minlength=n_class ** 2)

        # print(np.unique(label_true))
        # print(np.unique(label_pred))
        hist = hist.reshape(n_class, n_class)

        return hist

    def _fast_hist_gpu(self, label_true, label_pred, n_class):
        mask = (label_true >= 0) & (label_true < n_class)

        if self.ignore_index is not None:
            mask = mask & (label_true != self.ignore_index)

        hist = torch.bincount(
            n_class * label_true[mask].astype(int) +
            label_pred[mask], minlength=n_class ** 2)

        # print(np.unique(label_true))
        # print(np.unique(label_pred))
        hist = hist.reshape(n_class, n_class)

        return hist

    def _get_scores(self):
        """Returns accuracy score evaluation result.
            - overall accuracy
            - mean accuracy
            - mean IU
            - fwavacc
        """
        hist = self.confusion_matrix
        pacc = np.diag(hist).sum() / (hist.sum()+1)
        maccs = np.diag(hist) / (hist.sum(axis=1)+1)
        # print('category-wise mean accuracy: ', acc_cls)

        macc = np.nanmean(maccs)
        iu = np.diag(hist) / ((hist.sum(axis=1) + 1) + hist.sum(axis=0) - np.diag(hist))
        # print('category-wise mean iou: ', iu)

        mean_iu = np.nanmean(iu)
        freq = hist.sum(axis=1) / (hist.sum() + 1)
        fwavacc = (freq[freq > 0] * iu[freq > 0]).sum()
        cls_iu = dict(zip(range(self.nclass), iu))

        return pacc, maccs, macc, fwavacc, mean_iu, cls_iu

    def _get_scores_gpu(self):
        """Returns accuracy score evaluation result.
            - overall accuracy
            - mean accuracy
            - mean IU
            - fwavacc
        """
        hist = self.confusion_matrix_gpu
        pacc = torch.diag(hist).sum() / (hist.sum()+1)
        maccs = torch.diag(hist) / (hist.sum(axis=1)+1)
        # print('category-wise mean accuracy: ', acc_cls)

        macc = torch_nanmean(maccs)
        iu = torch.diag(hist) / ((hist.sum(axis=1) + 1) + hist.sum(axis=0) - torch.diag(hist))
        # print('category-wise mean iou: ', iu)

        mean_iu = np.nanmean(iu)
        freq = hist.sum(axis=1) / (hist.sum() + 1)
        fwavacc = (freq[freq > 0] * iu[freq > 0]).sum()
        cls_iu = dict(zip(range(self.nclass), iu))

        return pacc, maccs, macc, fwavacc, mean_iu, cls_iu

    def convert_det_format(self, all_dets):
        # TODO: 使用多线程加速
        logging.info('Start converting data to coco format...')
        results = []
        for image_id in tqdm(all_dets):
            for det in all_dets[image_id]:
                det['image_id'] = int(image_id)
                results.append(det)
        logging.info('OK!')
        return results
    def cocoSegmentationToSegmentationMap(self, anns, checkUniquePixelLabel= True):

        imgsize = anns[0]['segmentation']['size']
        labelMap = np.zeros(imgsize)
        for ann in anns:
            labelMask = self.coco.annToMask(ann) == 1
            newLabel = ann['category_id']

            if checkUniquePixelLabel and (labelMap[labelMask] != 0).any():
                raise Exception('Error: Some pixels have more than one label !' )

            labelMap[labelMask] = newLabel

        logging.info('OK!')
        return labelMap

    def save_results(self, results, save_dir):
        json.dump(self.convert_det_format(results),
                  open(save_dir, 'w'),
                  cls=MyEncoder, indent=None)

    def reset(self):
        self.confusion_matrix = np.zeros((self.nclass, self.nclass))

    def reset_gpu(self):
        self.confusion_matrix_gpu = torch.zeros((self.nclass, self.nclass), device='cuda')

    def update(self, label_preds, label_trues):
        for label_true, label_pred in zip(label_trues, label_preds):
            self.confusion_matrix += self._fast_hist(label_true.flatten(), label_pred.flatten(), self.nclass)

    def run_eval(self, dets, save_dir, epoch, rank=-1, task_name=''):
        """
        :param dets:
        :param save_dir:
        :param epoch:
        :param rank:
        :return:
        """
        # TODO: 使用多线程加速
        logging.info('Start coco evaluate...')

        pacc, maccs, macc, fwiou, miou, ious = self._get_scores()
        # print(pacc)
        # print(maccs)
        # print(macc)
        # print(fwiou)
        # print(miou)
        # print(ious)
        jsonpath = os.path.join(save_dir, task_name + 'results{}.json'.format(rank))
        self.save_results(dets,jsonpath)
        mAP = (miou + macc)/2
        f = None
        # -----------------------------------------------------
        if rank < 1:
            f = open(os.path.join(save_dir, task_name + 'eval_results.txt'), 'w')
            f.write('COCO Eval:\n')
            f.write(' Mean IOU = {:0.3f}\n'.format(miou))
            f.write(' FW IOU = {:0.3f}\n'.format(fwiou))
            f.write(' Mean accuracy = {:0.3f}\n'.format(macc))
            f.write(' Pixel accuracy = {:0.3f}\n'.format(pacc))
            for i in ious:
                f.write('Class {} Iou = {:0.3f}, Acc = {:0.3f}\n'.format(
                                                                        str(i),
                                                                         ious[i],
                                                                         maccs[i]))
                self.logger.scalar_summary(task_name+'Val_Coco_Class_iou', str(i), ious[i], epoch)
                self.logger.scalar_summary(task_name+'Val_Coco_Class_acc', str(i), maccs[i], epoch)
            self.logger.scalar_summary(task_name+'Val_Coco_mIou_mAcc_av', 'val', mAP, epoch)

        self.reset()
        return mAP


