"""
Create by Chengqi.Lv
2020/4/2
"""
import pycocotools.coco as coco
from pycocotools.cocoeval import COCOeval
import json
import os
import copy
from .Pascal_Evaluation.pascal_eval import DetectionPascal
import numpy as np


class CocoKeypointsEvaluator:
    def __init__(self, dataset, logger):
        self.ann_path = dataset.ann_path
        self.coco = dataset.coco
        self._valid_ids = dataset._valid_ids
        self.logger = logger

    def _to_float(self, x):
        return float("{:.2f}".format(x))

    def convert_eval_format(self, all_bboxes):
        detections = []
        for image_id in all_bboxes:
            for cls_ind in all_bboxes[image_id]:
                category_id = 1
                for dets in all_bboxes[image_id][cls_ind]:
                    bbox = dets[:4]
                    bbox[2] -= bbox[0]
                    bbox[3] -= bbox[1]
                    score = dets[4]
                    bbox_out = list(map(self._to_float, bbox))
                    # keypoint_score = np.array(dets[39:56], np.float32) * np.array(np.array(dets[39:56]) > 0.1).astype(
                    #     np.float32)  # chanded by zgt 20200319
                    # keypoint_score = keypoint_score.reshape(17, 1)
                    keypoints = np.concatenate([
                        np.array(dets[5:39], dtype=np.float32).reshape(-1, 2),
                        np.ones((17, 1), dtype=np.float32)], axis=1).reshape(51).tolist()
                    # keypoints = np.concatenate([
                    #     np.array(dets[5:39], dtype=np.float32).reshape(-1, 2),
                    #     keypoint_score], axis=1).reshape(51).tolist()
                    keypoints = list(map(self._to_float, keypoints))
                    detection = {
                        "image_id": int(image_id),
                        "category_id": int(category_id),
                        "bbox": bbox_out,
                        "score": float("{:.2f}".format(score)),
                        "keypoints": keypoints
                    }
                    detections.append(detection)
        return detections

    def save_results(self, results, save_dir):
        json.dump(self.convert_eval_format(results), open(save_dir, 'w'))

    def run_eval(self, results, save_dir, epoch, rank=-1, task_name=''):
        json_path = os.path.join(save_dir, task_name+'results{}.json'.format(rank))
        self.save_results(results, json_path)
        coco_dets = self.coco.loadRes(json_path)
        coco_eval = COCOeval(copy.deepcopy(self.coco), copy.deepcopy(coco_dets), "bbox")
        coco_eval.evaluate()
        coco_eval.accumulate()
        coco_eval.summarize()

        coco_keypoints_eval1 = COCOeval(self.coco, coco_dets, "keypoints")
        coco_keypoints_eval1.evaluate()
        coco_keypoints_eval1.accumulate()
        coco_keypoints_eval1.summarize()
        stats = coco_eval.stats
        f = None
        # -----------------------------------------------------
        if rank < 1:
            f = open(os.path.join(save_dir, task_name+'eval_results.txt'), 'w')
            f.write('COCO Eval:\n')
            stats = coco_eval.stats
            f.write(' Average Precision  (AP) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = {:0.3f}\n'.format(stats[0]))
            f.write(' Average Precision  (AP) @[ IoU=0.50      | area=   all | maxDets=100 ] = {:0.3f}\n'.format(stats[1]))
            f.write(' Average Precision  (AP) @[ IoU=0.75      | area=   all | maxDets=100 ] = {:0.3f}\n'.format(stats[2]))
            f.write(' Average Precision  (AP) @[ IoU=0.50:0.95 | area= small | maxDets=100 ] = {:0.3f}\n'.format(stats[3]))
            f.write(' Average Precision  (AP) @[ IoU=0.50:0.95 | area=medium | maxDets=100 ] = {:0.3f}\n'.format(stats[4]))
            f.write(' Average Precision  (AP) @[ IoU=0.50:0.95 | area= large | maxDets=100 ] = {:0.3f}\n'.format(stats[5]))
            f.write(' Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=  1 ] = {:0.3f}\n'.format(stats[6]))
            f.write(' Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets= 10 ] = {:0.3f}\n'.format(stats[7]))
            f.write(' Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = {:0.3f}\n'.format(stats[8]))
            f.write(' Average Recall     (AR) @[ IoU=0.50:0.95 | area= small | maxDets=100 ] = {:0.3f}\n'.format(stats[9]))
            f.write(' Average Recall     (AR) @[ IoU=0.50:0.95 | area=medium | maxDets=100 ] = {:0.3f}\n'.format(stats[10]))
            f.write(' Average Recall     (AR) @[ IoU=0.50:0.95 | area= large | maxDets=100 ] = {:0.3f}\n'.format(stats[11]))
            self.logger.scalar_summary(task_name+'Val_Coco_mAP', 'val', stats[0], epoch)

            f.write('COCO Eval keypoints:\n')
            stats_keypoints = coco_keypoints_eval1.stats

            f.write(' Average Precision  (AP) @[ IoU=0.50:0.95 | area=   all | maxDets=20 ] = {:0.3f}\n'.format(
                stats_keypoints[0]))
            f.write(' Average Precision  (AP) @[ IoU=0.50      | area=   all | maxDets=20 ] = {:0.3f}\n'.format(
                stats_keypoints[1]))
            f.write(' Average Precision  (AP) @[ IoU=0.75      | area=   all | maxDets=20 ] = {:0.3f}\n'.format(
                stats_keypoints[2]))
            f.write(' Average Precision  (AP) @[ IoU=0.50:0.95 | area=medium | maxDets=20 ] = {:0.3f}\n'.format(
                stats_keypoints[3]))
            f.write(' Average Precision  (AP) @[ IoU=0.50:0.95 | area= large | maxDets=20 ] = {:0.3f}\n'.format(
                stats_keypoints[4]))
            f.write(' Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=20 ] = {:0.3f}\n'.format(
                stats_keypoints[5]))
            f.write(' Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=20 ] = {:0.3f}\n'.format(
                stats_keypoints[6]))
            f.write(' Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=20 ] = {:0.3f}\n'.format(
                stats_keypoints[7]))
            f.write(' Average Recall     (AR) @[ IoU=0.50:0.95 | area=medium | maxDets=20 ] = {:0.3f}\n'.format(
                stats_keypoints[8]))
            f.write(' Average Recall     (AR) @[ IoU=0.50:0.95 | area= large | maxDets=20 ] = {:0.3f}\n'.format(
                stats_keypoints[9]))
            self.logger.scalar_summary('Val_Coco_Keypoints_mAP', 'val', stats_keypoints[0], epoch)

        # TODO: 加入pascal eval
        evaluator = DetectionPascal(self.ann_path, json_path)
        pascal_metrics = evaluator.evaluate(f)
        if rank < 1:
            for metrics_con in pascal_metrics:
                if 'AP' in metrics_con[0]:
                    self.logger.scalar_summary(task_name+'Val_Pascal_AP/{}'.format(metrics_con[0].replace('@', '_')),
                                               'val', metrics_con[1], epoch)
                elif 'Precision' in metrics_con[0]:
                    self.logger.scalar_summary(task_name+'Val_Pascal_PrecisionRecall_0.4IOU/{}'.format(metrics_con[0].split('/')[-1]),
                                               'Precision', metrics_con[1], epoch)
                elif 'Recall' in metrics_con[0]:
                    self.logger.scalar_summary(task_name+'Val_Pascal_PrecisionRecall_0.4IOU/{}'.format(metrics_con[0].split('/')[-1]),
                                               'Recall', metrics_con[1], epoch)
            f.close()
        return stats[0]
