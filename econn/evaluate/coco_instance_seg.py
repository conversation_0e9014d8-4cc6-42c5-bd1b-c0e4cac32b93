"""
Create by Chengqi.Lv
2020/5/13
"""

import cv2
import numpy as np
import json
import os
import copy
import logging
from tqdm import tqdm
import pycocotools.coco as coco
from pycocotools.cocoeval import COCOeval
import pycocotools.mask as mask_util


class CocoInstanceEvaluator:
    # TODO:参考detectron，将bbox和分割的eval合并
    def __init__(self, dataset, logger):
        self.coco = dataset.coco
        self._valid_ids = dataset._valid_ids
        self.logger = logger

    def convert_det_format(self, all_dets):
        # TODO: 使用多线程加速
        logging.info('Start converting data to coco format...')
        results = []
        for image_id in tqdm(all_dets):
            for det in all_dets[image_id]:
                cls_ind = det['label']
                category_id = self._valid_ids[cls_ind - 1]
                score = det['score']
                rle = det['rle']
                rle["counts"] = rle["counts"].decode("utf-8")

                result = {
                    "image_id": int(image_id),
                    "category_id": int(category_id),
                    "score": float("{:.2f}".format(score)),
                    "segmentation": rle
                }
                results.append(result)
        logging.info('OK!')
        return results

    def save_results(self, results, save_dir):
        json.dump(self.convert_det_format(results), open(save_dir, 'w'))

    def run_eval(self, dets, save_dir, epoch, rank=-1, task_name=''):
        """
        :param dets:
        :param save_dir:
        :param epoch:
        :param rank:
        :return:
        """
        json_path = os.path.join(save_dir, task_name+'results{}.json'.format(rank))
        self.save_results(dets, json_path)
        coco_dets = self.coco.loadRes(json_path)
        coco_eval = COCOeval(copy.deepcopy(self.coco), copy.deepcopy(coco_dets), "segm")
        coco_eval.evaluate()
        coco_eval.accumulate()
        coco_eval.summarize()

        stats = coco_eval.stats
        f = None
        # -----------------------------------------------------
        if rank < 1:
            f = open(os.path.join(save_dir, task_name+'eval_results.txt'), 'w')
            f.write('COCO Eval:\n')
            stats = coco_eval.stats
            f.write(' Average Precision  (AP) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = {:0.3f}\n'.format(stats[0]))
            f.write(' Average Precision  (AP) @[ IoU=0.50      | area=   all | maxDets=100 ] = {:0.3f}\n'.format(stats[1]))
            f.write(' Average Precision  (AP) @[ IoU=0.75      | area=   all | maxDets=100 ] = {:0.3f}\n'.format(stats[2]))
            f.write(' Average Precision  (AP) @[ IoU=0.50:0.95 | area= small | maxDets=100 ] = {:0.3f}\n'.format(stats[3]))
            f.write(' Average Precision  (AP) @[ IoU=0.50:0.95 | area=medium | maxDets=100 ] = {:0.3f}\n'.format(stats[4]))
            f.write(' Average Precision  (AP) @[ IoU=0.50:0.95 | area= large | maxDets=100 ] = {:0.3f}\n'.format(stats[5]))
            f.write(' Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=  1 ] = {:0.3f}\n'.format(stats[6]))
            f.write(' Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets= 10 ] = {:0.3f}\n'.format(stats[7]))
            f.write(' Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = {:0.3f}\n'.format(stats[8]))
            f.write(' Average Recall     (AR) @[ IoU=0.50:0.95 | area= small | maxDets=100 ] = {:0.3f}\n'.format(stats[9]))
            f.write(' Average Recall     (AR) @[ IoU=0.50:0.95 | area=medium | maxDets=100 ] = {:0.3f}\n'.format(stats[10]))
            f.write(' Average Recall     (AR) @[ IoU=0.50:0.95 | area= large | maxDets=100 ] = {:0.3f}\n'.format(stats[11]))
            self.logger.scalar_summary(task_name+'Val_Coco_mAP', 'val', stats[0], epoch)

        return stats[0]

