"""
Create by Chengqi.Lv
2020/4/2
"""
import logging
from econn.models.taskheads.anchor.anchor_target import anchor_inside_flags
from numpy import average
from numpy.matrixlib.defmatrix import matrix
import pycocotools.coco as coco
from pycocotools.cocoeval import COCOeval
import json
import os
import copy
import numpy as np
import torch
from torch.nn.modules.loss import Margin<PERSON><PERSON><PERSON><PERSON><PERSON>
from torch.utils.data import dataset
from .Pascal_Evaluation.pascal_eval import DetectionPascal
from sklearn.metrics import confusion_matrix,average_precision_score,precision_score,recall_score,precision_recall_curve
import matplotlib.pyplot as plt

class LinePointsEvaluator:
    def __init__(self, dataset, logger):
        self.ann_path = dataset.ann_path
        self.val_dataset=dataset
        self.logger = logger
        self.anchor_strid=[8, 16, 32]

    def _to_float(self, x):
        return float("{:.2f}".format(x))

    def save_results(self, results, save_dir):
        json.dump(results, open(save_dir, 'w'))

    def run_eval(self, results, save_dir, epoch, rank=-1, task_name=''):

        res_json_path = os.path.join(save_dir, task_name+'results{}.json'.format(rank))
        #label_path= os.path.join(save_dir, task_name+'results{}.json'.format(rank))
        # if not os.path.exists(label_path):
        #     pass
        self.save_results({"0":0},res_json_path)
        preds_np=[]
        gt_np=[]
        for idx in range(len(self.val_dataset)):
            img_info=self.val_dataset.get_train_data(idx)
            pred=results[idx]
            fmap_size=[x.shape for x in pred]
            gt_lines_points=np.array(img_info["lines_points"])
            g_labels=self.get_labels(fmap_size,self.anchor_strid,gt_lines_points)
            preds_np.append(pred[0].reshape(-1))
            gt_np.append(g_labels[0].reshape(-1))
            
        preds_np=np.array(preds_np).reshape(-1)
        gt_np=np.array(gt_np).reshape(-1).astype(np.int)
        pred_03=preds_np>0.3
        pred_04=preds_np>0.4
        pred_05=preds_np>0.5
        pred_06=preds_np>0.6
        pred_07=preds_np>0.7
        pred_08=preds_np>0.8
        map=average_precision_score(gt_np,preds_np)
        precision, recall, thresholds=precision_recall_curve(gt_np,preds_np)
        plt.plot(recall,precision)
        plt.savefig(os.path.join(save_dir, task_name+'PR{}.png'.format(rank)))
        #plt.close()
        r3=recall_score(gt_np,pred_03)
        r4=recall_score(gt_np,pred_04)
        r5=recall_score(gt_np,pred_05)
        r6=recall_score(gt_np,pred_06)
        r7=recall_score(gt_np,pred_07)
        r8=recall_score(gt_np,pred_08)

        p3=precision_score(gt_np,pred_03)
        p4=precision_score(gt_np,pred_04)
        p5=precision_score(gt_np,pred_05)
        p6=precision_score(gt_np,pred_06)
        p7=precision_score(gt_np,pred_07)
        p8=precision_score(gt_np,pred_08)
        
        f = None
        # -----------------------------------------------------
        if rank < 1:
            f = open(os.path.join(save_dir, task_name+'eval_results.txt'), 'w')
            f.write('LinePoints Eval:\n')
            #stats = coco_eval.stats
            f.write(' Average Precision  (AP)       @| score=   all ] = {:0.3f}\n'.format(map))

            f.write(' Average Precision  (P03)      @| score=   03  ] = {:0.3f}\n'.format(p3))
            f.write(' Average Precision  (P04)      @| score=   04  ] = {:0.3f}\n'.format(p4))
            f.write(' Average Precision  (P05)      @| score=   05  ] = {:0.3f}\n'.format(p5))
            f.write(' Average Precision  (P06)      @| score=   06  ] = {:0.3f}\n'.format(p6))
            f.write(' Average Precision  (P07)      @| score=   07  ] = {:0.3f}\n'.format(p7))
            f.write(' Average Precision  (P08)      @| score=   08  ] = {:0.3f}\n'.format(p8))

            f.write(' Average Recall     (R03)      @| score=   03  ] = {:0.3f}\n'.format(r3))
            f.write(' Average Recall     (R04)      @| score=   04  ] = {:0.3f}\n'.format(r4))
            f.write(' Average Recall     (R05)      @| score=   05  ] = {:0.3f}\n'.format(r5))
            f.write(' Average Recall     (R06)      @| score=   06  ] = {:0.3f}\n'.format(r6))
            f.write(' Average Recall     (R07)      @| score=   07  ] = {:0.3f}\n'.format(r7))
            f.write(' Average Recall     (R08)      @| score=   08  ] = {:0.3f}\n'.format(r8))

            self.logger.scalar_summary(task_name+'LinePoints map', 'val', map, epoch)
            self.logger.scalar_summary(task_name+'LinePoints P03', 'val', p3, epoch)
            self.logger.scalar_summary(task_name+'LinePoints P04', 'val', p4, epoch)
            self.logger.scalar_summary(task_name+'LinePoints P05', 'val', p5, epoch)
            self.logger.scalar_summary(task_name+'LinePoints P06', 'val', p6, epoch)
            self.logger.scalar_summary(task_name+'LinePoints P07', 'val', p7, epoch)
            self.logger.scalar_summary(task_name+'LinePoints P08', 'val', p8, epoch)

            self.logger.scalar_summary(task_name+'LinePoints R03', 'val', r3, epoch)
            self.logger.scalar_summary(task_name+'LinePoints R04', 'val', r4, epoch)
            self.logger.scalar_summary(task_name+'LinePoints R05', 'val', r5, epoch)
            self.logger.scalar_summary(task_name+'LinePoints R06', 'val', r6, epoch)
            self.logger.scalar_summary(task_name+'LinePoints R07', 'val', r7, epoch)
            self.logger.scalar_summary(task_name+'LinePoints R08', 'val', r8, epoch)
        # TODO: 加入pascal eval
            f.close()
        logging.info(' Average Precision  (AP)       @| score=   all  ] = {:0.3f}\n'.format(map))
        logging.info(' Average Precision  (P03)      @| score=   03  ] = {:0.3f}\n'.format(p3))
        logging.info(' Average Precision  (P04)      @| score=   04  ] = {:0.3f}\n'.format(p4))
        logging.info(' Average Precision  (P05)      @| score=   05  ] = {:0.3f}\n'.format(p5))
        logging.info(' Average Precision  (P06)      @| score=   06  ] = {:0.3f}\n'.format(p6))
        logging.info(' Average Precision  (P07)      @| score=   07  ] = {:0.3f}\n'.format(p7))
        logging.info(' Average Precision  (P08)      @| score=   08  ] = {:0.3f}\n'.format(p8))
        
        logging.info(' Average Recall     (R03)      @| score=   03  ] = {:0.3f}\n'.format(r3))
        logging.info(' Average Recall     (R04)      @| score=   04  ] = {:0.3f}\n'.format(r4))
        logging.info(' Average Recall     (R05)      @| score=   05  ] = {:0.3f}\n'.format(r5))
        logging.info(' Average Recall     (R06)      @| score=   06  ] = {:0.3f}\n'.format(r6))
        logging.info(' Average Recall     (R07)      @| score=   07  ] = {:0.3f}\n'.format(r7))
        logging.info(' Average Recall     (R08)      @| score=   08  ] = {:0.3f}\n'.format(r8))
        return map

    def matrix_count(self,preds,gt_points,conf):
        #tp_c,tn_c,fp_c,fn_c=0,0,0,0
        tmp_pred=np.zeros_like(preds)
        tmp_pred[np.where(preds>conf)]=1
        gt_points=gt_points.reshape(-1)
        tmp_pred=tmp_pred.reshape(-1)
        tn, fp, fn, tp=confusion_matrix(gt_points,tmp_pred).ravel()
        return [tn, fp, fn, tp] 

    def get_labels(self, fmap_size, anchor_strides, gt_lines_points):
        grid_labels = [np.zeros(size) for size in fmap_size]
        fmap_points_index = [np.unique(np.floor(gt_lines_points/stride), axis=0).astype(np.int)  for stride in anchor_strides]
        for fmap_labels,points_index in zip(grid_labels,fmap_points_index):
            b_index=(np.ones(len(points_index))*0).astype(np.int)
            c_index=(np.ones(len(points_index))*0).astype(np.int)
            w_index=points_index[:,0]
            h_index=points_index[:,1]
            fmap_labels[b_index,c_index,h_index,w_index]=1
        return grid_labels

class Avearager:
    def __init__(self,confs_size):
        
        self.TP=np.zeros(confs_size)
        self.FP=np.zeros(confs_size)
        self.TN=np.zeros(confs_size)
        self.FN=np.zeros(confs_size)

    def update(self,matrix_items):
        matrix_items=np.array(matrix_items)
        self.TP=self.TP+matrix_items[:,3]
        self.FP=self.FP+matrix_items[:,1] 
        self.TN=self.TN+matrix_items[:,0]
        self.FN=self.FN+matrix_items[:,2]
    def calculate(self):
        P=((self.TP+1e-16)/(self.FP+self.TP+1e-16))[1:99]
        R=((self.TP+1e-16)/(self.TP+self.FN))[1:99]
        #F1_score=2*P/(P+R)
        #ACC=(self.TP+self.TN)/(self.TP+self.FP+self.TN+self.FN)
        ap=voc_ap(R,P)
        return ap,P[48],R[48]

def voc_ap(rec, prec, use_07_metric=False):
    """ ap = voc_ap(rec, prec, [use_07_metric])
    Compute VOC AP given precision and recall.
    If use_07_metric is true, uses the
    VOC 07 11 point method (default:False).
    """
    # 针对2007年VOC，使用的11个点计算AP，现在不使用
    if use_07_metric:
        # 11 point metric
        ap = 0.
        for t in np.arange(0., 1.1, 0.1):
            if np.sum(rec >= t) == 0:
                p = 0
            else:
                p = np.max(prec[rec >= t])
            ap = ap + p / 11.
    else:
        # correct AP calculation
        # first append sentinel values at the end
        mrec = np.concatenate(([0.], rec, [1.]))  #[0.  0.0666, 0.1333, 0.4   , 0.4666,  1.]
        mpre = np.concatenate(([0.], prec, [0.])) #[0.  1.,     0.6666, 0.4285, 0.3043,  0.]
 
        # compute the precision envelope
        # 计算出precision的各个断点(折线点)
        for i in range(mpre.size - 1, 0, -1):
            mpre[i - 1] = np.maximum(mpre[i - 1], mpre[i])  #[1.     1.     0.6666 0.4285 0.3043 0.    ]
 
        # to calculate area under PR curve, look for points
        # where X axis (recall) changes value
        i = np.where(mrec[1:] != mrec[:-1])[0]  #precision前后两个值不一样的点
        #print(mrec[1:], mrec[:-1])
        #print(i) #[0, 1, 3, 4, 5]
 
        # AP= AP1 + AP2+ AP3+ AP4
        ap = np.sum((mrec[i + 1] - mrec[i]) * mpre[i + 1])
    return ap