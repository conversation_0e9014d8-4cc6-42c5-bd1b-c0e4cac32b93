"""
Create by Chengqi.Lv
2020/4/9
"""
from .coco_bbox_eval import CocoBBoxEvaluator
from .coco_keypoints_eval import CocoKeypointsEvaluator
from .coco_instance_seg import CocoInstanceEvaluator
from .coco_semantic_eval import  CocoStuffEvaluator
from .line_points_eval import LinePointsEvaluator


def build_evaluator(val_type, val_dataset, logger):
    if val_type == 'coco_bbox':
        evaluator = CocoBBoxEvaluator(val_dataset, logger)
    elif val_type == 'coco_keypoints':
        evaluator = CocoKeypointsEvaluator(val_dataset, logger)
    elif val_type == 'coco_instance':
        evaluator = CocoInstanceEvaluator(val_dataset, logger)
    elif val_type == 'coco_stuff':
        evaluator = CocoStuffEvaluator(val_dataset,logger)
    elif val_type=="line_point":
        evaluator=LinePointsEvaluator(val_dataset,logger)
    else:
        raise NotImplementedError
    return evaluator
