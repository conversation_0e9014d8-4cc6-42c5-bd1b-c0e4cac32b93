import numpy as np
import json
import matplotlib.pyplot as plt
from econn.evaluate.Pascal_Evaluation.utils.object_detection_evaluation import PascalDetectionEvaluator


class Information(object):
    def __init__(self, gt, results):
        self.gt = gt
        self.results = results

        self.groundtruth_boxes_dict = {image['id']: [] for image in self.gt["images"]}
        self.groundtruth_classes_dict = {image['id']: [] for image in self.gt["images"]}
        self.groundtruth_difficult_dict = {image['id']: [] for image in self.gt["images"]}

        self.detected_boxes_dict = {image['id']: [] for image in self.gt["images"]}
        self.detected_classes_dict = {image['id']: [] for image in self.gt["images"]}
        self.detected_scores_dict = {image['id']: [] for image in self.gt["images"]}

        self.load_detection_info()
        self.load_groundtruth_info()

    def load_groundtruth_info(self):
        annotations = self.gt["annotations"]
        for annotation in annotations:
            image_id = annotation['image_id']
            bbox = annotation['bbox']  # (x, y, width, height)
            category_id = annotation['category_id']

            # (ymin, ymax, xmin, xmax)
            box = [bbox[1], bbox[0], bbox[1]+bbox[3], bbox[0]+bbox[2]]
            self.groundtruth_boxes_dict[image_id].append(box)
            self.groundtruth_classes_dict[image_id].append(category_id)

        for key in self.groundtruth_boxes_dict:
            self.groundtruth_boxes_dict[key] = np.array(self.groundtruth_boxes_dict[key], dtype=np.float32)
        for key in self.groundtruth_classes_dict:
            self.groundtruth_classes_dict[key] = np.array(self.groundtruth_classes_dict[key])
        for key in self.groundtruth_difficult_dict:
            self.groundtruth_difficult_dict[key] = np.array(self.groundtruth_difficult_dict[key])

    def load_detection_info(self):

        for result in self.results:
            image_id = result['image_id']
            bbox = result['bbox']  # (x, y, width, height)
            category_id = result['category_id']
            score = result['score']

            # (ymin, ymax, xmin, xmax)
            box = [bbox[1], bbox[0], bbox[1] + bbox[3], bbox[0] + bbox[2]]
            self.detected_boxes_dict[image_id].append(box)
            self.detected_classes_dict[image_id].append(category_id)
            self.detected_scores_dict[image_id].append(score)

        for key in self.detected_boxes_dict:
            self.detected_boxes_dict[key] = np.array(self.detected_boxes_dict[key], dtype=np.float32)
        for key in self.detected_classes_dict:
            self.detected_classes_dict[key] = np.array(self.detected_classes_dict[key])
        for key in self.detected_scores_dict:
            self.detected_scores_dict[key] = np.array(self.detected_scores_dict[key], dtype=np.float32)


class DetectionPascal(object):
    def __init__(self, gt_json_path, result_json_path):

        with open(gt_json_path) as f:
            self.gt = json.load(f)
        with open(result_json_path) as f:
            self.result = json.load(f)

        self.image_num = len(self.gt['images'])
        info = Information(self.gt, self.result)
        self.category_id_map = {item['id']: item['name'] for item in self.gt["categories"]}
        category_list = [{"id": item['id'], "name": item['name']} for item in self.gt["categories"]]

        self.evaluator = PascalDetectionEvaluator(category_list)
        self.mAP = 0
        for image in self.gt["images"]:
            groundtruth_boxes = info.groundtruth_boxes_dict[image['id']]
            groundtruth_classes = info.groundtruth_classes_dict[image['id']]
            groundtruth_difficult = info.groundtruth_difficult_dict[image['id']]
            groundtruth_dict = {
                "groundtruth_boxes": groundtruth_boxes,
                "groundtruth_classes": groundtruth_classes,
                "groundtruth_difficult": groundtruth_difficult
            }
            self.evaluator.add_single_ground_truth_image_info(image['id'], groundtruth_dict)

        for image in self.gt["images"]:
            detected_boxes = info.detected_boxes_dict[image['id']]
            detected_classes = info.detected_classes_dict[image['id']]
            detected_scores = info.detected_scores_dict[image['id']]
            detections_dict = {
                "detection_boxes": detected_boxes,
                "detection_classes": detected_classes,
                "detection_scores": detected_scores
            }
            # TODO: find out why results with no det are still saved in json?????
            if detected_boxes.size:
                self.evaluator.add_single_detected_image_info(image['id'], detections_dict)

    def evaluate(self, f=None):
        metrics = self.evaluator.evaluate()
        metrics_write = [(k, metrics[k]) for k in sorted(metrics.keys())]
        print("*"*50)
        for metrics_con in metrics_write:
            print(metrics_con)
            if f is not None:
                f.write('{}={:0.3f}\n'.format(metrics_con[0], metrics_con[1]))
        self.mAP = metrics_write[-1][1]
        return metrics_write


if __name__ == '__main__':
    gt_json_path = r"/home/<USER>/Projects/eco_centernet/data/eco_indoor/annotations/ecov5.4_instances_val2019.json"
    result_json_path = r"/home/<USER>/Projects/econn/workspace/ecoindoor/GFL_test_20200824/results.json"
    evaluator = DetectionPascal(gt_json_path, result_json_path)
    evaluator.evaluate()
